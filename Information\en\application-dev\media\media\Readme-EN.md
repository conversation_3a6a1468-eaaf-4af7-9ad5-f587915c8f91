# Media Kit

- [Introduction to Media Kit](media-kit-intro.md)
- [Media Development Preparations](media-preparation.md)
- Media Development (ArkTS)<!--media-kit-dev--arkts-->
  - Playback<!--media-playback-arkts-->
    - [Using AVPlayer to Play Audio (ArkTS)](using-avplayer-for-playback.md)
    - [Using AVPlayer to Play Videos (ArkTS)](video-playback.md)
    - [Using AVPlayer to Play Streaming Media (ArkTS)](streaming-media-playback-development-guide.md)
    - [Using AVPlayer to Add External Subtitles to Videos (ArkTS)](video-subtitle.md)
    - [Using SoundPool to Play Short Sounds (ArkTS)](using-soundpool-for-playback.md)
  - Recording<!--media-recording-arkts-->
    - [Using AVRecorder to Record Audio (ArkTS)](using-avrecorder-for-recording.md)
    - [Using AVRecorder to Record Videos (ArkTS)](video-recording.md)
    - [Using AVScreenCaptureRecorder to Capture Screens and Write Them to Files (ArkTS)](using-avscreencapture-ArkTs.md)
  - Media Information Query<!--media-info-arkts-->
    - [Using AVMetadataExtractor to Extract Audio and Video Metadata (ArkTS)](avmetadataextractor.md)
    - [Using AVImageGenerator to Extract Video Images at a Specified Time (ArkTS)](avimagegenerator.md)
  - Video Transcoding<!--media-transcoder-arkts-->
    - [Using AVTranscoder to Transcode Videos (ArkTS)](using-avtranscoder-for-transcodering.md)
- Media Development (C/C++)<!--media-kit-dev--c-->
  - Playback<!--media-playback-c-->
    - [Using AVPlayer to Play Audio (C/C++)](using-ndk-avplayer-for-playerback.md)
  - Screen Capture<!--media-recording-c-->
    - [Using AVScreenCapture to Capture Screens and Obtain Streams (C/C++)](using-avscreencapture-for-buffer.md)
    - [Using AVScreenCapture to Capture Screens and Write Them to Files (C/C++)](using-avscreencapture-for-file.md)
