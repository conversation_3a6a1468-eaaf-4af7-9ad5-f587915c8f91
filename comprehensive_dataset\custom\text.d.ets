/**
 * Provides a text component.
 * @component Text
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
@Component
export struct Text {
  /**
   * Creates a text component.
   * @param content Text content.
   */
  constructor(content?: string);

  /**
   * Sets the text content.
   * @param content Text content.
   * @returns This Text component.
   */
  content(content: string): Text;

  /**
   * Sets the text font size.
   * @param size Text font size.
   * @returns This Text component.
   */
  fontSize(size: number | string): Text;

  /**
   * Sets the text font weight.
   * @param weight Text font weight.
   * @returns This Text component.
   */
  fontWeight(weight: number | FontWeight): Text;

  /**
   * Sets the text font family.
   * @param family Text font family.
   * @returns This Text component.
   */
  fontFamily(family: string): Text;

  /**
   * Sets the text font style.
   * @param style Text font style.
   * @returns This Text component.
   */
  fontStyle(style: FontStyle): Text;

  /**
   * Sets the text color.
   * @param color Text color.
   * @returns This Text component.
   */
  fontColor(color: string): Text;

  /**
   * Sets the text alignment.
   * @param alignment Text alignment.
   * @returns This Text component.
   */
  textAlign(alignment: TextAlign): Text;

  /**
   * Sets the text line height.
   * @param height Text line height.
   * @returns This Text component.
   */
  lineHeight(height: number | string): Text;

  /**
   * Sets the text decoration.
   * @param decoration Text decoration.
   * @returns This Text component.
   */
  decoration(decoration: TextDecoration): Text;

  /**
   * Sets the text overflow handling.
   * @param overflow Text overflow handling.
   * @returns This Text component.
   */
  textOverflow(overflow: TextOverflow): Text;

  /**
   * Sets the maximum number of lines.
   * @param lines Maximum number of lines.
   * @returns This Text component.
   */
  maxLines(lines: number): Text;
}

/**
 * Enum for font weight.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum FontWeight {
  /**
   * Lighter font weight.
   */
  Lighter = 100,

  /**
   * Normal font weight.
   */
  Normal = 400,

  /**
   * Bold font weight.
   */
  Bold = 700,

  /**
   * Bolder font weight.
   */
  Bolder = 900
}

/**
 * Enum for font style.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum FontStyle {
  /**
   * Normal font style.
   */
  Normal,

  /**
   * Italic font style.
   */
  Italic
}

/**
 * Enum for text alignment.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum TextAlign {
  /**
   * Start alignment.
   */
  Start,

  /**
   * Center alignment.
   */
  Center,

  /**
   * End alignment.
   */
  End
}

/**
 * Enum for text decoration.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum TextDecoration {
  /**
   * No decoration.
   */
  None,

  /**
   * Underline decoration.
   */
  Underline,

  /**
   * Line-through decoration.
   */
  LineThrough,

  /**
   * Overline decoration.
   */
  Overline
}

/**
 * Enum for text overflow handling.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum TextOverflow {
  /**
   * Clip overflow text.
   */
  Clip,

  /**
   * Show ellipsis for overflow text.
   */
  Ellipsis,

  /**
   * Show marquee for overflow text.
   */
  Marquee
}

export { Text, FontWeight, FontStyle, TextAlign, TextDecoration, TextOverflow };
