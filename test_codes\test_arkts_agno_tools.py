"""
Test ArkTS Agno Tools

This module tests the ArkTS Import Tools for Agno Agents.
"""

import unittest
import os
import sys
import json
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the modules to test
from arkts_query_cached import Ark<PERSON><PERSON>ueryCached
from arkts_query_enhanced import ArkTSQueryEnhanced
from arkts_formatter import ArkTSFormatter
from arkts_agno_tools import ArkTSImportTools


class TestArkTSAgnoTools(unittest.TestCase):
    """Test case for ArkTS Agno Tools."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock the ArkTSQueryAsync class
        self.patcher = patch('arkts_agno_tools.ArkTSQueryAsync')
        self.mock_query_class = self.patcher.start()
        self.mock_query = MagicMock()
        self.mock_query_class.return_value = self.mock_query

        # Create sample results
        self.sample_results = [
            {
                'symbol_name': 'Button',
                'symbol_type': 'Component',
                'module_name': '@kit.ArkUI',
                'import_statement': 'import { Button } from \'@kit.ArkUI\';',
                'description': 'A clickable button component that responds to user interactions.',
                'score': 0.9876
            },
            {
                'symbol_name': 'TextButton',
                'symbol_type': 'Component',
                'module_name': '@kit.ArkUI',
                'import_statement': 'import { TextButton } from \'@kit.ArkUI\';',
                'description': 'A text-only button variant with minimal styling.',
                'score': 0.8765
            }
        ]

        # Create mock coroutines
        async def mock_coro(return_value):
            return return_value

        # Set up mock return values
        self.mock_query.search_component_async.return_value = mock_coro(self.sample_results)
        self.mock_query.search_import_path_async.return_value = mock_coro(self.sample_results)
        self.mock_query.suggest_imports_async.return_value = mock_coro(self.sample_results)
        self.mock_query.handle_agent_query_async.return_value = mock_coro(self.sample_results)

        # Create the toolkit
        self.toolkit = ArkTSImportTools(
            qdrant_url='http://localhost:6333',
            collection_name='arkts',
            ollama_url='http://localhost:11434',
            embedding_model='llama2'
        )

    def tearDown(self):
        """Tear down test fixtures."""
        self.patcher.stop()

    def test_search_component(self):
        """Test search_component method."""
        result = self.toolkit.search_component('Button', 5)

        # Check that the query was called with correct parameters
        self.mock_query.search_component_async.assert_called_once_with('Button', 5)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the component name
        self.assertIn('Button', result)

        # Check that the result is formatted as expected
        self.assertIn('# Component Search Results', result)

    def test_search_import_path(self):
        """Test search_import_path method."""
        result = self.toolkit.search_import_path('Button', 5)

        # Check that the query was called with correct parameters
        self.mock_query.search_import_path_async.assert_called_once_with('Button', 5)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the symbol name
        self.assertIn('Button', result)

        # Check that the result is formatted as expected
        self.assertIn('# Import Path Search Results', result)

    def test_search_arkts_api(self):
        """Test search_arkts_api method."""
        result = self.toolkit.search_arkts_api('Button', 5)

        # Check that the query was called with correct parameters
        self.mock_query.suggest_imports_async.assert_called_once_with('Button', 5)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the API name
        self.assertIn('Button', result)

        # Check that the result is formatted as expected
        self.assertIn('# ArkTS Search Results', result)

    def test_handle_agent_query(self):
        """Test handle_agent_query method."""
        result = self.toolkit.handle_agent_query('ArkTS search: \'Button component\'', 5)

        # Check that the query was called with correct parameters
        self.mock_query.handle_agent_query_async.assert_called_once_with('ArkTS search: \'Button component\'', 5)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the component name
        self.assertIn('Button', result)

    def test_error_handling(self):
        """Test error handling."""
        # Make the query raise an exception
        self.mock_query.search_component_async.side_effect = Exception('Test error')

        # Call the method
        result = self.toolkit.search_component('Button', 5)

        # Check that the result contains an error message
        self.assertIn('Error', result)
        self.assertIn('Test error', result)

    def test_run_async(self):
        """Test _run_async method."""
        # Create a simple coroutine
        async def test_coro():
            return "test_result"

        # Run the coroutine
        result = self.toolkit._run_async(test_coro())

        # Check the result
        self.assertEqual(result, "test_result")


class TestArkTSFormatter(unittest.TestCase):
    """Test case for ArkTSFormatter."""

    def setUp(self):
        """Set up test fixtures."""
        self.formatter = ArkTSFormatter(max_description_length=100, include_scores=True)

        # Create sample results
        self.component_results = [
            {
                'symbol_name': 'Button',
                'symbol_type': 'Component',
                'module_name': '@kit.ArkUI',
                'import_statement': 'import { Button } from \'@kit.ArkUI\';',
                'description': 'A clickable button component that responds to user interactions.',
                'score': 0.9876
            }
        ]

        self.import_path_results = [
            {
                'symbol_name': 'Button',
                'symbol_type': 'Component',
                'module_name': '@kit.ArkUI',
                'import_statement': 'import { Button } from \'@kit.ArkUI\';',
                'is_default': False,
                'score': 0.9876
            }
        ]

        self.nested_results = [
            {
                'symbol_name': 'ButtonState',
                'symbol_type': 'Enum',
                'module_name': '@kit.ArkUI',
                'import_statement': 'import { Button } from \'@kit.ArkUI\';',
                'parent_symbol': 'Button',
                'is_nested': True,
                'full_name': 'Button.ButtonState',
                'score': 0.9876
            }
        ]

    def test_format_component_results(self):
        """Test format_component_results method."""
        result = self.formatter.format_component_results(self.component_results)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the component name
        self.assertIn('Button', result)

        # Check that the result contains the import statement
        self.assertIn('import { Button } from', result)

        # Check that the result contains the score
        self.assertIn('0.9876', result)

    def test_format_import_path_results(self):
        """Test format_import_path_results method."""
        result = self.formatter.format_import_path_results(self.import_path_results)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the symbol name
        self.assertIn('Button', result)

        # Check that the result contains the import statement
        self.assertIn('import { Button } from', result)

        # Check that the result contains the score
        self.assertIn('0.9876', result)

    def test_format_nested_results(self):
        """Test format_nested_results method."""
        result = self.formatter.format_nested_results(self.nested_results)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the nested symbol name
        self.assertIn('ButtonState', result)

        # Check that the result contains the parent symbol name
        self.assertIn('Button', result)

        # Check that the result contains the full name
        self.assertIn('Button.ButtonState', result)

        # Check that the result contains the score
        self.assertIn('0.9876', result)

    def test_format_as_json(self):
        """Test format_as_json method."""
        result = self.formatter.format_as_json(self.component_results)

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result can be parsed as JSON
        parsed = json.loads(result)

        # Check that the parsed result is a list
        self.assertIsInstance(parsed, list)

        # Check that the parsed result contains the component
        self.assertEqual(parsed[0]['symbol_name'], 'Button')

    def test_format_as_markdown_table(self):
        """Test format_as_markdown_table method."""
        result = self.formatter.format_as_markdown_table(
            self.component_results,
            fields=['symbol_name', 'symbol_type', 'import_statement', 'score']
        )

        # Check that the result is a string
        self.assertIsInstance(result, str)

        # Check that the result contains the component name
        self.assertIn('Button', result)

        # Check that the result contains the import statement
        self.assertIn('import { Button } from', result)

        # Check that the result contains the score
        self.assertIn('0.9876', result)

        # Check that the result is formatted as a markdown table
        self.assertIn('|', result)
        self.assertIn('---', result)


if __name__ == '__main__':
    unittest.main()
