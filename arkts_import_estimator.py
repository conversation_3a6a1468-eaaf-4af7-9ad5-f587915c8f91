"""
ArkTS Import Estimator

This module provides functionality to extract import statements for ArkTS programming language
by analyzing source code and documentation.

It supports all ArkTS features including:
- Nested imports
- Nested classes
- Nested namespaces
- Reimport
- Reexport
- Type declarations

The system uses Qdrant for vector and hybrid search capabilities and Ollama for embeddings.
"""

import os
import re
import json
import time
import logging
import requests
from typing import List, Dict, Any, Optional, Tuple, Set, Union
import numpy as np
from qdrant_client import QdrantClient
from qdrant_client.http import models
from qdrant_client.http.exceptions import UnexpectedResponse
from component_cards import ArkTSSymbolParser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSImportEstimator")

class ArkTSImportEstimator:
    """ArkTS Import Estimator for suggesting import statements."""

    def __init__(self, qdrant_url: str = "http://gmktec.ai-institute.uk:6333",
                 collection_name: str = "arkts_imports",
                 ollama_url: str = "http://localhost:11434",
                 embedding_model: str = "mxbai-embed-large"):
        """Initialize the estimator.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
        """
        self.qdrant_url = qdrant_url
        self.collection_name = collection_name
        self.ollama_url = ollama_url
        self.embedding_model = embedding_model
        self.parser = ArkTSSymbolParser()

        # Set vector size based on embedding model
        if embedding_model == "mxbai-embed-large":
            self.vector_size = 1024  # mxbai-embed-large produces 1024-dimensional vectors
        elif embedding_model == "nomic-embed-text":
            self.vector_size = 768  # nomic-embed-text produces 768-dimensional vectors
        else:
            self.vector_size = 768  # Default vector size

        # Initialize Qdrant client with compatibility check disabled
        self.client = QdrantClient(url=qdrant_url, check_compatibility=False)

        # Ensure collection exists
        self._ensure_collection_exists()

        logger.info(f"ArkTSImportEstimator initialized with Qdrant at {qdrant_url} and Ollama at {ollama_url}")

    def _get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama.

        Args:
            text: Text to embed

        Returns:
            Embedding vector as a list of floats
        """
        # Maximum retry attempts
        max_retries = 3
        retry_delay = 1  # seconds

        for attempt in range(max_retries):
            try:
                # Call Ollama API to get embedding
                response = requests.post(
                    f"{self.ollama_url}/api/embeddings",
                    json={
                        "model": self.embedding_model,
                        "prompt": text
                    },
                    timeout=30  # Add timeout to prevent hanging
                )

                # Check if request was successful
                if response.status_code == 200:
                    # Extract embedding from response
                    embedding = response.json().get("embedding", [])

                    # Verify embedding dimensions
                    if len(embedding) == self.vector_size:
                        return embedding
                    else:
                        logger.warning(f"Embedding dimension mismatch: expected {self.vector_size}, got {len(embedding)}")
                        # Try to pad or truncate to match expected size
                        if len(embedding) > self.vector_size:
                            logger.warning(f"Truncating embedding from {len(embedding)} to {self.vector_size}")
                            return embedding[:self.vector_size]
                        else:
                            logger.warning(f"Padding embedding from {len(embedding)} to {self.vector_size}")
                            return embedding + [0.0] * (self.vector_size - len(embedding))
                else:
                    logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {response.status_code} {response.text}")

            except requests.exceptions.ConnectionError:
                logger.error(f"Connection error to Ollama server (attempt {attempt+1}/{max_retries}). Is Ollama running?")
            except requests.exceptions.Timeout:
                logger.error(f"Timeout connecting to Ollama server (attempt {attempt+1}/{max_retries})")
            except Exception as e:
                logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {str(e)}")

            # Wait before retrying
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff

        # If all retries failed, return a zero vector
        logger.error(f"All {max_retries} attempts to get embedding failed. Using zero vector.")
        return [0.0] * self.vector_size

    def _ensure_collection_exists(self) -> None:
        """Ensure the Qdrant collection exists, create it if it doesn't."""
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            # Always recreate collection to ensure correct vector size
            if self.collection_name in collection_names:
                logger.info(f"Deleting existing collection {self.collection_name}")
                self.client.delete_collection(collection_name=self.collection_name)

            # Create collection
            logger.info(f"Creating new collection {self.collection_name} with vector size {self.vector_size}")
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=models.VectorParams(
                    size=self.vector_size,
                    distance=models.Distance.COSINE
                )
            )

            # Create payload indices for filtering
            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="symbol_type",
                field_schema=models.PayloadSchemaType.KEYWORD
            )

            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="module_name",
                field_schema=models.PayloadSchemaType.KEYWORD
            )

            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="symbol_name",
                field_schema=models.PayloadSchemaType.TEXT
            )

            self.client.create_payload_index(
                collection_name=self.collection_name,
                field_name="is_nested",
                field_schema=models.PayloadSchemaType.BOOL
            )

            logger.info(f"Created collection {self.collection_name}")

        except Exception as e:
            logger.error(f"Error ensuring collection exists: {str(e)}")
            raise

    def index_file(self, file_path: str) -> int:
        """Index a d.ts or d.ets file.

        Args:
            file_path: Path to the d.ts or d.ets file

        Returns:
            Number of symbols indexed
        """
        try:
            # Parse file
            symbols = self.parser.parse_file(file_path)

            if not symbols:
                logger.warning(f"No symbols found in {file_path}")
                return 0

            # Index symbols
            self._index_symbols(symbols)

            logger.info(f"Indexed {len(symbols)} symbols from {file_path}")
            return len(symbols)

        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {str(e)}")
            return 0

    def _index_symbols(self, symbols: List[Dict[str, Any]]) -> None:
        """Index symbols in Qdrant.

        Args:
            symbols: List of symbols to index
        """
        if not symbols:
            return

        points = []

        for symbol in symbols:
            # Create text for embedding
            if 'full_name' in symbol:
                text_to_embed = f"{symbol['full_name']} {symbol['symbol_type']} {symbol['description']}"
            else:
                text_to_embed = f"{symbol['symbol_name']} {symbol['symbol_type']} {symbol['description']}"

            # Generate embedding using Ollama
            vector = self._get_embedding(text_to_embed)

            # Create unique ID
            id_str = f"{symbol['symbol_name']}:{symbol['module_name']}:{symbol.get('parent_symbol', '')}"
            point_id = abs(hash(id_str)) % (2**63 - 1)  # Positive 64-bit integer

            # Create point
            point = models.PointStruct(
                id=point_id,
                vector=vector,
                payload=symbol
            )

            points.append(point)

        # Batch upsert
        try:
            self.client.upsert(
                collection_name=self.collection_name,
                points=points
            )
        except Exception as e:
            logger.error(f"Error upserting points: {str(e)}")
            # Try inserting points one by one
            for point in points:
                try:
                    self.client.upsert(
                        collection_name=self.collection_name,
                        points=[point]
                    )
                except Exception as e2:
                    logger.error(f"Error upserting point {point.id}: {str(e2)}")

    def index_directory(self, directory_path: str) -> int:
        """Index all d.ts and d.ets files in a directory.

        Args:
            directory_path: Path to the directory

        Returns:
            Number of symbols indexed
        """
        total_symbols = 0

        for root, _, files in os.walk(directory_path):
            for file in files:
                if file.endswith('.d.ts') or file.endswith('.d.ets'):
                    file_path = os.path.join(root, file)
                    symbols_count = self.index_file(file_path)
                    total_symbols += symbols_count

        logger.info(f"Indexed {total_symbols} symbols from {directory_path}")
        return total_symbols

    def suggest_imports(self, query: str, limit: int = 10, use_hybrid: bool = True) -> List[Dict[str, Any]]:
        """Suggest import statements for a query.

        Args:
            query: Query string
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search (vector + text)

        Returns:
            List of import suggestions
        """
        try:
            # Generate query embedding using Ollama
            query_vector = self._get_embedding(query)

            if use_hybrid:
                # Hybrid search (vector + text)
                results = self.client.search(
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        should=[
                            models.FieldCondition(
                                key="symbol_name",
                                match=models.MatchText(text=query)
                            ),
                            models.FieldCondition(
                                key="description",
                                match=models.MatchText(text=query)
                            )
                        ]
                    ),
                    limit=limit
                )
            else:
                # Vector search only
                results = self.client.search(
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    limit=limit
                )

            # Format results
            suggestions = []
            for result in results:
                suggestions.append({
                    'symbol_name': result.payload['symbol_name'],
                    'symbol_type': result.payload['symbol_type'],
                    'module_name': result.payload['module_name'],
                    'import_statement': result.payload['import_statement'],
                    'description': result.payload['description'],
                    'is_nested': result.payload['is_nested'],
                    'parent_symbol': result.payload.get('parent_symbol'),
                    'score': result.score
                })

            return suggestions

        except Exception as e:
            logger.error(f"Error suggesting imports: {str(e)}")
            return []

    def filter_suggestions_by_type(self, query: str, symbol_type: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Filter import suggestions by symbol type.

        Args:
            query: Query string
            symbol_type: Symbol type to filter by
            limit: Maximum number of results

        Returns:
            List of filtered import suggestions
        """
        try:
            # Generate query embedding using Ollama
            query_vector = self._get_embedding(query)

            # Search with type filter
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=query_vector,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="symbol_type",
                            match=models.MatchValue(value=symbol_type)
                        )
                    ]
                ),
                limit=limit
            )

            # Format results
            suggestions = []
            for result in results:
                suggestions.append({
                    'symbol_name': result.payload['symbol_name'],
                    'symbol_type': result.payload['symbol_type'],
                    'module_name': result.payload['module_name'],
                    'import_statement': result.payload['import_statement'],
                    'description': result.payload['description'],
                    'is_nested': result.payload['is_nested'],
                    'parent_symbol': result.payload.get('parent_symbol'),
                    'score': result.score
                })

            return suggestions

        except Exception as e:
            logger.error(f"Error filtering suggestions: {str(e)}")
            return []

    def search_nested_symbols(self, parent_symbol: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for nested symbols within a parent symbol.

        Args:
            parent_symbol: Parent symbol name
            limit: Maximum number of results

        Returns:
            List of nested symbols
        """
        try:
            # Create a dummy vector for search (required by Qdrant)
            dummy_vector = [0.0] * self.vector_size  # Same dimension as our embedding model

            # Search for nested symbols
            results = self.client.search(
                collection_name=self.collection_name,
                query_vector=dummy_vector,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="parent_symbol",
                            match=models.MatchValue(value=parent_symbol)
                        ),
                        models.FieldCondition(
                            key="is_nested",
                            match=models.MatchValue(value=True)
                        )
                    ]
                ),
                limit=limit
            )

            # Format results
            nested_symbols = []
            for result in results:
                nested_symbols.append({
                    'symbol_name': result.payload['symbol_name'],
                    'symbol_type': result.payload['symbol_type'],
                    'module_name': result.payload['module_name'],
                    'import_statement': result.payload['import_statement'],
                    'description': result.payload['description'],
                    'is_nested': result.payload['is_nested'],
                    'parent_symbol': result.payload['parent_symbol'],
                    'full_name': result.payload.get('full_name')
                })

            return nested_symbols

        except Exception as e:
            logger.error(f"Error searching nested symbols: {str(e)}")
            return []


def main():
    """Main function for testing."""
    import argparse

    parser = argparse.ArgumentParser(description='ArkTS Import Estimator')
    parser.add_argument('--qdrant-url', type=str, default='http://gmktec.ai-institute.uk:6333', help='Qdrant server URL')
    parser.add_argument('--collection', type=str, default='arkts_imports', help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, default='http://localhost:11434', help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, default='mxbai-embed-large', help='Ollama embedding model to use')
    parser.add_argument('--index', type=str, help='Directory to index')
    parser.add_argument('--query', type=str, help='Query for import suggestions')
    parser.add_argument('--limit', type=int, default=10, help='Maximum number of results')
    parser.add_argument('--hybrid', action='store_true', help='Use hybrid search')
    parser.add_argument('--type', type=str, help='Filter by symbol type')
    parser.add_argument('--nested', type=str, help='Search for nested symbols within a parent')

    args = parser.parse_args()

    # Initialize estimator
    estimator = ArkTSImportEstimator(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model
    )

    # Index directory
    if args.index:
        total_symbols = estimator.index_directory(args.index)
        print(f"Indexed {total_symbols} symbols from {args.index}")

    # Query for import suggestions
    if args.query:
        if args.type:
            suggestions = estimator.filter_suggestions_by_type(args.query, args.type, args.limit)
            print(f"\nImport suggestions for '{args.query}' (type: {args.type}):")
        else:
            suggestions = estimator.suggest_imports(args.query, args.limit, args.hybrid)
            print(f"\nImport suggestions for '{args.query}' (hybrid: {args.hybrid}):")

        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion['import_statement']} (Score: {suggestion['score']:.4f})")
            print(f"   Type: {suggestion['symbol_type']}, Module: {suggestion['module_name']}")
            if suggestion['description']:
                print(f"   Description: {suggestion['description'][:100]}...")
            if suggestion['is_nested']:
                print(f"   Nested in: {suggestion['parent_symbol']}")
            print()

    # Search for nested symbols
    if args.nested:
        nested_symbols = estimator.search_nested_symbols(args.nested, args.limit)
        print(f"\nNested symbols within '{args.nested}':")

        for i, symbol in enumerate(nested_symbols, 1):
            print(f"{i}. {symbol['symbol_name']} ({symbol['symbol_type']})")
            print(f"   Import: {symbol['import_statement']}")
            if symbol.get('full_name'):
                print(f"   Full name: {symbol['full_name']}")
            if symbol['description']:
                print(f"   Description: {symbol['description'][:100]}...")
            print()


if __name__ == "__main__":
    main()
