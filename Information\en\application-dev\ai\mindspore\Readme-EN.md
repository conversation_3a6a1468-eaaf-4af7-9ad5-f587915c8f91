# MindSpore Lite Kit

- [Introduction to MindSpore Lite Kit](MindSpore-Lite-Kit-Introduction.md)
- [Using MindSpore Lite for Offline Model Conversion and Inference](mindspore-lite-converter-guidelines.md)
- Model Deployment<!--deployment-->
  - [Using MindSpore Lite for Model Inference (C/C++)](mindspore-lite-guidelines.md)
  - [Using the MindSpore Lite Engine for On-Device Training (C/C++)](mindspore-lite-train-guidelines.md)
- [Using MindSpore Lite for Image Classification (ArkTS)](mindspore-guidelines-based-js.md)
- [Using MindSpore Lite for Image Classification (C/C++)](mindspore-guidelines-based-native.md)
