# tab-bar

>  **NOTE**
>
>  This component is supported since API version 4. Updates will be marked with a superscript to indicate their earliest API version.

**\<tab-bar>** is a child component of **[Tabs](js-components-container-tabs.md)** and is used to provide the area to display tab labels. Its child components are horizontally arranged.

## Required Permissions

None


## Child Components

Supported


## Attributes

In addition to the [universal attributes](js-components-common-attributes.md), the following attributes are supported.

| Name| Type| Default Value| Mandatory| Description|
| -------- | -------- | -------- | -------- | -------- |
| mode | string | scrollable | No| Extensibility of the component width. Available values are as follows:<br>- **scrollable**: The width of a child component is the configured width. When the total width of all child components (including the margins) is greater than the tab-bar width, the child components can scroll horizontally.<br>- **fixed**: The width of a child component equals the tab-bar width divided by the number of child components.|


## Styles

The [universal styles](js-components-common-styles.md) are supported.


## Events

The [universal events](js-components-common-events.md) are supported.


## Methods

The [universal methods](js-components-common-methods.md) are supported.


## Example

For details, see [Example in tabs](js-components-container-tabs.md#example).
