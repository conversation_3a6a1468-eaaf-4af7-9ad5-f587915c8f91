# OH_NativeBundle_ElementName


## Overview

The OH_NativeBundle_ElementName struct describes the information about an element name.

This struct is an output parameter of [OH_NativeBundle_GetMainElementName()](_bundle.md#oh_nativebundle_getmainelementname).

**Since**

13

**Related module**

[Bundle](_bundle.md)


## Summary


### Member Variables

| Name| Description|
| -------- | -------- |
| [bundleName](#bundlename) | Bundle name of the application.|
| [moduleName](#modulename) | Module name.|
| [abilityName](#abilityname) | Ability name.|


## Member Variable Description


### bundleName


```
char* OH_NativeBundle_ElementName::bundleName
```

**Description**

Bundle name of the application.

**Since**

13


### moduleName


```
char* OH_NativeBundle_ElementName::moduleName
```

**Description**

Module name.

**Since**

13


### abilityName


```
char* OH_NativeBundle_ElementName::abilityName
```

**Description**

Ability name.

**Since**

13
