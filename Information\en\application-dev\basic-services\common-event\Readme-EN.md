# Common Events

- IPC
    - [Common Event Overview](common-event-overview.md)
    - Common Event Subscription
        <!--Del-->
        - [Common Event Subscription Overview](common-event-subscription-overview.md)
        <!--DelEnd-->
        - [Subscribing to Common Events in Dynamic Mode](common-event-subscription.md)
        <!--Del-->
        - [Subscribing to Common Events in Static Mode (for System Applications Only)](common-event-static-subscription.md)
        <!--DelEnd-->
        - [Unsubscribing from Common Events in Dynamic Mode](common-event-unsubscription.md)
    - [Publishing Common Events](common-event-publish.md)
    <!--Del-->
    - [Removing Sticky Common Events (for System Applications Only)](common-event-remove-sticky.md)
    <!--DelEnd-->
- Inter-Thread Communication (FA Model)
    - [Using Emitter for Inter-Thread Communication](itc-with-emitter.md)
