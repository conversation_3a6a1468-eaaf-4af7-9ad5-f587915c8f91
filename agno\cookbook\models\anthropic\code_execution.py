from agno.agent import Agent
from agno.models.anthropic import Claude

agent = Agent(
    model=<PERSON>(
        id="claude-sonnet-4-20250514",
        default_headers={"anthropic-beta": "code-execution-2025-05-22"},
    ),
    tools=[
        {
            "type": "code_execution_20250522",
            "name": "code_execution",
        }
    ],
    markdown=True,
)

agent.print_response(
    "Calculate the mean and standard deviation of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]",
    stream=True,
)
