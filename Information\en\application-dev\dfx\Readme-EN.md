# Performance Analysis Kit

- [Introduction to Performance Analysis Kit](performance-analysis-kit-overview.md)
- HiLog
  - [Using HiLog (ArkTS)](hilog-guidelines-arkts.md)
  - [Using HiLog (C/C++)](hilog-guidelines-ndk.md)
- HiAppEvent
  - [Introduction to HiAppEvent](hiappevent-intro.md)
  - Event Subscription
    - Application Event
      - [Subscribing to Application Events (ArkTS)](hiappevent-watcher-app-events-arkts.md)
      - [Subscribing to Application Events (C/C++)](hiappevent-watcher-app-events-ndk.md)
    - System Event
      - Crash Event
        - [Crash Event Overview](hiappevent-watcher-crash-events.md)
        - [Subscribing to Crash Events (ArkTS)](hiappevent-watcher-crash-events-arkts.md)
        - [Subscribing to Crash Events (C/C++)](hiappevent-watcher-crash-events-ndk.md)
      - Freeze Event
        - [Freeze Event Overview](hiappevent-watcher-freeze-events.md)
        - [Subscribing to Freeze Events (ArkTS)](hiappevent-watcher-freeze-events-arkts.md)
        - [Subscribing to Freeze Events (C/C++)](hiappevent-watcher-freeze-events-ndk.md)
      - Resource Leak Event
        - [Resource Leak Event Overview](hiappevent-watcher-resourceleak-events.md)
        - [Subscribing to Resource Leak Events (ArkTS)](hiappevent-watcher-resourceleak-events-arkts.md)
        - [Subscribing to Resource Leak Events (C/C++)](hiappevent-watcher-resourceleak-events-ndk.md)
      - Address Sanitizer Event
        - [Address Sanitizer Event Overview](hiappevent-watcher-address-sanitizer-events.md)
        - [Subscribing to Address Sanitizer Events (ArkTS)](hiappevent-watcher-address-sanitizer-events-arkts.md)
        - [Subscribing to Address Sanitizer Events (C/C++)](hiappevent-watcher-address-sanitizer-events-ndk.md)
      - Main Thread Jank Event
        - [Main Thread Jank Event Overview](hiappevent-watcher-mainthreadjank-events.md)
        - [Subscribing to Main Thread Jank Events (ArkTS)](hiappevent-watcher-mainthreadjank-events-arkts.md)
        - [Subscribing to Main Thread Jank Events (C/C++)](hiappevent-watcher-mainthreadjank-events-ndk.md)
  <!--Del-->
  - [Event Reporting](hiappevent-event-reporting.md)
  <!--DelEnd-->
- HiTraceMeter
  - [Using HiTraceMeter (ArkTS/JS)](hitracemeter-guidelines-arkts.md)
  - [Using HiTraceMeter (C/C++)](hitracemeter-guidelines-ndk.md)
  - [Viewing HiTraceMeter Logs](hitracemeter-view.md)
- HiTraceChain
  - [Using HiTraceChain (ArkTS/JS)](hitracechain-guidelines-arkts.md)
  - [Using HiTraceChain (C/C++)](hitracechain-guidelines-ndk.md)
- HiChecker
  - [Using HiChecker (ArkTS/JS)](hichecker-guidelines-arkts.md)
- HiDebug
  - [Using HiDebug (ArkTS)](hidebug-guidelines-arkts.md)
  - [Using HiDebug (C/C++)](hidebug-guidelines-ndk.md)
- HiCollie
  - [Using HiCollie (C/C++)](hicollie-guidelines-ndk.md)
- Error Management
  - [Development of Error Manager](errormanager-guidelines.md)
  - [Development of Application Recovery](apprecovery-guidelines.md)
- Fault Analysis
  - [Analyzing JSCrash](jscrash-guidelines.md)
  - [Analyzing CppCrash](cppcrash-guidelines.md)
  - [Analyzing AppFreeze](appfreeze-guidelines.md)
- Command Line Tools
  - [hdc](hdc.md)
  - [hilog](hilog.md)
  - [hidumper](hidumper.md)
  - [hitrace](hitrace.md)
  - [hiperf](hiperf.md)
  <!--Del-->
  - [hisysevent](hisysevent.md)
  - [uinput](uinput.md)
  <!--DelEnd-->
