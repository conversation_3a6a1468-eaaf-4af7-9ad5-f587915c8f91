#!/usr/bin/env python3
"""
Simple test to isolate the real problem
"""

import logging
import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import tool decorator
try:
    from agno.tools import tool
    logger.info("✅ Tool decorator imported")
except ImportError as e:
    logger.error(f"❌ Tool decorator import failed: {e}")
    tool = None

def test_imports():
    """Test all imports"""
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama
        from agno.tools import tool
        logger.info("✅ Agno imports successful")
        return True
    except Exception as e:
        logger.error(f"❌ Agno import failed: {e}")
        return False

def test_ollama_model():
    """Test Ollama model creation"""
    try:
        from agno.models.ollama import Ollama

        model = Ollama(
            id=config.AGENT_MODEL,
            host=config.OLLAMA_URL,
            options={
                "num_predict": config.AGENT_MAX_TOKENS,
                "temperature": config.AGENT_TEMPERATURE
            }
        )
        logger.info("✅ Ollama model created successfully")
        return True
    except Exception as e:
        logger.error(f"❌ Ollama model creation failed: {e}")
        return False

@tool(
    name="simple_test_tool",
    description="A simple test tool",
    show_result=True
)
def simple_test_tool(message: str) -> str:
    """Simple test tool that just returns a message.

    Args:
        message: Test message

    Returns:
        Formatted response
    """
    logger.info(f"🔧 Simple tool called with: {message}")
    return f"Tool response: {message}"

def test_agent_creation():
    """Test agent creation with simple tool"""
    try:
        from agno.agent import Agent
        from agno.models.ollama import Ollama

        model = Ollama(
            id=config.AGENT_MODEL,
            host=config.OLLAMA_URL,
            options={
                "num_predict": config.AGENT_MAX_TOKENS,
                "temperature": config.AGENT_TEMPERATURE
            }
        )

        agent = Agent(
            name="Simple Test Agent",
            model=model,
            tools=[simple_test_tool],
            description="A simple test agent",
            instructions=["You are a test agent. Always use the simple_test_tool."],
            show_tool_calls=True,
            markdown=True
        )

        logger.info("✅ Agent created successfully")
        return agent
    except Exception as e:
        logger.error(f"❌ Agent creation failed: {e}")
        return None

def test_agent_run():
    """Test agent run"""
    agent = test_agent_creation()
    if not agent:
        return False

    try:
        logger.info("🧪 Testing agent run...")
        response = agent.run("Test message")
        logger.info(f"✅ Agent response: {response.content}")
        return True
    except Exception as e:
        logger.error(f"❌ Agent run failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running simple agent tests...")

    tests = [
        ("Import Test", test_imports),
        ("Ollama Model Test", test_ollama_model),
        ("Agent Creation Test", lambda: test_agent_creation() is not None),
        ("Agent Run Test", test_agent_run)
    ]

    for test_name, test_func in tests:
        print(f"\n📝 {test_name}...")
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
                break
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
            break

if __name__ == "__main__":
    main()
