"""
Find files in a directory that match a pattern.
"""

import os
import sys
import re
import json

def find_files(directory, pattern, case_sensitive=False):
    """Find files in a directory that match a pattern."""
    matches = []
    
    if not case_sensitive:
        pattern = pattern.lower()
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.d.ts') or file.endswith('.d.ets') or file.endswith('.md'):
                if not case_sensitive:
                    if pattern in file.lower():
                        matches.append(os.path.join(root, file))
                else:
                    if pattern in file:
                        matches.append(os.path.join(root, file))
    
    return matches

def search_content(directory, pattern, case_sensitive=False):
    """Search for pattern in file content."""
    matches = []
    
    if not case_sensitive:
        pattern_re = re.compile(pattern, re.IGNORECASE)
    else:
        pattern_re = re.compile(pattern)
    
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.d.ts') or file.endswith('.d.ets') or file.endswith('.md'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if pattern_re.search(content):
                            matches.append(file_path)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return matches

def main():
    """Main function."""
    if len(sys.argv) < 3:
        print("Usage: python find_files.py <directory> <pattern> [--content] [--case-sensitive]")
        return
    
    directory = sys.argv[1]
    pattern = sys.argv[2]
    search_in_content = "--content" in sys.argv
    case_sensitive = "--case-sensitive" in sys.argv
    
    if search_in_content:
        matches = search_content(directory, pattern, case_sensitive)
    else:
        matches = find_files(directory, pattern, case_sensitive)
    
    # Print results
    print(f"Found {len(matches)} matches:")
    for match in matches:
        print(match)
    
    # Save results to JSON
    with open('search_results.json', 'w') as f:
        json.dump(matches, f, indent=2)
    
    print(f"\nResults saved to search_results.json")

if __name__ == "__main__":
    main()
