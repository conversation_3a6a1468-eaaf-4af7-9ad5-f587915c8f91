# featureAbility Switching


  | API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding API in the Stage Model| 
| -------- | -------- | -------- |
| [getWant(callback: AsyncCallback&lt;Want&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitygetwant)<br>[getWant(): Promise&lt;Want&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitygetwant-1) | \@ohos.app.ability.UIAbility.d.ts | [launchWant: Want;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#properties)| 
| [startAbility(parameter: StartAbilityParameter, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitystartability)<br>[startAbility(parameter: StartAbilityParameter): Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitystartability-1) | application\UIAbilityContext.d.ts | [startAbility(want: Want, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextstartability)<br>[startAbility(want: Want, options: StartOptions, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextstartability-1)<br>[startAbility(want: Want, options?: StartOptions): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextstartability-2) |
| [getContext(): Context;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitygetcontext) | \@ohos.app.ability.UIAbility.d.ts | [context: UIAbilityContext;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#properties)|
| [startAbilityForResult(parameter: StartAbilityParameter, callback: AsyncCallback&lt;AbilityResult&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitystartabilityforresult7)<br>[startAbilityForResult(parameter: StartAbilityParameter): Promise&lt;AbilityResult&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitystartabilityforresult7-1) | application\UIAbilityContext.d.ts | [startAbilityForResult(want: Want, callback: AsyncCallback&lt;AbilityResult&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextstartabilityforresult)<br>[startAbilityForResult(want: Want, options: StartOptions, callback: AsyncCallback&lt;AbilityResult&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextstartabilityforresult-1)<br>[startAbilityForResult(want: Want, options?: StartOptions): Promise&lt;AbilityResult&gt;;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextstartabilityforresult-2) |
| [terminateSelfWithResult(parameter: AbilityResult, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityterminateselfwithresult7)<br>[terminateSelfWithResult(parameter: AbilityResult): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityterminateselfwithresult7-1) | application\UIAbilityContext.d.ts | [terminateSelfWithResult(parameter: AbilityResult, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextterminateselfwithresult)<br>[terminateSelfWithResult(parameter: AbilityResult): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextterminateselfwithresult-1) |
| [terminateSelf(callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityterminateself7)<br>[terminateSelf(): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityterminateself7-1) | application\UIAbilityContext.d.ts | [terminateSelf(callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextterminateself)<br>[terminateSelf(): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextterminateself-1) |
| [acquireDataAbilityHelper(uri: string): DataAbilityHelper;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityacquiredataabilityhelper7) | \@ohos.data.dataShare.d.ts<br>\@ohos.data.fileAccess.d.ts | [createDataShareHelper(context: Context, uri: string, callback: AsyncCallback&lt;DataShareHelper&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#datasharecreatedatasharehelper)<br>[createDataShareHelper(context: Context, uri: string): Promise&lt;DataShareHelper&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#datasharecreatedatasharehelper-1)<br>[createFileAccessHelper(context: Context): FileAccessHelper;](../reference/apis-core-file-kit/js-apis-fileAccess-sys.md#fileaccesscreatefileaccesshelper-1)<br>[createFileAccessHelper(context: Context, wants: Array&lt;Want&gt;): FileAccessHelper;](../reference/apis-core-file-kit/js-apis-fileAccess-sys.md#fileaccesscreatefileaccesshelper) |
| [hasWindowFocus(callback: AsyncCallback&lt;boolean&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityhaswindowfocus7)<br>[hasWindowFocus(): Promise&lt;boolean&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityhaswindowfocus7-1) | \@ohos.window.d.ts | [on(eventType: 'windowStageEvent', callback: Callback&lt;WindowStageEventType&gt;): void;](../reference/apis-arkui/js-apis-window.md#onwindowstageevent9)<br>Checks whether the [active window](../reference/apis-arkui/js-apis-window.md#windowstageeventtype9) has the focus.|
| [connectAbility(request: Want, options:ConnectOptions ): number;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilityconnectability7) | application\UIAbilityContext.d.ts | [connectServiceExtensionAbility(want: Want, options: ConnectOptions): number;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextconnectserviceextensionability) |
| [disconnectAbility(connection: number, callback:AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitydisconnectability7)<br>[disconnectAbility(connection: number): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitydisconnectability7-1) | application\UIAbilityContext.d.ts | [disconnectAbility(connection: number, callback:AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextdisconnectserviceextensionability-1)<br>[disconnectAbility(connection: number): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#uiabilitycontextdisconnectserviceextensionability) |
| [getWindow(callback: AsyncCallback&lt;window.Window&gt;): void;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitygetwindow7)<br>[getWindow(): Promise&lt;window.Window&gt;;](../reference/apis-ability-kit/js-apis-ability-featureAbility.md#featureabilitygetwindow7-1) | \@ohos.window.d.ts | [getLastWindow(ctx: BaseContext, callback: AsyncCallback&lt;Window&gt;): void;](../reference/apis-arkui/js-apis-window.md#windowgetlastwindow9)<br>[getLastWindow(ctx: BaseContext): Promise&lt;Window&gt;;](../reference/apis-arkui/js-apis-window.md#windowgetlastwindow9-1) |
