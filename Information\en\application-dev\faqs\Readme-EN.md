# FAQs

- [Full SDK Compilation](full-sdk-compile-guide.md)
- [Switching to Full SDK](full-sdk-switch-guide.md)
- [Using NDK in a CMake Project](cmake-with-ndk.md)
- [Application Model Development](faqs-ability.md)
- ArkUI Development (ArkTS)
  - [ArkTS Syntax Usage](faqs-arkui-arkts.md)
  - [ArkUI Component Development (ArkTS)](faqs-arkui-component.md)
  - [ArkUI Layout Development (ArkTS)](faqs-arkui-layout.md)
  - [ArkUI Routing/Navigation Development (ArkTS)](faqs-arkui-route-nav.md)
  - [ArkUI Animation/Interaction Event Development (ArkTS)](faqs-arkui-animation-interactive-event.md)
- [ArkUI Development (JS)](faqs-arkui-js.md)
- [ArkTS Common Library Development](faqs-arkts-utils.md)
- [Web Development](faqs-arkui-web.md)
- [Bundle Management Development](faqs-bundle-management.md)
- [Resource Manager Development](faqs-globalization.md)
- [Common Event and Notification Development](faqs-event-notification.md)
- [Graphics Development](faqs-graphics.md)
- [Window Management Development](faqs-window-manager.md)
- [Multimedia Development](faqs-multimedia.md)
- [Basic Security Capability Development](faqs-security.md)
- [Ability Access Control Development](faqs-ability-access-control.md)
- [Data Management Development](faqs-distributed-data-management.md)
- [File Management Development](faqs-file-management.md)
- [Network Management Development](faqs-network-management.md)
- [DFX Development](faqs-dfx.md)
- [Pan-Sensor Development](faqs-sensor.md)
- [Startup Development](faqs-startup.md)
- [Distributed Device Development](faqs-distributed-device-profile.md)
- [SDK Usage](faqs-sdk.md)
- [NDK Usage](faqs-ndk.md)
- [Compiler and Runtime](faqs-compiler-runtime.md)
- [Usage of Third- and Fourth-Party Libraries](faqs-third-fourth-party-library.md)