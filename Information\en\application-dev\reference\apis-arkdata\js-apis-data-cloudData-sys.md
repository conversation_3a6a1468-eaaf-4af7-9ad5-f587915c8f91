# @ohos.data.cloudData (Cloud Service) (System API)

The **cloudData** module provides APIs for implementing device-cloud synergy and device-cloud sharing and setting the device-cloud sync strategy.

Device-cloud synergy enables sync of the structured data (in RDB stores) between devices and the cloud. The cloud serves as a data hub to implement data backup in the cloud and data consistency between the devices with the same account.

Device-cloud sharing enables data sharing across accounts based on device-cloud synergy. Understanding the following concepts helps you better understand the device-cloud sharing process: 

- **sharingResource**: an identifier of the string type generated for each data record shared by an application before device-cloud sync is performed. It uniquely identifies the data record being shared.
- **Participant**: all participants involved in a share, including the inviter and invitees.
- **invitationCode**: an invitation code generated by the share server for a share operation. It is generated after a data share is initiated and attached to an invitation pushed to the devices of target invitees. The target invitees then confirm the invitation via this code.

The **cloudData** module provides the following functionalities:

- [Config](#config): provides APIs for setting device-cloud synergy, including enabling and disabling device-cloud sync, clearing data, and notifying data changes.
- [sharing<sup>11+</sup>](#sharing11): provides APIs for device-cloud sharing, including sharing or unsharing data, exiting a share, changing the privilege on the shared data, querying participants, confirming an invitation, changing invitation confirmation state, and querying the shared resource.

> **NOTE** 
>
> - The initial APIs of this module are supported since API version 10. Newly added APIs will be marked with a superscript to indicate their earliest API version.
>
> - This topic describes only the system APIs provided by the module. For details about its public APIs, see [@ohos.data.cloudData (Cloud Service)](js-apis-data-cloudData.md).
>
> - Before using this module, ensure that the cloud service is available.

## Modules to Import

```ts
import { cloudData } from '@kit.ArkData';
```

## ClearAction

Enumerates the operations for clearing the downloaded cloud data locally.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

| Name     | Description                        |
| --------- | ---------------------------- |
| CLEAR_CLOUD_INFO | Clear the cloud identifier of the data downloaded from the cloud and retain the data locally. |
| CLEAR_CLOUD_DATA_AND_INFO |Clear the data downloaded from the cloud, excluding the cloud data that has been modified locally.  |

## ExtraData<sup>11+</sup>

Represents the transparently transmitted data, which contains information required for a data change notification.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

| Name     | Type  | Mandatory | Description                                                        |
| --------- | ------ | ---- | ------------------------------------------------------------ |
| eventId   | string | Yes  | Event ID. The value **cloud_data_change** indicates cloud data changes.             |
| extraData | string | Yes | Data to be transmitted transparently. <br/>**extraData** is a JSON string that must contain the **data** field. The **data** field contains information required for a change notification, including the account ID, application name, database name, database type, and database table name. All the fields cannot be empty. |

**Example**

```ts
// accountId: ID of the cloud account.
// bundleName: application bundle name.
// containerName: name of the cloud database.
// databaseScopes: type of the cloud database.
// recordTypes: name of the cloud database table.

interface ExtraData {
  eventId: "cloud_data_change",
  extraData: '{
    "data": "{
     "accountId": "aaa",
     "bundleName": "com.bbb.xxx",
     "containerName": "alias",
     "databaseScopes": ["private", "shared"],
     "recordTypes": ["xxx", "yyy", "zzz"]
    }"
  }'
}

```

## StatisticInfo<sup>12+</sup>

Represents the device-cloud sync statistics.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

| Name     | Type  | Mandatory | Description                                                 |
| --------- | ------ | ---- |-----------------------------------------------------|
| table   | string | Yes  | Name of the table queried. For example, the value **cloud_notes** indicates that the sync information of the **cloud_notes** table is queried. |
| inserted   | number | Yes  | Number of data records that are added locally and have not been synced to the cloud. For example, the value **2** indicates that the table has two data records that are added locally but not synced to the cloud.         |
| updated   | number | Yes  | Number of data records that are modified locally or on the cloud but have not been synced. For example, the value **2** indicates that the table has two data records that are updated locally or on the cloud but not synced.    |
| normal | number | Yes  | Number of consistent data records between the device and the cloud. For example, the value **2** indicates that table has two data records that are consistent between the device and the cloud.                    |

## SyncInfo<sup>12+</sup>

Represents information required for the last device-cloud sync.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

| Name      | Type                                                        | Mandatory | Description                      |
| ---------- | ------------------------------------------------------------ | ---- | -------------------------- |
| startTime  | Date                                                         | Yes  | Start time of the last device-cloud sync. |
| finishTime | Date                                                         | Yes  | End time of the last device-cloud sync. |
| code       | [relationalStore.ProgressCode](js-apis-data-relationalStore.md#progresscode10) | Yes  | Status of the last device-cloud sync. |

## Config

Provides APIs for setting device-cloud synergy, including enabling and disabling device-cloud synergy, clearing data, and notifying data changes.

### enableCloud

static enableCloud(accountId: string, switches: Record<string, boolean>, callback: AsyncCallback&lt;void&gt;): void

Enables device-cloud synergy. This API uses an asynchronous callback to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name   | Type                           | Mandatory | Description                                                        |
| --------- | ------------------------------- | ---- | ------------------------------------------------------------ |
| accountId | string                          | Yes  | ID of the cloud account.                                        |
| switches  | Record<string, boolean>         | Yes  | Device-cloud synergy settings for applications. The value **true** means to enable device-cloud synergy; the value **false** means the opposite. |
| callback  | AsyncCallback&lt;void&gt;       | Yes  | Callback used to return the result.                                                  |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
let switches: Record<string, boolean> = { 'test_bundleName1': true, 'test_bundleName2': false };
try {
  cloudData.Config.enableCloud(account, switches, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeded in enabling cloud');
    } else {
      console.error(`Failed to enable.Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### enableCloud

static enableCloud(accountId: string, switches: Record<string, boolean>): Promise&lt;void&gt;

Enables device-cloud synergy. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name   | Type                           | Mandatory | Description                                                        |
| --------- | ------------------------------- | ---- | ------------------------------------------------------------ |
| accountId | string                          | Yes  | ID of the cloud account.                                        |
| switches  | Record<string, boolean>         | Yes  | Device-cloud synergy settings for applications. The value **true** means to enable device-cloud synergy; the value **false** means the opposite. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
let switches: Record<string, boolean> = { 'test_bundleName1': true, 'test_bundleName2': false };
try {
  cloudData.Config.enableCloud(account, switches).then(() => {
    console.info('Succeeded in enabling cloud');
  }).catch((err: BusinessError) => {
    console.error(`Failed to enable.Code: ${err.code}, message: ${err.message}`);
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### disableCloud

static disableCloud(accountId: string, callback: AsyncCallback&lt;void&gt;): void

Disables device-cloud synergy. This API uses an asynchronous callback to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name   | Type                     | Mandatory | Description                |
| --------- | ------------------------- | ---- | -------------------- |
| accountId | string                    | Yes  | ID of the cloud account. |
| callback  | AsyncCallback&lt;void&gt; | Yes  | Callback used to return the result.          |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
try {
  cloudData.Config.disableCloud(account, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeded in disabling cloud');
    } else {
      console.error(`Failed to disableCloud. Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### disableCloud

static disableCloud(accountId: string): Promise&lt;void&gt;

Disables device-cloud synergy. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name   | Type  | Mandatory | Description                |
| --------- | ------ | ---- | -------------------- |
| accountId | string | Yes  | ID of the cloud account. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
try {
  cloudData.Config.disableCloud(account).then(() => {
    console.info('Succeeded in disabling cloud');
  }).catch((err: BusinessError) => {
    console.error(`Failed to disableCloud. Code: ${err.code}, message: ${err.message}`);
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### changeAppCloudSwitch

static changeAppCloudSwitch(accountId: string, bundleName: string, status: boolean, callback: AsyncCallback&lt;void&gt;): void

Changes the device-cloud synergy setting for an application. This API uses an asynchronous callback to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| accountId | string                          | Yes  | ID of the cloud account. |
| bundleName| string                         | Yes  | Bundle name of the application. |
| status    | boolean                        | Yes  | Device-cloud synergy setting for the application. The value **true** means to enable device-cloud synergy; the value **false** means the opposite. |
| callback  | AsyncCallback&lt;void&gt;       | Yes  | Callback used to return the result.                  |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
let bundleName: string = 'test_bundleName';
try {
  cloudData.Config.changeAppCloudSwitch(account, bundleName, true, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeded in changing App cloud switch');
    } else {
      console.error(`Failed to change App cloud switch. Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### changeAppCloudSwitch

static changeAppCloudSwitch(accountId: string, bundleName: string, status: boolean): Promise&lt;void&gt;

Changes the device-cloud synergy setting for an application. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| accountId | string                          | Yes  | ID of the cloud account. |
| bundleName| string                         | Yes  | Bundle name of the application. |
| status    | boolean                        | Yes  | Device-cloud synergy setting for the application. The value **true** means to enable device-cloud synergy; the value **false** means the opposite. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
let bundleName: string = 'test_bundleName';
try {
  cloudData.Config.changeAppCloudSwitch(account, bundleName, true).then(() => {
    console.info('Succeeded in changing App cloud switch');
  }).catch((err: BusinessError) => {
    console.error(`Failed to change App cloud switch. Code is ${err.code}, message is ${err.message}`);
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### notifyDataChange

static notifyDataChange(accountId: string, bundleName: string, callback: AsyncCallback&lt;void&gt;): void

Notifies the data changes in the cloud. This API uses an asynchronous callback to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Server

**Parameters**

| Name    | Type                     | Mandatory | Description                |
| ---------- | ------------------------- | ---- | -------------------- |
| accountId  | string                    | Yes  | ID of the cloud account. |
| bundleName | string                    | Yes  | Bundle name of the application.            |
| callback   | AsyncCallback&lt;void&gt; | Yes  | Callback used to return the result.          |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
let bundleName: string = 'test_bundleName';
try {
  cloudData.Config.notifyDataChange(account, bundleName, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeded in notifying the change of data');
    } else {
      console.error(`Failed to notify the change of data. Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### notifyDataChange

static notifyDataChange(accountId: string,bundleName: string): Promise&lt;void&gt;

Notifies the data changes in the cloud. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Server

**Parameters**

| Name    | Type  | Mandatory | Description                |
| ---------- | ------ | ---- | -------------------- |
| accountId  | string | Yes  | ID of the cloud account. |
| bundleName | string | Yes  | Bundle name of the application.            |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = 'test_id';
let bundleName: string = 'test_bundleName';
try {
  cloudData.Config.notifyDataChange(account, bundleName).then(() => {
    console.info('Succeeded in notifying the change of data');
  }).catch((err: BusinessError) => {
    console.error(`Failed to notify the change of data. Code: ${err.code}, message: ${err.message}`);
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### notifyDataChange<sup>11+</sup>

 **static** notifyDataChange(extInfo: ExtraData, callback: AsyncCallback&lt;void&gt;):void

Notifies the data changes in the cloud with the specified information, such as the database and table names (specified by the **extraData** field in **extInfo**). This API uses an asynchronous callback to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name  | Type                     | Mandatory | Description                                   |
| -------- | ------------------------- | ---- | --------------------------------------- |
| extInfo  | [ExtraData](#extradata11)   | Yes  | Transparently transmitted data, including information about the application that has data changes. |
| callback | AsyncCallback&lt;void&gt; | Yes  | Callback used to return the result. If the operation is successful, **err** is **undefined**. Otherwise, **err** is an error object.|

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, which is usually returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let eventId: string = "cloud_data_change";
let extraData: string = '{"data":"{"accountId":"aaa","bundleName":"com.bbb.xxx","containerName":"alias", "databaseScopes": ["private", "shared"],"recordTypes":"["xxx","yyy","zzz"]"}"}';
try {
  cloudData.Config.notifyDataChange({
    eventId: eventId, extraData: extraData
  }, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeded in notifying the change of data');
    } else {
      console.error(`Failed to notify the change of data. Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### notifyDataChange<sup>11+</sup>

static notifyDataChange(extInfo: ExtraData, userId: number,callback: AsyncCallback&lt;void&gt;):void

Notifies the data changes of a user in the cloud. This API uses an asynchronous callback to return the result. You can also specify the database and tables with data changes in the **extraData** field in **extInfo**.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name  | Type                     | Mandatory | Description                                           |
| -------- | ------------------------- | ---- | ----------------------------------------------- |
| extInfo  | [ExtraData](#extradata11)   | Yes  | Transparently transmitted data, including information about the application that has data changes.       |
| userId   | number                    | Yes  | User ID in the system. |
| callback | AsyncCallback&lt;void&gt; | Yes  | Callback used to return the result. If the operation is successful, **err** is **undefined**. Otherwise, **err** is an error object.|

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, which is usually returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let eventId: string = "cloud_data_change";
let extraData: string = '{"data":"{"accountId":"aaa","bundleName":"com.bbb.xxx","containerName":"alias", "databaseScopes": ["private", "shared"],"recordTypes":"["xxx","yyy","zzz"]"}"}';
let userId: number = 100;
try {
  cloudData.Config.notifyDataChange({
    eventId: eventId, extraData: extraData
  }, userId, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeded in notifying the change of data');
    } else {
      console.error(`Failed to notify the change of data. Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### notifyDataChange<sup>11+</sup>

**static** notifyDataChange(extInfo: ExtraData, userId?: number): Promise&lt;void&gt;

Notifies the data changes in the cloud. This API uses a promise to return the result. You can specify the database and tables with data changes in the **extraData** field in **extInfo**, and specify the user ID.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name | Type                   | Mandatory | Description                                           |
| ------- | ----------------------- | ---- | ----------------------------------------------- |
| extInfo | [ExtraData](#extradata11) | Yes  | Transparently transmitted data, including information about the application that has data changes.        |
| userId  | number                  | No  | User ID. This parameter is optional. The default value is the current user ID. If this parameter is specified, the value must be an existing user ID in the system. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, which is usually returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let eventId: string = "cloud_data_change";
let extraData: string = '{"data":"{"accountId":"aaa","bundleName":"com.bbb.xxx","containerName":"alias", "databaseScopes": ["private", "shared"],"recordTypes":"["xxx","yyy","zzz"]"}"}';
let userId: number = 100;
try {
  cloudData.Config.notifyDataChange({
    eventId: eventId, extraData: extraData
  }, userId).then(() => {
    console.info('Succeeded in notifying the change of data');
  }).catch((err: BusinessError) => {
    console.error(`Failed to notify the change of data. Code: ${err.code}, message: ${err.message}`);
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### queryStatistics<sup>12+</sup>

static queryStatistics(accountId: string, bundleName: string, storeId?: string): Promise&lt;Record&lt;string, Array&lt;StatisticInfo&gt;&gt;&gt;

Queries device-cloud data statistics, which include the data not synchronized, data synced and consistent, and data synced but inconsistent between the device and the cloud. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name | Type     | Mandatory | Description                               |
| ------- |---------| ---- |-----------------------------------|
| accountId | string  | Yes  | ID of the cloud account.                      |
| bundleName | string  | Yes  | Bundle name of the application.                            |
| storeId  | string  | No  | Name of the RDB store. If this parameter is not specified, all local databases of this application are queried by default. |

**Return value**

| Type                                                                                  | Description                    |
|--------------------------------------------------------------------------------------| ------------------------ |
| Promise&lt;Record&lt;string, Array&lt;[StatisticInfo](#statisticinfo12)&gt;&gt;&gt; | Promise used to return the table name and statistics. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

const accountId:string = "accountId";
const bundleName:string = "bundleName";
const storeId:string = "storeId";

cloudData.Config.queryStatistics(accountId, bundleName, storeId).then((result) => {
    console.info(`Succeeded in querying statistics. Info is ${JSON.stringify(result)}`);
}).catch((err: BusinessError) => {
    console.error(`Failed to query statistics. Error code is ${err.code}, message is ${err.message}`);
});
```

### queryLastSyncInfo<sup>12+</sup>

static queryLastSyncInfo(accountId: string, bundleName: string, storeId?: string): Promise&lt;Record<string, SyncInfo>>

Queries information about the last device-cloud sync. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name    | Type  | Mandatory | Description                                                        |
| ---------- | ------ | ---- | ------------------------------------------------------------ |
| accountId  | string | Yes  | ID of the cloud account.                                        |
| bundleName | string | Yes  | Bundle name of the application.                                                  |
| storeId    | string | No  | Name of the RDB store. The default value is an empty string. If the default value is used, this API queries the last device-cloud sync information of all databases of this application. |

**Return value**

| Type                                                        | Description                                        |
| ------------------------------------------------------------ | -------------------------------------------- |
| Promise&lt;Record&lt;string, [SyncInfo](#syncinfo12)&gt;&gt; | Promise used to return the database name and the result set of the last device-cloud sync. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

const accountId:string = "accountId";
const bundleName:string = "bundleName";
const storeId:string = "storeId";
try {
    cloudData.Config.queryLastSyncInfo(accountId, bundleName, storeId).then((result) => {
    	console.info(`Succeeded in querying last syncinfo. Info is ${JSON.stringify(result)}`);
	}).catch((err: BusinessError) => {
    	console.error(`Failed to query last syncinfo. Error code is ${err.code}, message is ${err.message}`);
	});
} catch(e) {
    let error = e as BusinessError;
  	console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### setGlobalCloudStrategy<sup>12+</sup>

static setGlobalCloudStrategy(strategy: StrategyType, param?: Array&lt;commonType.ValueType&gt;): Promise&lt;void&gt;

Sets a global device-cloud sync strategy. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name    | Type                                                                    | Mandatory | Description               |
| ---------- |------------------------------------------------------------------------| ---- |-------------------|
| strategy  | [StrategyType](js-apis-data-cloudData.md#strategytype)    | Yes  | Type of the strategy to set.         |
| param | Array&lt;[commonType.ValueType](js-apis-data-commonType.md#valuetype)&gt; | No  | Strategy parameters to set. If this parameter is not specified, all the strategy configuration is canceled by default. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.Config.setGlobalCloudStrategy(cloudData.StrategyType.NETWORK, [cloudData.NetWorkStrategy.WIFI]).then(() => {
    console.info('Succeeded in setting the global cloud strategy');
}).catch((err: BusinessError) => {
    console.error(`Failed to set global cloud strategy. Code: ${err.code}, message: ${err.message}`);
});
```

###  clear

static clear(accountId: string, appActions: Record<string, ClearAction>,  callback: AsyncCallback&lt;void&gt;): void

Clears the cloud data locally. This API uses an asynchronous callback to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name    | Type                                               | Mandatory | Description                            |
| ---------- | --------------------------------------------------- | ---- | -------------------------------- |
| accountId  | string                                              | Yes  | ID of the cloud account.            |
| appActions | Record<string, [ClearAction](#clearaction)>         | Yes  | Information about the application whose data is to be cleared and the operation to perform. |
| callback   | AsyncCallback&lt;void&gt;                           | Yes  | Callback used to return the result.                      |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = "test_id";
type dataType = Record<string, cloudData.ClearAction>
let appActions: dataType = {
  'test_bundleName1': cloudData.ClearAction.CLEAR_CLOUD_INFO,
  'test_bundleName2': cloudData.ClearAction.CLEAR_CLOUD_DATA_AND_INFO
};
try {
  cloudData.Config.clear(account, appActions, (err: BusinessError) => {
    if (err === undefined) {
      console.info('Succeeding in clearing cloud data');
    } else {
      console.error(`Failed to clear cloud data. Code: ${err.code}, message: ${err.message}`);
    }
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

### clear

static clear(accountId: string, appActions: Record<string, ClearAction>): Promise&lt;void&gt;

Clears the cloud data locally. This API uses a promise to return the result.

**Required permissions**: ohos.permission.CLOUDDATA_CONFIG

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Config

**Parameters**

| Name    | Type                                               | Mandatory | Description                            |
| ---------- | --------------------------------------------------- | ---- | -------------------------------- |
| accountId  | string                                              | Yes  | ID of the cloud account.            |
| appActions | Record<string, [ClearAction](#clearaction)>         | Yes  | Information about the application whose data is to be cleared and the operation to perform. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;void&gt; | Promise that returns no value. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 201      | Permission verification failed, usually the result returned by VerifyAccessToken.|
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let account: string = "test_id";
type dataType = Record<string, cloudData.ClearAction>;
let appActions: dataType = {
  'test_bundleName1': cloudData.ClearAction.CLEAR_CLOUD_INFO,
  'test_bundleName2': cloudData.ClearAction.CLEAR_CLOUD_DATA_AND_INFO
};
try {
  cloudData.Config.clear(account, appActions).then(() => {
    console.info('Succeeding in clearing cloud data');
  }).catch((err: BusinessError) => {
    console.error(`Failed to clear cloud data. Code: ${err.code}, message: ${err.message}`);
  });
} catch (e) {
  let error = e as BusinessError;
  console.error(`An unexpected error occurred. Code: ${error.code}, message: ${error.message}`);
}
```

## sharing<sup>11+</sup>

Provides APIs for device-cloud data sharing, including sharing or unsharing data, exiting a share, changing the privilege on the shared data, querying participants, confirming an invitation, changing the invitation confirmation state, and querying the shared resource.

### Role<sup>11+</sup>

Enumerates the roles of the participants in a device-cloud share.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

| Name          | Value  | Description                              |
| --------------| ---- | ---------------------------------- |
| ROLE_INVITER  | 0    | Inviter, the one who shares data. Use the enum name rather than the enum value. |
| ROLE_INVITEE  | 1    | Invitee, the one who can use the shared data. Use the enum name rather than the enum value. |

### State<sup>11+</sup>

Enumerates the device-cloud sharing states.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

| Name          | Value  | Description                              |
| --------------| ---- | ---------------------------------- |
| STATE_UNKNOWN    | 0    | Unknown state. Use the enum name rather than the enum value.  |
| STATE_ACCEPTED   | 1    | The device-cloud sharing invitation is accepted. Use the enum name rather than the enum value. |
| STATE_REJECTED   | 2    | The device-cloud sharing invitation is rejected. Use the enum name rather than the enum value. |
| STATE_SUSPENDED  | 3    | The device-cloud sharing is suspended temporarily. Use the enum name rather than the enum value. |
| STATE_UNAVAILABLE<sup>12+</sup>   | 4    | The device-cloud sharing is unavailable. Use the enum name rather than the enum value. |

### SharingCode<sup>11+</sup>

Enumerates the error codes for device-cloud sharing.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

| Name          | Value  | Description                              |
| --------------| ---- | ---------------------------------- |
| SUCCESS                 | 0    | Operation successful. Use the enum name rather than the enum value.  |
| REPEATED_REQUEST        | 1    | Repeated invitation, which means the participant has been invited. Use the enum name rather than the enum value. |
| NOT_INVITER             | 2    | The participant is not the inviter of this share. Use the enum name rather than the enum value. |
| NOT_INVITER_OR_INVITEE  | 3    | Invalid participant, which means the participant is neither the inviter nor the invitee. Use the enum name rather than the enum value. |
| OVER_QUOTA              | 4    | The number of device-cloud sharing times has reached the limit for the current account. Use the enum name rather than the enum value.  |
| TOO_MANY_PARTICIPANTS   | 5    | The number of device-cloud sharing participants has reached the limit. Use the enum name rather than the enum value. |
| INVALID_ARGS            | 6    | Invalid parameter. Use the enum name rather than the enum value. |
| NETWORK_ERROR           | 7    | Network error. Use the enum name rather than the enum value. |
| CLOUD_DISABLED          | 8    | Cloud is disabled. Use the enum name rather than the enum value.  |
| SERVER_ERROR            | 9    | Server error. Use the enum name rather than the enum value. |
| INNER_ERROR             | 10   | System internal error. Use the enum name rather than the enum value. |
| INVALID_INVITATION      | 11   | Invalid invitation, which means the current invitation has expired or does not exist. Use the enum name rather than the enum value. |
| RATE_LIMIT              | 12   | The amount of data to be synchronized at a time has reached the limit. Use the enum name rather than the enum value.  |
| CUSTOM_ERROR            | 1000 | Customized error. Error codes smaller than **1000** are used to define internal error codes, and error codes greater than **1000** are used to customize error codes. Use the enum name rather than the enum value. |

### Result&lt;T&gt;<sup>11+</sup>

Represents the device-cloud sharing result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

| Name         | Type                         | Mandatory | Description          |
| ----------- | --------------------------- | --- | ------------ |
| code        | number                      | Yes  | Error code.      |
| description | string                      | No  | Detailed description of the error code. The default value is **undefined**.      |
| value       | T                           | No  | Value returned. The specific type is specified by the **T** parameter. The default value is **undefined**. |

### Privilege<sup>11+</sup>

Defines the privilege (permissions) on the shared data.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

| Name         | Type                         | Mandatory | Description          |
| ----------- | --------------------------- | --- | ------------ |
| writable    | boolean              | No  | Whether the participant can modify the shared data. The value **true** means the participant can modify the data; the value **false** means the opposite. The default value is **false**.  |
| readable    | boolean              | No  | Whether the participant can read the shared data. The value **true** means the participant can read the data; the value **false** means the opposite. The default value is **false**.  |
| creatable   | boolean              | No  | Whether the participant can create data to share. The value **true** means the participant can create data; the value **false** means the opposite. The default value is **false**. |
| deletable   | boolean              | No  | Whether the participant can delete the shared data. The value **true** means the participant can delete the data; the value **false** means the opposite. The default value is **false**. |
| shareable   | boolean              | No  | Whether the participant can share the data to others. The value **true** means the participant can share the data; the value **false** means the opposite. The default value is **false**. |

### Participant<sup>11+</sup>

Represents information about a participant of device-cloud sharing.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

| Name         | Type                         | Mandatory | Description          |
| ----------- | --------------------------- | --- | ------------ |
| identity    | string                  | Yes  | ID of the participant.             |
| role        | [Role](#role11)           | No  | Role of the participant, inviter or invitee. The default value is **undefined**. |
| state       | [State](#state11)         | No  | State of the device-cloud sharing. The default value is **undefined**. |
| privilege   | [Privilege](#privilege11) | No  | Permissions on the shared data. The [Privilege](#privilege11) defaults are used by default. |
| attachInfo  | string                  | No  | Additional information, such as the verification code used for participant identity verification. The default value is an empty string. |

### allocResourceAndShare<sup>11+</sup>

allocResourceAndShare(storeId: string, predicates: relationalStore.RdbPredicates, participants: Array&lt;Participant&gt;, columns?: Array&lt;string&gt;): Promise&lt;relationalStore.ResultSet&gt;

Allocates a shared resource ID based on the data that matches the specified predicates. This API uses a promise to return the result set of the data to share, which also includes the column names if they are specified.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| storeId      | string                        | Yes  | Name of the RDB store. |
| predicates   | [relationalStore.RdbPredicates](js-apis-data-relationalStore.md#rdbpredicates) | Yes  | Predicates for matching the data to share. |
| participants | Array&lt;[Participant](#participant11)&gt; | Yes  | Participants of the share. |
| columns      | Array&lt;string&gt;           | No  | Columns in which the data is located. The default value is **undefined**, which means column names are not returned. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[relationalStore.ResultSet](js-apis-data-relationalStore.md#resultset)&gt; | Promise used to return the result set of the data to share. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';
import { relationalStore } from '@kit.ArkData';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
let sharingResource: string;
let predicates = new relationalStore.RdbPredicates('test_table');
predicates.equalTo('data', 'data_test');
cloudData.sharing.allocResourceAndShare('storeName', predicates, participants, ['uuid', 'data']).then((resultSet) => {
  if (!resultSet.goToFirstRow()) {
    console.error(`row error`);
    return;
  }
  const res = resultSet.getString(resultSet.getColumnIndex(relationalStore.Field.SHARING_RESOURCE_FIELD));
  console.info(`sharing resource: ${res}`);
  sharingResource = res;
}).catch((err: BusinessError) => {
  console.error(`alloc resource and share failed, code is ${err.code},message is ${err.message}`);
})

```

### allocResourceAndShare<sup>11+</sup>

allocResourceAndShare(storeId: string, predicates: relationalStore.RdbPredicates, participants: Array&lt;Participant&gt;, columns: Array&lt;string&gt;, callback: AsyncCallback&lt;relationalStore.ResultSet&gt;): void

Allocates a shared resource ID based on the data that matches the specified predicates. This API uses an asynchronous callback to return the result set of the data to share, which includes the shared resource ID and column names.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| storeId      | string                        | Yes  | Name of the RDB store. |
| predicates   | [relationalStore.RdbPredicates](js-apis-data-relationalStore.md#rdbpredicates) | Yes  | Predicates for matching the data to share. |
| participants | Array&lt;[Participant](#participant11)&gt; | Yes  | Participants of the share. |
| columns      | Array&lt;string&gt;           | Yes  | Columns in which the data is located. |
| callback     | AsyncCallback&lt;[relationalStore.ResultSet](js-apis-data-relationalStore.md#resultset)&gt;  | Yes | Callback used to return the result set of the data to share. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { relationalStore } from '@kit.ArkData';
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
let sharingResource: string;
let predicates = new relationalStore.RdbPredicates('test_table');
predicates.equalTo('data', 'data_test');
cloudData.sharing.allocResourceAndShare('storeName', predicates, participants, ['uuid', 'data'], (err: BusinessError, resultSet) => {
  if (err) {
    console.error(`alloc resource and share failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  if (!resultSet.goToFirstRow()) {
    console.error(`row error`);
    return;
  }
  const res = resultSet.getString(resultSet.getColumnIndex(relationalStore.Field.SHARING_RESOURCE_FIELD));
  console.info(`sharing resource: ${res}`);
  sharingResource = res;
})

```

### allocResourceAndShare<sup>11+</sup>

allocResourceAndShare(storeId: string, predicates: relationalStore.RdbPredicates, participants: Array&lt;Participant&gt;, callback: AsyncCallback&lt;relationalStore.ResultSet&gt;): void

Allocates a shared resource ID based on the data that matches the specified predicates. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| storeId      | string                        | Yes  | Name of the RDB store. |
| predicates   | [relationalStore.RdbPredicates](js-apis-data-relationalStore.md#rdbpredicates) | Yes  | Predicates for matching the data to share. |
| participants | Array&lt;[Participant](#participant11)&gt; | Yes  | Participants of the share. |
| callback     | AsyncCallback&lt;[relationalStore.ResultSet](js-apis-data-relationalStore.md#resultset)&gt;  | Yes  | Callback used to return the result set of the data to share. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { relationalStore } from '@kit.ArkData';
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
let sharingResource: string;
let predicates = new relationalStore.RdbPredicates('test_table');
predicates.equalTo('data', 'data_test');
cloudData.sharing.allocResourceAndShare('storeName', predicates, participants, (err: BusinessError, resultSet) => {
  if (err) {
    console.error(`alloc resource and share failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  if (!resultSet.goToFirstRow()) {
    console.error(`row error`);
    return;
  }
  const res = resultSet.getString(resultSet.getColumnIndex(relationalStore.Field.SHARING_RESOURCE_FIELD));
  console.info(`sharing resource: ${res}`);
  sharingResource = res;
})

```

### share<sup>11+</sup>

share(sharingResource: string, participants: Array&lt;Participant&gt;): Promise&lt;Result&lt;Array&lt;Result&lt;Participant&gt;&gt;&gt;&gt;

Shares data based on the specified shared resource ID and participants. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string                                     | Yes  | Shared resource ID. |
| participants      | Array&lt;[Participant](#participant11)&gt;   | Yes  | Participants of the share. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;Array&lt;[Result](#resultt11)&lt;[Participant](#participant11)&gt;&gt;&gt;&gt; | Promise used to return the result. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
cloudData.sharing.share('sharing_resource_test', participants).then((result) => {
  console.info(`share success, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`share failed, code is ${err.code},message is ${err.message}`);
})

```

### share<sup>11+</sup>

share(sharingResource: string, participants: Array&lt;Participant&gt;, callback: AsyncCallback&lt;Result&lt;Array&lt;Result&lt;Participant&gt;&gt;&gt;&gt;): void

Shares data based on the specified shared resource ID and participants. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource  | string                                     | Yes  | Shared resource ID. |
| participants     | Array&lt;[Participant](#participant11)&gt; | Yes  | Participants of the share. |
| callback         | AsyncCallback&lt;[Result](#resultt11)&lt;Array&lt;[Result](#resultt11)&lt;[Participant](#participant11)&gt;&gt;&gt;&gt;  | Yes  | Callback used to return the result.   |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
cloudData.sharing.share('sharing_resource_test', participants, ((err: BusinessError, result) => {
  if (err) {
    console.error(`share failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`share succeeded, result: ${result}`);
}))

```

### unshare<sup>11+</sup>

unshare(sharingResource: string, participants: Array&lt;Participant&gt;): Promise&lt;Result&lt;Array&lt;Result&lt;Participant&gt;&gt;&gt;&gt;

Unshares data based on the specified shared resource ID and participants. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string                                     | Yes  | Shared resource ID. |
| participants      | Array&lt;[Participant](#participant11)&gt; | Yes  | Participants of the share. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;Array&lt;[Result](#resultt11)&lt;[Participant](#participant11)&gt;&gt;&gt;&gt; | Promise used to return the result. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
cloudData.sharing.unshare('sharing_resource_test', participants).then((result) => {
  console.info(`unshare succeeded, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`unshare failed, code is ${err.code},message is ${err.message}`);
})

```

### unshare<sup>11+</sup>

unshare(sharingResource: string, participants: Array&lt;Participant&gt;, callback: AsyncCallback&lt;Result&lt;Array&lt;Result&lt;Participant&gt;&gt;&gt;&gt;): void

Unshares data based on the specified shared resource ID and participants. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource  | string                                     | Yes  | Shared resource ID. |
| participants     | Array&lt;[Participant](#participant11)&gt; | Yes  | Participants of the share. |
| callback         | AsyncCallback&lt;[Result](#resultt11)&lt;Array&lt;[Result](#resultt11)&lt;[Participant](#participant11)&gt;&gt;&gt;&gt;  | Yes  | Callback used to return the result.   |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})
cloudData.sharing.unshare('sharing_resource_test', participants, ((err: BusinessError, result) => {
  if (err) {
    console.error(`unshare failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`unshare succeeded, result: ${result}`);
}))

```

### exit<sup>11+</sup>

exit(sharingResource: string): Promise&lt;Result&lt;void&gt;&gt;

Exits the share of the specified shared resource. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string       | Yes  | Shared resource ID. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;void&gt;&gt; | Promise used to return the result. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.exit('sharing_resource_test').then((result) => {
  console.info(`exit share success, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`exit share failed, code is ${err.code},message is ${err.message}`);
})

```

### exit<sup>11+</sup>

exit(sharingResource: string, callback: AsyncCallback&lt;Result&lt;void&gt;&gt;): void

Exits the share of the specified shared resource. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource  | string                | Yes  | Shared resource ID. |
| callback         | AsyncCallback&lt;[Result](#resultt11)&lt;void&gt;&gt;  | Yes  | Callback used to return the result.   |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.exit('sharing_resource_test', ((err: BusinessError, result) => {
  if (err) {
    console.error(`exit share failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`exit share succeeded, result: ${result}`);
}))

```

### changePrivilege<sup>11+</sup>

changePrivilege(sharingResource: string, participants: Array&lt;Participant&gt;): Promise&lt;Result&lt;Array&lt;Result&lt;Participant&gt;&gt;&gt;&gt;

Changes the privilege on the shared data. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string                                    | Yes  | Shared resource ID. |
| participants      | Array&lt;[Participant](#participant11)&gt;  | Yes  | Participants with new privilege.|

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;Array&lt;[Result](#resultt11)&lt;[Participant](#participant11)&gt;&gt;&gt;&gt; | Promise used to return the result. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})

cloudData.sharing.changePrivilege('sharing_resource_test', participants).then((result) => {
  console.info(`change privilege succeeded, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`change privilege failed, code is ${err.code},message is ${err.message}`);
})

```

### changePrivilege<sup>11+</sup>

changePrivilege(sharingResource: string, participants: Array&lt;Participant&gt;, callback: AsyncCallback&lt;Result&lt;Array&lt;Result&lt;Participant&gt;&gt;&gt;&gt;): void

Changes the privilege on the shared data. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource  | string                | Yes  | Shared resource ID. |
| participants     | Array&lt;[Participant](#participant11)&gt;  | Yes  | Participants with new privilege.|
| callback         | callback: AsyncCallback&lt;[Result](#resultt11)&lt;Array&lt;[Result](#resultt11)&lt;[Participant](#participant11)&gt;&gt;&gt;&gt;  | Yes  | Callback used to return the result.   |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let participants = new Array<cloudData.sharing.Participant>();
participants.push({
  identity: '000000000',
  role: cloudData.sharing.Role.ROLE_INVITER,
  state: cloudData.sharing.State.STATE_UNKNOWN,
  privilege: {
    writable: true,
    readable: true,
    creatable: false,
    deletable: false,
    shareable: false
  },
  attachInfo: ''
})

cloudData.sharing.changePrivilege('sharing_resource_test', participants, ((err: BusinessError, result) => {
  if (err) {
    console.error(`change privilege failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`change privilege succeeded, result: ${result}`);
}))

```

### queryParticipants<sup>11+</sup>

queryParticipants(sharingResource: string): Promise&lt;Result&lt;Array&lt;Participant&gt;&gt;&gt;

Queries the participants of the specified shared data. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string                 | Yes  | Shared resource ID. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;Array&lt;[Participant](#participant11)&gt;&gt;&gt; | Promise used to return the participants obtained. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.queryParticipants('sharing_resource_test').then((result) => {
  console.info(`query participants succeeded, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`query participants failed, code is ${err.code},message is ${err.message}`);
})

```

### queryParticipants<sup>11+</sup>

queryParticipants(sharingResource: string, callback: AsyncCallback&lt;Result&lt;Array&lt;Participant&gt;&gt;&gt;): void

Queries the participants of the specified shared data. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource  | string                | Yes  | Shared resource ID. |
| callback         | AsyncCallback&lt;[Result](#resultt11)&lt;Array&lt;[Participant](#participant11)&gt;&gt;&gt;  | Yes  | Callback used to return the participants obtained. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.queryParticipants('sharing_resource_test', ((err: BusinessError, result) => {
  if (err) {
    console.error(`query participants failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`query participants succeeded, result: ${result}`);
}))

```

### queryParticipantsByInvitation<sup>11+</sup>

queryParticipantsByInvitation(invitationCode: string): Promise&lt;Result&lt;Array&lt;Participant&gt;&gt;&gt;

Queries the participants based on the sharing invitation code. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| invitationCode   | string                 | Yes  | Invitation code of the share. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;Array&lt;[Participant](#participant11)&gt;&gt;&gt; | Promise used to return the participants obtained.|

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.queryParticipantsByInvitation('sharing_invitation_code_test').then((result) => {
  console.info(`query participants by invitation succeeded, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`query participants by invitation failed, code is ${err.code},message is ${err.message}`);
})

```

### queryParticipantsByInvitation<sup>11+</sup>

queryParticipantsByInvitation(invitationCode: string, callback: AsyncCallback&lt;Result&lt;Array&lt;Participant&gt;&gt;&gt;): void

Queries the participants based on the sharing invitation code. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| invitationCode  | string                | Yes  | Invitation code of the share. |
| callback        | AsyncCallback&lt;[Result](#resultt11)&lt;Array&lt;[Participant](#participant11)&gt;&gt;&gt; | Yes  | Callback used to return the participants obtained. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.queryParticipantsByInvitation('sharing_invitation_code_test', ((err: BusinessError, result) => {
  if (err) {
    console.error(`query participants by invitation failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`query participants by invitation succeeded, result: ${result}`);
}))

```

### confirmInvitation<sup>11+</sup>

confirmInvitation(invitationCode: string, state: State): Promise&lt;Result&lt;string&gt;&gt;

Confirms the invitation based on the sharing invitation code and obtains the shared resource ID. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| invitationCode   | string                 | Yes  | Invitation code of the share. |
| state            | [State](#state11)        | Yes  | Confirmation state. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;string&gt;&gt; | Promise used to return the result. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let shareResource: string | undefined;
cloudData.sharing.confirmInvitation('sharing_invitation_code_test', cloudData.sharing.State.STATE_ACCEPTED).then((result: cloudData.sharing.Result<string>) => {
  console.info(`confirm invitation succeeded, result: ${result}`);
  shareResource = result.value;
}).catch((err: BusinessError) => {
  console.error(`confirm invitation failed, code is ${err.code},message is ${err.message}`);
})

```

### confirmInvitation<sup>11+</sup>

confirmInvitation(invitationCode: string, state: State, callback: AsyncCallback&lt;Result&lt;string&gt;&gt;): void

Confirms the invitation based on the sharing invitation code and obtains the shared resource ID. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| invitationCode  | string                | Yes  | Invitation code of the share. |
| state           | [State](#state11)       | Yes  | Confirmation state. |
| callback        | AsyncCallback&lt;[Result](#resultt11)&lt;string&gt;&gt; | Yes  | Callback used to return the result.   |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

let shareResource: string;
cloudData.sharing.confirmInvitation('sharing_invitation_code_test', cloudData.sharing.State.STATE_ACCEPTED, ((err: BusinessError, result) => {
  if (err) {
    console.error(`confirm invitation failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`confirm invitation succeeded, result: ${result}`);
  shareResource = result.value;
}))

```

### changeConfirmation<sup>11+</sup>

changeConfirmation(sharingResource: string, state: State): Promise&lt;Result&lt;void&gt;&gt;

Changes the invitation confirmation state based on the shared resource ID. This API uses a promise to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string                 | Yes  | Shared resource ID. |
| state             | [State](#state11)        | Yes  | New confirmation state of the invitation. |

**Return value**

| Type               | Description                     |
| ------------------- | ------------------------- |
| Promise&lt;[Result](#resultt11)&lt;void&gt;&gt; |  Promise used to return the result. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.changeConfirmation('sharing_resource_test', cloudData.sharing.State.STATE_REJECTED).then((result) => {
  console.info(`change confirmation succeeded, result: ${result}`);
}).catch((err: BusinessError) => {
  console.error(`change confirmation failed, code is ${err.code},message is ${err.message}`);
})

```

### changeConfirmation<sup>11+</sup>

changeConfirmation(sharingResource: string, state: State, callback: AsyncCallback&lt;Result&lt;void&gt;&gt;): void;

Changes the invitation confirmation state based on the shared resource ID. This API uses an asynchronous callback to return the result.

**System capability**: SystemCapability.DistributedDataManager.CloudSync.Client

**Parameters**

| Name   | Type                           | Mandatory | Description                        |
| --------- | ------------------------------- | ---- | ---------------------------- |
| sharingResource   | string                 | Yes  | Shared resource ID. |
| state             | [State](#state11)        | Yes  | New confirmation state of the invitation. |
| callback          | AsyncCallback&lt;[Result](#resultt11)&lt;void&gt;&gt; | Yes  | Callback used to return the result.   |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md).

| ID | Error Message                                            |
| -------- | ---------------------------------------------------- |
| 202      | Permission verification failed, application which is not a system application uses system API.|
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed.|
| 801      | Capability not supported.|

**Example**

```ts
import { BusinessError } from '@kit.BasicServicesKit';

cloudData.sharing.changeConfirmation('sharing_resource_test', cloudData.sharing.State.STATE_REJECTED, ((err: BusinessError, result) => {
  if (err) {
    console.error(`change confirmation failed, code is ${err.code},message is ${err.message}`);
    return;
  }
  console.info(`change confirmation succeeded, result: ${result}`);
}))

```
<!--no_check-->
