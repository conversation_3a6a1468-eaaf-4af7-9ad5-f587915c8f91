/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
/**
 * @file Attribution Test Manager Interface Description file
 * @kit StoreKit
 */
/**
 * Class that is used to declare methods of managing attribution test information.
 *
 * @namespace attributionTestManager
 * @syscap SystemCapability.AppGalleryService.AttributionManager
 * @StageModelOnly
 * @since 5.0.0(12)
 */
declare namespace attributionTestManager {
    /**
     * Enum for attribution source type.
     *
     * @enum { number }
     * @syscap SystemCapability.AppGalleryService.AttributionManager
     * @StageModelOnly
     * @since 5.0.0(12)
     */
    export enum SourceType {
        /**
         * Indicates that attribution sourceType is impression.
         *
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        IMPRESSION = 0,
        /**
         * Indicates that attribution sourceType is click.
         *
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        CLICK = 1
    }
    /**
     * Attribution source info to be validated in the testing environment
     *
     * @typedef AdSourceInfo
     * @syscap SystemCapability.AppGalleryService.AttributionManager
     * @StageModelOnly
     * @since 5.0.0(12)
     */
    export interface AdSourceInfo {
        /**
         * Identifier of the advertisement platform to which the advertisement task belongs.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        adTechId: string;
        /**
         * Identifier of the advertisement task.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        campaignId: string;
        /**
         * AppId of the advertiser app，generated by AppGallery.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        destinationId: string;
        /**
         * Type of attribution source.
         *
         * @type { SourceType }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        sourceType: SourceType;
        /**
         * Identifier of the monitoring platform used for the advertising.
         *
         * @type { ?string[] }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        mmpIds?: string[];
        /**
         * Business information concerned by the ad platform, such as creative ideas、materials.
         *
         * @type { ?string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        serviceTag?: string;
        /**
         * UUID used for computing signature.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        nonce: string;
        /**
         * Timestamp of requesting advertisement.
         *
         * @type { number }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        timestamp: number;
        /**
         * Signature of advertisement info.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        signature: string;
    }
    /**
     * Postback info to be sent in the testing environment.
     *
     * @typedef PostbackInfo
     * @syscap SystemCapability.AppGalleryService.AttributionManager
     * @StageModelOnly
     * @since 5.0.0(12)
     */
    export interface PostbackInfo {
        /**
         * Identifier of the platform to which the postback will send to.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        adTechId: string;
        /**
         * Identifier of the advertisement task.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        campaignId: string;
        /**
         * AppId of the media app，generated by AppGallery.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        sourceId: string;
        /**
         * AppId of the advertiser app，generated by AppGallery.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        destinationId: string;
        /**
         * Business information concerned by the ad platform, such as creative ideas、materials.
         *
         * @type { ?string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        serviceTag?: string;
        /**
         * Business scene.
         *
         * @type { ?number }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        businessScene?: number;
        /**
         * Trigger value.
         *
         * @type { number }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        triggerData: number;
        /**
         * Url where this postback will send to.
         *
         * @type { string }
         * @syscap SystemCapability.AppGalleryService.AttributionManager
         * @StageModelOnly
         * @since 5.0.0(12)
         */
        postbackUrl: string;
    }
    /**
     * Used to validate attribution source in the testing environment.
     *
     * @param { AdSourceInfo } adSourceInfo - Attribution source info to be validated in the testing environment.
     * @param { string } publicKey - The public key to validate signature.
     * @returns { Promise<void> } return value.
     * @throws { BusinessError } 401 - Parameter error.
     * @throws { BusinessError } 1009300001 - The specified service extension connect failed.
     * @throws { BusinessError } 1009300002 - System internal error.
     * @throws { BusinessError } 1009300101 - AdTechId is missing in the request.
     * @throws { BusinessError } 1009300102 - CampaignId is missing in the request.
     * @throws { BusinessError } 1009300104 - DestinationId is missing in the request.
     * @throws { BusinessError } 1009300105 - SourceType is missing in the request.
     * @throws { BusinessError } 1009300106 - Nonce is missing in the request.
     * @throws { BusinessError } 1009300107 - Timestamp is missing in the request.
     * @throws { BusinessError } 1009300108 - Signature is missing in the request.
     * @throws { BusinessError } 1009300111 - AdSourceInfo is missing in the request.
     * @throws { BusinessError } 1009300112 - PublicKey is missing in the request.
     * @throws { BusinessError } 1009300114 - The signature verification failed in the testing environment.
     * @syscap SystemCapability.AppGalleryService.AttributionManager
     * @StageModelOnly
     * @since 5.0.0(12)
     */
    function validateSource(adSourceInfo: AdSourceInfo, publicKey: string): Promise<void>;
    /**
     * Used to set attribution postback info in the testing environment.
     *
     * @param { PostbackInfo } postbackInfo - Postback info to be sent in the testing environment.
     * @returns { Promise<void> } return value.
     * @throws { BusinessError } 401 - Parameter error.
     * @throws { BusinessError } 1009300001 - The specified service extension connect failed.
     * @throws { BusinessError } 1009300002 - System internal error.
     * @throws { BusinessError } 1009300101 - AdTechId is missing in the request.
     * @throws { BusinessError } 1009300102 - CampaignId is missing in the request.
     * @throws { BusinessError } 1009300103 - SourceId is missing in the request.
     * @throws { BusinessError } 1009300104 - DestinationId is missing in the request.
     * @throws { BusinessError } 1009300109 - TriggerData is missing in the request.
     * @throws { BusinessError } 1009300110 - PostbackUrl is missing in the request.
     * @throws { BusinessError } 1009300113 - PostbackInfo is missing in the request.
     * @throws { BusinessError } 1009300115 - Too many postbacks setting to the testing environment.
     * @syscap SystemCapability.AppGalleryService.AttributionManager
     * @StageModelOnly
     * @since 5.0.0(12)
     */
    function setPostback(postbackInfo: PostbackInfo): Promise<void>;
    /**
     * Used to flush postbacks in the testing environment.
     *
     * @param { string } adTechId - AdTechId of whom the postbacks will be sent in the testing environment.
     * @returns { Promise<void> } return value.
     * @throws { BusinessError } 401 - Parameter error.
     * @throws { BusinessError } 1009300001 - The specified service extension connect failed.
     * @throws { BusinessError } 1009300002 - System internal error.
     * @throws { BusinessError } 1009300101 - AdTechId is missing in the request.
     * @throws { BusinessError } 1009300116 - There is no postback to be sent of this adTechId.
     * @throws { BusinessError } 1009300117 - Failed to send postbacks to the postbackUrl.
     * @throws { BusinessError } 1009300119 - Network error.
     * @throws { BusinessError } 1009300120 - Request too frequent.
     * @syscap SystemCapability.AppGalleryService.AttributionManager
     * @StageModelOnly
     * @since 5.0.0(12)
     */
    function flushPostbacks(adTechId: string): Promise<void>;
}
export default attributionTestManager;
