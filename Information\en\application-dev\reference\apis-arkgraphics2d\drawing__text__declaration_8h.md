# drawing_text_declaration.h


## Overview

The **drawing_text_declaration.h** file declares the structs related to text in 2D drawing.

**File to include**: &lt;native_drawing/drawing_text_declaration.h&gt;

**Library**: libnative_drawing.so

**Since**: 8

**Related module**: [Drawing](_drawing.md)


## Summary


### Types

| Name| Description| 
| -------- | -------- |
| typedef struct [OH_Drawing_FontCollection](_drawing.md#oh_drawing_fontcollection)  [OH_Drawing_FontCollection](_drawing.md#oh_drawing_fontcollection) | Defines a struct used to load fonts.| 
| typedef struct [OH_Drawing_Typography](_drawing.md#oh_drawing_typography)  [OH_Drawing_Typography](_drawing.md#oh_drawing_typography) | Defines a struct used to manage the typography layout and display.| 
| typedef struct [OH_Drawing_TextStyle](_drawing.md#oh_drawing_textstyle)  [OH_Drawing_TextStyle](_drawing.md#oh_drawing_textstyle) | Defines a struct used to manage text colors and decorations.| 
| typedef struct [OH_Drawing_TypographyStyle](_drawing.md#oh_drawing_typographystyle)  [OH_Drawing_TypographyStyle](_drawing.md#oh_drawing_typographystyle) | Defines a struct used to manage the typography style, such as the text direction.| 
| typedef struct [OH_Drawing_TypographyCreate](_drawing.md#oh_drawing_typographycreate)  [OH_Drawing_TypographyCreate](_drawing.md#oh_drawing_typographycreate) | Defines a struct used to create an [OH_Drawing_Typography](_drawing.md#oh_drawing_typography) object.| 
| typedef struct [OH_Drawing_TextBox](_drawing.md#oh_drawing_textbox)  [OH_Drawing_TextBox](_drawing.md#oh_drawing_textbox) | Defines a struct used to receive the rectangle size, direction, and quantity of text boxes.| 
| typedef struct [OH_Drawing_PositionAndAffinity](_drawing.md#oh_drawing_positionandaffinity)  [OH_Drawing_PositionAndAffinity](_drawing.md#oh_drawing_positionandaffinity) | Defines a struct used to receive the position and affinity of a glyph.| 
| typedef struct [OH_Drawing_Range](_drawing.md#oh_drawing_range)  [OH_Drawing_Range](_drawing.md#oh_drawing_range) | Defines a struct used to receive the start position and end position of a glyph.| 
| typedef struct [OH_Drawing_TextShadow](_drawing.md#oh_drawing_textshadow)  [OH_Drawing_TextShadow](_drawing.md#oh_drawing_textshadow) | Defines a struct used to manage text shadows.| 
| typedef struct [OH_Drawing_FontParser](_drawing.md#oh_drawing_fontparser)  [OH_Drawing_FontParser](_drawing.md#oh_drawing_fontparser) | Defines a struct used to parse system font files.| 
