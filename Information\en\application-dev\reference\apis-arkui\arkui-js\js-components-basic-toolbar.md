# toolbar

>  **NOTE**
>
>  This component is supported since API version 5. Updates will be marked with a superscript to indicate their earliest API version.

The **\<toolbar>** component provides a bar that is usually placed at the bottom of a page to display operation options for the page.


## Required Permissions

None


## Child Components

Only the **\<toolbar-item>** component is supported.

>  **NOTE**
>
>  A maximum of five **\<toolbar-item>** child components can be displayed in a **\<toolbar>** component. If there are six or more toolbar items, the first four are displayed, and the rest items are added to the **More** list of the toolbar. Users can click **More** to view the items. The list is displayed in the default style instead of the custom style set for the \<toolbar-item> components.


## Attributes

The [universal attributes](js-components-common-attributes.md) are supported.


## Styles

The [universal styles](js-components-common-styles.md) are supported.

>  **NOTE**
>
>  The **height** style is not supported. The height is fixed at 56px.


## Events

Not supported


## Methods

Not supported


## Example

For details, see [Example in toolbar-item](js-components-basic-toolbar-item.md).
