"""
Async Manager for ArkTS Import Suggestion System

This module provides utilities for managing asynchronous operations in the ArkTS import
suggestion system. It includes classes and functions for handling event loops, timeouts,
and other aspects of asynchronous programming.
"""

import time
import logging
import asyncio
import warnings
import contextlib
import gc
import socket
import sys
from typing import Any, Callable, Coroutine, Dict, List, Optional, TypeVar, Union

# Configure logging
logger = logging.getLogger("AsyncManager")

# Suppress ResourceWarnings about unclosed sockets and transports
# This is a known issue with asyncio on Windows
warnings.filterwarnings("ignore", category=ResourceWarning, message="unclosed.*")

# Patch socket.socket to track all created sockets
if sys.platform == 'win32':
    # Keep track of created sockets
    _open_sockets = set()
    _original_socket = socket.socket

    def _patched_socket(*args, **kwargs):
        sock = _original_socket(*args, **kwargs)
        _open_sockets.add(sock)
        return sock

    socket.socket = _patched_socket

    def close_all_sockets():
        """Close all tracked sockets."""
        for sock in list(_open_sockets):
            try:
                if not sock._closed:
                    sock.close()
                _open_sockets.remove(sock)
            except Exception:
                pass

# Type variable for generic function return type
T = TypeVar('T')


class AsyncManager:
    """Manages async operations and event loops."""

    def __init__(self, default_timeout: float = 10.0):
        """Initialize the async manager.

        Args:
            default_timeout: Default timeout for async operations in seconds
        """
        self.default_timeout = default_timeout

    @staticmethod
    async def run_with_timeout(coro: Coroutine, timeout: float) -> Any:
        """Run an async coroutine with a timeout.

        Args:
            coro: Async coroutine to run
            timeout: Timeout in seconds

        Returns:
            Result of the coroutine

        Raises:
            asyncio.TimeoutError: If the coroutine times out
        """
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Async operation timed out after {timeout}s")
            raise

    @staticmethod
    def get_or_create_event_loop() -> asyncio.AbstractEventLoop:
        """Get the current event loop or create a new one if it doesn't exist or is closed.

        Returns:
            An event loop
        """
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                logger.info("Current event loop is closed, creating a new one")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            return loop
        except RuntimeError:
            logger.info("No current event loop, creating a new one")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop

    def run_coroutine(self, coro: Coroutine, timeout: Optional[float] = None) -> Any:
        """Run a coroutine in the current event loop or a new one.

        Args:
            coro: Async coroutine to run
            timeout: Timeout in seconds (defaults to self.default_timeout)

        Returns:
            Result of the coroutine
        """
        if timeout is None:
            timeout = self.default_timeout

        # For consistency and to avoid issues, always use a new loop
        return self.run_in_new_loop(coro, timeout)

    def run_coroutines(self, coroutines: List[Coroutine], timeout: Optional[float] = None) -> List[Any]:
        """Run multiple coroutines concurrently.

        Args:
            coroutines: List of async coroutines to run
            timeout: Timeout in seconds (defaults to self.default_timeout)

        Returns:
            List of results from the coroutines
        """
        if timeout is None:
            timeout = self.default_timeout

        async def run_all():
            return await asyncio.gather(*coroutines, return_exceptions=True)

        # Use the context manager to handle event loop lifecycle
        with self.event_loop_context() as loop:
            try:
                # Create a task for the coroutines
                task = loop.create_task(self.run_with_timeout(run_all(), timeout))

                # Run the task to completion
                return loop.run_until_complete(task)
            except Exception as e:
                logger.error(f"Error running coroutines: {str(e)}")
                raise

    def run_in_new_loop(self, coro: Coroutine, timeout: Optional[float] = None) -> Any:
        """Run a coroutine in a new event loop.

        This is useful when running in a context where the current event loop
        might be in use or closed.

        Args:
            coro: Async coroutine to run
            timeout: Timeout in seconds (defaults to self.default_timeout)

        Returns:
            Result of the coroutine
        """
        if timeout is None:
            timeout = self.default_timeout

        # Use the context manager to handle event loop lifecycle
        with self.event_loop_context() as loop:
            try:
                # Set up a task to run the coroutine with timeout
                task = loop.create_task(self.run_with_timeout(coro, timeout))

                # Run the task to completion
                return loop.run_until_complete(task)
            except Exception as e:
                logger.error(f"Error running coroutine in new loop: {str(e)}")
                raise

    @staticmethod
    async def with_retry(coro_func: Callable[..., Coroutine],
                        max_retries: int = 3,
                        retry_delay: float = 1.0,
                        *args: Any,
                        **kwargs: Any) -> Any:
        """Run a coroutine with retry logic.

        Args:
            coro_func: Async function that returns a coroutine
            max_retries: Maximum number of retries
            retry_delay: Initial delay between retries in seconds (will be increased exponentially)
            *args: Arguments to pass to the coroutine function
            **kwargs: Keyword arguments to pass to the coroutine function

        Returns:
            Result of the coroutine

        Raises:
            Exception: The last exception encountered after all retries
        """
        last_exception = None

        for attempt in range(1, max_retries + 1):
            try:
                return await coro_func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                logger.warning(f"Attempt {attempt}/{max_retries} failed: {str(e)}")

                if attempt < max_retries:
                    retry_delay_current = retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                    logger.info(f"Retrying in {retry_delay_current:.2f} seconds...")
                    await asyncio.sleep(retry_delay_current)
                else:
                    logger.error(f"All {max_retries} attempts failed")
                    raise last_exception

    @staticmethod
    def is_event_loop_running() -> bool:
        """Check if an event loop is running.

        Returns:
            True if an event loop is running, False otherwise
        """
        try:
            loop = asyncio.get_event_loop()
            return loop.is_running()
        except RuntimeError:
            return False

    @contextlib.contextmanager
    def event_loop_context(self):
        """Context manager for event loops.

        This ensures that the event loop is properly closed and resources are cleaned up.

        Yields:
            An event loop
        """
        # Create a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            # Yield the loop for use
            yield loop
        finally:
            # Clean up resources
            try:
                # Cancel all pending tasks
                pending = asyncio.all_tasks(loop)
                if pending:
                    for task in pending:
                        task.cancel()

                    # Run the event loop until all tasks are cancelled
                    loop.run_until_complete(
                        asyncio.gather(*pending, return_exceptions=True)
                    )

                # Close the loop
                loop.run_until_complete(loop.shutdown_asyncgens())

                # Close transports
                for transport in getattr(loop, "_transports", set()):
                    if hasattr(transport, "close"):
                        transport.close()

                # Close all sockets on Windows
                if sys.platform == 'win32':
                    close_all_sockets()

                # Force garbage collection to clean up any remaining resources
                gc.collect()
            except Exception as e:
                logger.warning(f"Error cleaning up event loop: {str(e)}")
            finally:
                loop.close()


# Create a global instance for convenience
async_manager = AsyncManager()
