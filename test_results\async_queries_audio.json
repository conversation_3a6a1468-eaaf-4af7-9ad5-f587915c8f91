{"sync_time": 2.1235365867614746, "async_time": 0.35169124603271484, "sync_results": [{"symbol_name": "audio", "symbol_type": "namespace", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12", "import_statement": "import { audio } from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.6787143}, {"symbol_name": "AudioR<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12", "import_statement": "import { audio.AudioRenderer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioRenderer", "score": 0.6183092}, {"symbol_name": "AudioStreamInfo", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12", "import_statement": "import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioStreamInfo", "score": 0.6171435}], "async_results": [{"symbol_name": "audio", "symbol_type": "namespace", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12", "import_statement": "import { audio } from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.77510001, "vector_score": 0.6787143, "text_score": 1.0}, {"symbol_name": "AudioR<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12", "import_statement": "import { audio.AudioRenderer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioRenderer", "score": 0.7328164399999999, "vector_score": 0.6183092, "text_score": 1.0}, {"symbol_name": "AudioStreamInfo", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12", "import_statement": "import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioStreamInfo", "score": 0.7320004499999999, "vector_score": 0.6171435, "text_score": 1.0}]}