/**
 * Provides an image component.
 * @component Image
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
@Component
export struct Image {
  /**
   * Creates an image component.
   * @param src Image source.
   */
  constructor(src?: string);

  /**
   * Sets the image source.
   * @param src Image source.
   * @returns This Image component.
   */
  src(src: string): Image;

  /**
   * Sets the image alternative text.
   * @param alt Image alternative text.
   * @returns This Image component.
   */
  alt(alt: string): Image;

  /**
   * Sets the image object fit.
   * @param fit Image object fit.
   * @returns This Image component.
   */
  objectFit(fit: ImageFit): Image;

  /**
   * Sets the image rendering mode.
   * @param mode Image rendering mode.
   * @returns This Image component.
   */
  renderMode(mode: ImageRenderMode): Image;

  /**
   * Sets the image interpolation mode.
   * @param mode Image interpolation mode.
   * @returns This Image component.
   */
  interpolation(mode: ImageInterpolation): Image;

  /**
   * Sets the image repeat mode.
   * @param repeat Image repeat mode.
   * @returns This Image component.
   */
  objectRepeat(repeat: ImageRepeat): Image;

  /**
   * Sets the image border radius.
   * @param radius Image border radius.
   * @returns This Image component.
   */
  borderRadius(radius: number | string): Image;

  /**
   * Sets the image loading event handler.
   * @param callback Image loading event handler.
   * @returns This Image component.
   */
  onLoad(callback: (event: { width: number, height: number }) => void): Image;

  /**
   * Sets the image error event handler.
   * @param callback Image error event handler.
   * @returns This Image component.
   */
  onError(callback: () => void): Image;

  /**
   * Sets the image complete event handler.
   * @param callback Image complete event handler.
   * @returns This Image component.
   */
  onComplete(callback: () => void): Image;
}

/**
 * Enum for image fit.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum ImageFit {
  /**
   * Fill the image.
   */
  Fill,

  /**
   * Contain the image.
   */
  Contain,

  /**
   * Cover the image.
   */
  Cover,

  /**
   * Scale down the image.
   */
  ScaleDown,

  /**
   * Use the original image size.
   */
  None
}

/**
 * Enum for image rendering mode.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum ImageRenderMode {
  /**
   * Original rendering mode.
   */
  Original,

  /**
   * Template rendering mode.
   */
  Template
}

/**
 * Enum for image interpolation mode.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum ImageInterpolation {
  /**
   * None interpolation mode.
   */
  None,

  /**
   * Low interpolation mode.
   */
  Low,

  /**
   * Medium interpolation mode.
   */
  Medium,

  /**
   * High interpolation mode.
   */
  High
}

/**
 * Enum for image repeat mode.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum ImageRepeat {
  /**
   * No repeat.
   */
  NoRepeat,

  /**
   * Repeat in x direction.
   */
  X,

  /**
   * Repeat in y direction.
   */
  Y,

  /**
   * Repeat in both directions.
   */
  XY
}

export { Image, ImageFit, ImageRenderMode, ImageInterpolation, ImageRepeat };
