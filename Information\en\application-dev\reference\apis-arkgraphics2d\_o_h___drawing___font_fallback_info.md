# OH_Drawing_FontFallbackInfo


## Overview

The OH_Drawing_FontFallbackInfo struct describes the information about a font fallback.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| char \* [language](#language) | Pointer to the language supported by the font fallback. The language format is bcp47. | 
| char \* [familyName](#familyname) | Pointer to the name of a font family. | 


## Member Variable Description


### familyName

```
char* OH_Drawing_FontFallbackInfo::familyName
```
**Description**

Pointer to the name of a font family.


### language

```
char* OH_Drawing_FontFallbackInfo::language
```
**Description**

Pointer to the language supported by the font fallback. The language format is bcp47.
