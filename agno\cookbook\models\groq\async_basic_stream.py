import asyncio

from agno.agent import Agent
from agno.models.groq import Groq

agent = Agent(
    model=Groq(id="llama-3.3-70b-versatile"),
    description="You help people with their health and fitness goals.",
    instructions=["Recipes should be under 5 ingredients"],
)
# -*- Print a response to the terminal
asyncio.run(
    agent.aprint_response("Share a breakfast recipe.", markdown=True, stream=True)
)
