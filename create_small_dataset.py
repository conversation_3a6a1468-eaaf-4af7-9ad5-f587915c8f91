"""
Create a small test dataset for ArkTS import suggestion system.
This script creates a smaller dataset with only essential files.
"""

import os
import shutil
import glob
from pathlib import Path

def create_directory(directory):
    """Create a directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def copy_file(source, destination):
    """Copy a file from source to destination."""
    try:
        shutil.copy2(source, destination)
        print(f"Copied: {source} -> {destination}")
        return True
    except Exception as e:
        print(f"Error copying {source}: {e}")
        return False

def main():
    """Main function."""
    # Create test dataset directory
    test_dataset_dir = "small_dataset"
    create_directory(test_dataset_dir)
    
    # Copy our custom component files
    custom_files = [
        "test_dataset/button_component.d.ets",
        "test_dataset/dialog_component.d.ets",
        "test_dataset/text.d.ets",
        "test_dataset/image.d.ets"
    ]
    
    copied_files = []
    for file in custom_files:
        if os.path.exists(file):
            destination = os.path.join(test_dataset_dir, os.path.basename(file))
            if copy_file(file, destination):
                copied_files.append(destination)
    
    # Add a few more essential files
    essential_files = [
        "Information/default/openharmony/ets/component/button.d.ts",
        "Information/default/openharmony/ets/api/arkui/ButtonModifier.d.ts",
        "Information/default/openharmony/ets/api/@ohos.arkui.advanced.ProgressButton.d.ets",
        "Information/default/openharmony/ets/api/@ohos.arkui.advanced.Dialog.d.ets",
        "Information/default/openharmony/ets/component/alert_dialog.d.ts",
        "Information/default/openharmony/ets/component/custom_dialog_controller.d.ts",
        "Information/default/openharmony/ets/api/@ohos.multimedia.audio.d.ts",
        "Information/default/openharmony/ets/api/@ohos.net.http.d.ts",
        "Information/default/openharmony/ets/api/@ohos.bluetooth.d.ts",
    ]
    
    for file in essential_files:
        if os.path.exists(file):
            destination = os.path.join(test_dataset_dir, os.path.basename(file))
            if copy_file(file, destination):
                copied_files.append(destination)
    
    # Create a list of copied files
    with open(os.path.join(test_dataset_dir, "files.txt"), "w") as f:
        for file in copied_files:
            f.write(f"{file}\n")
    
    print(f"\nCreated small test dataset with {len(copied_files)} files.")
    print(f"Files list saved to {os.path.join(test_dataset_dir, 'files.txt')}")

if __name__ == "__main__":
    main()
