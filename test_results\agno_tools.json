{"component": "# Component Search Results\n\n## 1. Button\n**Type:** component\n**Import:** `import { Button } from 'button_component';`\n**Relevance Score:** 0.7793\n\nProvides a button component. @component Button @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @since 7\n\n**Usage Example:**\n```typescript\nimport { Button } from 'button_component'\n\n@Entry\n@Component\nstruct MyComponent {\n  build() {\n    Button()\n  }\n}\n```\n\n---\n", "import_path": "No results found.", "api": "# ArkTS Search Results\n\n## Namespace Results\n\n### 1. bluetooth\n**Module:** @ohos.bluetooth\n**Import:** `import { bluetooth } from '@ohos.bluetooth';`\n**Relevance Score:** 0.7884\n\nProvides methods to operate or manage Bluetooth. @namespace bluetooth @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager\n\n---\n\n### 2. BLE\n**Module:** @ohos.bluetooth\n**Import:** `import { bluetooth.BLE } from '@ohos.bluetooth';`\n**Relevance Score:** 0.5863\n\n**Nested in:** `bluetooth`\n\nProvides methods to operate or manage Bluetooth. @namespace BLE @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.BL...\n\n---\n\n\n\n## Interface Results\n\n### 1. DeviceClass\n**Module:** @ohos.bluetooth\n**Import:** `import { bluetooth.DeviceClass } from '@ohos.bluetooth';`\n**Relevance Score:** 0.5617\n\n**Nested in:** `bluetooth`\n\nDescribes the class of a bluetooth device. @typedef DeviceClass @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.De...\n\n---\n\n\n", "agent": "No results found."}