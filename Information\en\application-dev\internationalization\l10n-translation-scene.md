# Scene and Context Clarification for Translation

The translation results of the same content may vary greatly in different scenes and contexts. When providing UI strings for translation, clarifying the scene and context can help to avoid translation errors. 

Translation scene information is usually provided in two ways:

- Use string resource files for commenting or annotation, including the context, part-of-speech, keyword meaning, maximum text length allowed by a control, and meaning and value range of a variable.
- Provide screenshots.
