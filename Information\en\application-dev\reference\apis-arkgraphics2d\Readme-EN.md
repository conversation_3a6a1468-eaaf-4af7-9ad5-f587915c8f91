# ArkGraphics 2D

- ArkTS APIs
  - [@ohos.effectKit (Image Effects)](js-apis-effectKit.md)
  - [@ohos.graphics.colorSpaceManager (Color Space Management)](js-apis-colorSpaceManager.md)
  - [@ohos.graphics.sendableColorSpaceManager (Sendable Color Space Management)](js-apis-sendableColorSpaceManager.md)
  - [@ohos.graphics.common2D (Common Data Types of 2D Graphics)](js-apis-graphics-common2D.md)
  - [@ohos.graphics.displaySync (Variable Frame Rate)](js-apis-graphics-displaySync.md)
  - [@ohos.graphics.drawing (Drawing)](js-apis-graphics-drawing.md)
  - [@ohos.graphics.hdrCapability (HDR Capability)](js-apis-hdrCapability.md)
  - [@ohos.graphics.text (Text)](js-apis-graphics-text.md)
  - [@ohos.graphics.uiEffect (Cascading Effect)](js-apis-uiEffect.md)
  - [@ohos.graphics.uiEffect (Cascading Effect) (System API)](js-apis-uiEffect-sys.md)
- C APIs
  - Modules
    - [Drawing](_drawing.md)
    - [EffectKit](effect_kit.md)
    - [NativeDisplaySoloist](_native_display_soloist.md)
    - [NativeVsync](_native_vsync.md)
    - [NativeWindow](_native_window.md)
    - [OH_NativeBuffer](_o_h___native_buffer.md)
    - [OH_NativeImage](_o_h___native_image.md)
    - [NativeColorSpaceManager](_native_color_space_manager.md)
  - Header Files
    - [drawing_bitmap.h](drawing__bitmap_8h.md)
    - [drawing_brush.h](drawing__brush_8h.md)
    - [drawing_canvas.h](drawing__canvas_8h.md)
    - [drawing_color.h](drawing__color_8h.md)
    - [drawing_color_filter.h](drawing__color__filter_8h.md)
    - [drawing_color_space.h](drawing__color__space_8h.md)
    - [drawing_error_code.h](drawing__error__code_8h.md)
    - [drawing_filter.h](drawing__filter_8h.md)
    - [drawing_font.h](drawing__font_8h.md)
    - [drawing_font_collection.h](drawing__font__collection_8h.md)
    - [drawing_font_mgr.h](drawing__font__mgr_8h.md)
    - [drawing_gpu_context.h](drawing__gpu__context_8h.md)
    - [drawing_image.h](drawing__image_8h.md)
    - [drawing_image_filter.h](drawing__image__filter_8h.md)
    - [drawing_mask_filter.h](drawing__mask__filter_8h.md)
    - [drawing_matrix.h](drawing__matrix_8h.md)
    - [drawing_memory_stream.h](drawing__memory__stream_8h.md)
    - [drawing_path.h](drawing__path_8h.md)
    - [drawing_path_effect.h](drawing__path__effect_8h.md)
    - [drawing_pen.h](drawing__pen_8h.md)
    - [drawing_pixel_map.h](drawing__pixel__map_8h.md)
    - [drawing_point.h](drawing__point_8h.md)
    - [drawing_record_cmd.h](drawing__record__cmd_8h.md)
    - [drawing_rect.h](drawing__rect_8h.md)
    - [drawing_region.h](drawing__region_8h.md)
    - [drawing_register_font.h](drawing__register__font_8h.md)
    - [drawing_round_rect.h](drawing__round__rect_8h.md)
    - [drawing_sampling_options.h](drawing__sampling__options_8h.md)
    - [drawing_shader_effect.h](drawing__shader__effect_8h.md)
    - [drawing_shadow_layer.h](drawing__shadow__layer_8h.md)
    - [drawing_surface.h](drawing__surface_8h.md)
    - [drawing_text_blob.h](drawing__text__blob_8h.md)
    - [drawing_text_declaration.h](drawing__text__declaration_8h.md)
    - [drawing_text_typography.h](drawing__text__typography_8h.md)
    - [drawing_typeface.h](drawing__typeface_8h.md)
    - [drawing_types.h](drawing__types_8h.md)
    - [effect_filter.h](effect__filter_8h.md)
    - [effect_types.h](effect__types_8h.md)
    - [external_window.h](external__window_8h.md)
    - [native_buffer.h](native__buffer_8h.md)
    - [native_display_soloist.h](native__display__soloist_8h.md)
    - [native_image.h](native__image_8h.md)
    - [native_vsync.h](native__vsync_8h.md)
    - [native_color_space_manager.h](native__color__space__manager_8h.md)
  - Structs
    - [DisplaySoloist_ExpectedRateRange](_display_soloist___expected_rate_range.md)
    - [OH_Drawing_BitmapFormat](_o_h___drawing___bitmap_format.md)
    - [OH_Drawing_Font_Metrics](_o_h___drawing___font___metrics.md)
    - [OH_Drawing_FontAdjustInfo](_o_h___drawing___font_adjust_info.md)
    - [OH_Drawing_FontAliasInfo](_o_h___drawing___font_alias_info.md)
    - [OH_Drawing_FontConfigInfo](_o_h___drawing___font_config_info.md)
    - [OH_Drawing_FontDescriptor](_o_h___drawing___font_descriptor.md)
    - [OH_Drawing_FontFallbackGroup](_o_h___drawing___font_fallback_group.md)
    - [OH_Drawing_FontFallbackInfo](_o_h___drawing___font_fallback_info.md)
    - [OH_Drawing_FontFeature](_o_h___drawing___font_feature.md)
    - [OH_Drawing_FontGenericInfo](_o_h___drawing___font_generic_info.md)
    - [OH_Drawing_FontStyleStruct](_o_h___drawing___font_style_struct.md)
    - [OH_Drawing_GpuContextOptions](_o_h___drawing___gpu_context_options.md)
    - [OH_Drawing_Image_Info](_o_h___drawing___image___info.md)
    - [OH_Drawing_LineMetrics](_o_h___drawing___line_metrics.md)
    - [OH_Drawing_PlaceholderSpan](_o_h___drawing___placeholder_span.md)
    - [OH_Drawing_Point2D](_o_h___drawing___point2_d.md)
    - [OH_Drawing_Point3D](_o_h___drawing___point3_d.md)
    - [OH_Drawing_RectStyle_Info](_o_h___drawing___rect_style___info.md)
    - [OH_Drawing_RunBuffer](_o_h___drawing___run_buffer.md) 
    - [OH_Drawing_StrutStyle](_o_h___drawing___strut_style.md)
    - [OH_Filter](_o_h___filter.md)
    - [OH_Filter_ColorMatrix](_o_h___filter___color_matrix.md)
    - [OH_NativeBuffer_ColorXY](_o_h___native_buffer___color_x_y.md)
    - [OH_NativeBuffer_Config](_o_h___native_buffer___config.md)
    - [OH_NativeBuffer_Cta861](_o_h___native_buffer___cta861.md)
    - [OH_NativeBuffer_Plane](_o_h___native_buffer___plane.md)
    - [OH_NativeBuffer_Planes](_o_h___native_buffer___planes.md)
    - [OH_NativeBuffer_Smpte2086](_o_h___native_buffer___smpte2086.md)
    - [OH_NativeBuffer_StaticMetadata](_o_h___native_buffer___static_metadata.md)
    - [OH_OnFrameAvailableListener](_o_h___on_frame_available_listener.md)
    - [OHExtDataHandle](_o_h_ext_data_handle.md)
    - [OHHDRMetaData](_o_h_h_d_r_meta_data.md)
    - [Region](_region.md)
    - [Rect](_rect.md)
    - [ColorSpacePrimaries](_color_space_primaries.md)
    - [WhitePointArray](_white_point_array.md)
- Error Codes
  - [colorSpaceManager Error Codes](errorcode-colorspace-manager.md)
