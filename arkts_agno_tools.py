"""
ArkTS Import Tools for Agno Agents

This module provides a toolkit for Agno agents to search for ArkTS imports.
It integrates with the Agno framework and provides tools for searching components,
import paths, and general ArkTS APIs.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union, Callable

# Import ArkTS query and formatter
from arkts_query_async import ArkTSQ<PERSON>yAsync
from arkts_formatter import Ark<PERSON><PERSON><PERSON>atter

# Import utility classes
from async_manager import AsyncManager
from error_handler import <PERSON><PERSON>r<PERSON><PERSON><PERSON>
from performance_optimizer import PerformanceOptimizer

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSImportTools")

# Global instance for Agno tools
_arkts_tools_instance = None

# Try to import Agno tools
try:
    from agno.tools import Toolkit, tool
    AGNO_AVAILABLE = True
except ImportError:
    logger.warning("Agno framework not found. Using mock implementation.")
    AGNO_AVAILABLE = False

    # Mock implementation for testing without Agno
    class tool:
        def __init__(self, **kwargs):
            self.kwargs = kwargs

        def __call__(self, func):
            func._tool_config = self.kwargs
            return func

    class Toolkit:
        def __init__(self, **kwargs):
            self.kwargs = kwargs


class ArkTSImportTools(Toolkit):
    """ArkTS Import Tools for Agno Agents."""

    def __init__(
        self,
        qdrant_url: Optional[str] = None,
        collection_name: Optional[str] = None,
        ollama_url: Optional[str] = None,
        embedding_model: Optional[str] = None,
        cache_results: bool = True,
        cache_ttl: int = 3600,
        cache_max_size: int = 1000,
        reranking_weight: float = 0.5,
        max_description_length: int = 200,
        include_scores: bool = True,
        show_result_tools: Optional[List[str]] = None,
        stop_after_tool_call_tools: Optional[List[str]] = None,
        **kwargs
    ):
        """Initialize the ArkTS Import Tools.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
            cache_results: Whether to cache results
            cache_ttl: Time-to-live for cache entries in seconds
            cache_max_size: Maximum number of entries in the cache
            reranking_weight: Weight for semantic vs. text match in reranking
            max_description_length: Maximum length for descriptions in formatted output
            include_scores: Whether to include scores in formatted output
            show_result_tools: List of tools to show results for
            stop_after_tool_call_tools: List of tools to stop after calling
            **kwargs: Additional arguments for the Toolkit
        """
        # Initialize the query engine
        self.query_engine = ArkTSQueryAsync(
            qdrant_url=qdrant_url,
            collection_name=collection_name,
            ollama_url=ollama_url,
            embedding_model=embedding_model,
            cache_ttl=cache_ttl,
            cache_max_size=cache_max_size,
            reranking_weight=reranking_weight
        )

        # Initialize the formatter
        self.formatter = ArkTSFormatter(
            max_description_length=max_description_length,
            include_scores=include_scores
        )

        # Default tool settings
        if show_result_tools is None:
            show_result_tools = ["search_component", "search_import_path", "search_arkts_api"]

        if stop_after_tool_call_tools is None:
            stop_after_tool_call_tools = []

        # Initialize the toolkit without tools first
        super().__init__(
            name="arkts_import_tools",
            show_result_tools=show_result_tools,
            stop_after_tool_call_tools=stop_after_tool_call_tools,
            cache_results=cache_results,
            **kwargs
        )

        # Set global instance
        global _arkts_tools_instance
        _arkts_tools_instance = self

        logger.info(f"ArkTSImportTools initialized with Qdrant at {qdrant_url} and Ollama at {ollama_url}")

    def _run_async(self, coro):
        """Run an async coroutine and return the result.

        Args:
            coro: Async coroutine to run

        Returns:
            Result of the coroutine
        """
        try:
            # Use AsyncManager to run the coroutine in a new event loop
            # This handles all the event loop management and error handling
            async_mgr = AsyncManager(default_timeout=60.0)
            return async_mgr.run_in_new_loop(coro)
        except Exception as e:
            logger.error(f"Error running async coroutine: {str(e)}")
            # For testing purposes, if it's not a coroutine, just return it
            if not asyncio.iscoroutine(coro):
                return coro

            # If async fails, try to run a similar synchronous method
            logger.info("Async operation failed, falling back to synchronous operation")
            try:
                # Try to determine the synchronous equivalent method
                if hasattr(self.query_engine, coro.__qualname__.split('.')[0]):
                    # Get the method name without the 'async' suffix
                    method_name = coro.__qualname__.split('.')[-1]
                    if method_name.endswith('_async'):
                        sync_method_name = method_name[:-6]  # Remove '_async' suffix
                        if hasattr(self.query_engine, sync_method_name):
                            # Get the arguments from the coroutine
                            import inspect
                            args = inspect.getcoroutinelocals(coro)
                            if args and len(args) > 1:
                                # Call the synchronous method with the same arguments
                                sync_method = getattr(self.query_engine, sync_method_name)
                                return sync_method(*args[1:])  # Skip 'self' argument
            except Exception as inner_e:
                logger.error(f"Error in fallback to synchronous operation: {str(inner_e)}")

            # If all else fails, raise the original exception
            raise

    def search_component(self, component_name: str, limit: int = 5) -> str:
        """Search for ArkTS components by name.

        Args:
            component_name: Name of the component to search for
            limit: Maximum number of results to return

        Returns:
            Formatted search results
        """
        logger.info(f"Searching for component: {component_name}")

        try:
            # Run async search
            results = self._run_async(
                self.query_engine.search_component_async(component_name, limit)
            )

            # Format results
            formatted = self._format_component_results(results)

            logger.info(f"Found {len(results)} components matching '{component_name}'")
            return formatted

        except Exception as e:
            error_msg = f"Error searching for component {component_name}: {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"

    @tool(
        name="search_import_path",
        description="Search for import paths for ArkTS symbols. Args: symbol_name (str): Name of the symbol to find import paths for, limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
    )
    def search_import_path(symbol_name: str, limit: int = 5) -> str:
        """Search for import paths for ArkTS symbols.

        Args:
            symbol_name: Name of the symbol to find import paths for
            limit: Maximum number of results to return

        Returns:
            Formatted search results
        """
        logger.info(f"Searching for import paths for: {symbol_name}")

        try:
            # Get global instance
            global _arkts_tools_instance
            if _arkts_tools_instance is None:
                return "Error: ArkTS tools not initialized"

            # Run async search
            results = _arkts_tools_instance._run_async(
                _arkts_tools_instance.query_engine.search_import_path_async(symbol_name, limit)
            )

            # Format results
            formatted = _arkts_tools_instance._format_import_path_results(results)

            logger.info(f"Found {len(results)} import paths for '{symbol_name}'")
            return formatted

        except Exception as e:
            error_msg = f"Error searching for import paths for {symbol_name}: {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"

    @tool(
        name="search_arkts_api",
        description="Search for ArkTS APIs by query. Args: query (str): Query string to search for, limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
    )
    def search_arkts_api(query: str, limit: int = 5) -> str:
        """Search for ArkTS APIs by query.

        Args:
            query: Query string to search for
            limit: Maximum number of results to return

        Returns:
            Formatted search results
        """
        logger.info(f"Searching for ArkTS APIs: {query}")

        try:
            # Get global instance
            global _arkts_tools_instance
            if _arkts_tools_instance is None:
                return "Error: ArkTS tools not initialized"

            # Run async search
            results = _arkts_tools_instance._run_async(
                _arkts_tools_instance.query_engine.suggest_imports_async(query, limit)
            )

            # Format results
            formatted = _arkts_tools_instance._format_api_results(results)

            logger.info(f"Found {len(results)} APIs matching '{query}'")
            return formatted

        except Exception as e:
            error_msg = f"Error searching for ArkTS APIs with query '{query}': {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"

    @tool(
        name="handle_agent_query",
        description="Handle queries in the format used by Agno agents. Args: agent_query (str): Query string in the format 'ArkTS search: query', limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
    )
    def handle_agent_query(agent_query: str, limit: int = 5) -> str:
        """Handle queries in the format used by Agno agents.

        Args:
            agent_query: Query string in the format "ArkTS search: 'query'"
            limit: Maximum number of results to return

        Returns:
            Formatted search results
        """
        logger.info(f"Handling agent query: {agent_query}")

        try:
            # Get global instance
            global _arkts_tools_instance
            if _arkts_tools_instance is None:
                return "Error: ArkTS tools not initialized"

            # Run async search
            results = _arkts_tools_instance._run_async(
                _arkts_tools_instance.query_engine.handle_agent_query_async(agent_query, limit)
            )

            # Determine query type
            query_type = "general"
            if "component" in agent_query.lower():
                query_type = "component"
            elif "import path" in agent_query.lower():
                query_type = "import_path"

            # Format results
            formatted = _arkts_tools_instance.formatter.format_results_for_agent(results, query_type)

            logger.info(f"Found {len(results)} results for agent query: '{agent_query}'")
            return formatted

        except Exception as e:
            error_msg = f"Error handling agent query '{agent_query}': {str(e)}"
            logger.error(error_msg)
            return f"Error: {error_msg}"

    def _format_component_results(self, results: List[Dict[str, Any]]) -> str:
        """Format component search results.

        Args:
            results: List of component search results

        Returns:
            Formatted results as a string
        """
        return self.formatter.format_results_for_agent(results, "component")

    def _format_import_path_results(self, results: List[Dict[str, Any]]) -> str:
        """Format import path search results.

        Args:
            results: List of import path search results

        Returns:
            Formatted results as a string
        """
        return self.formatter.format_results_for_agent(results, "import_path")

    def _format_api_results(self, results: List[Dict[str, Any]]) -> str:
        """Format general API search results.

        Args:
            results: List of general API search results

        Returns:
            Formatted results as a string
        """
        return self.formatter.format_results_for_agent(results, "general")


# Standalone tool functions for Agno integration
@tool(
    name="search_component",
    description="Search for ArkTS components by name. Args: component_name (str): Name of the component to search for, limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
)
def search_component_tool(component_name: str, limit: int = 5) -> str:
    """Search for ArkTS components by name.

    Args:
        component_name: Name of the component to search for
        limit: Maximum number of results to return

    Returns:
        Formatted search results
    """
    logger.info(f"Searching for component: {component_name}")

    try:
        # Get global instance
        global _arkts_tools_instance
        if _arkts_tools_instance is None:
            return "Error: ArkTS tools not initialized"

        # Run async search
        results = _arkts_tools_instance._run_async(
            _arkts_tools_instance.query_engine.search_component_async(component_name, limit)
        )

        # Format results
        formatted = _arkts_tools_instance._format_component_results(results)

        logger.info(f"Found {len(results)} components matching '{component_name}'")
        return formatted

    except Exception as e:
        error_msg = f"Error searching for component {component_name}: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


@tool(
    name="search_import_path",
    description="Search for import paths for ArkTS symbols. Args: symbol_name (str): Name of the symbol to find import paths for, limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
)
def search_import_path_tool(symbol_name: str, limit: int = 5) -> str:
    """Search for import paths for ArkTS symbols.

    Args:
        symbol_name: Name of the symbol to find import paths for
        limit: Maximum number of results to return

    Returns:
        Formatted search results
    """
    logger.info(f"Searching for import paths for: {symbol_name}")

    try:
        # Get global instance
        global _arkts_tools_instance
        if _arkts_tools_instance is None:
            return "Error: ArkTS tools not initialized"

        # Run async search
        results = _arkts_tools_instance._run_async(
            _arkts_tools_instance.query_engine.search_import_path_async(symbol_name, limit)
        )

        # Format results
        formatted = _arkts_tools_instance._format_import_path_results(results)

        logger.info(f"Found {len(results)} import paths for '{symbol_name}'")
        return formatted

    except Exception as e:
        error_msg = f"Error searching for import paths for {symbol_name}: {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


@tool(
    name="search_arkts_api",
    description="Search for ArkTS APIs by query. Args: query (str): Query string to search for, limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
)
def search_arkts_api_tool(query: str, limit: int = 5) -> str:
    """Search for ArkTS APIs by query.

    Args:
        query: Query string to search for
        limit: Maximum number of results to return

    Returns:
        Formatted search results
    """
    logger.info(f"Searching for ArkTS APIs: {query}")

    try:
        # Get global instance
        global _arkts_tools_instance
        if _arkts_tools_instance is None:
            return "Error: ArkTS tools not initialized"

        # Run async search
        results = _arkts_tools_instance._run_async(
            _arkts_tools_instance.query_engine.suggest_imports_async(query, limit)
        )

        # Format results
        formatted = _arkts_tools_instance._format_api_results(results)

        logger.info(f"Found {len(results)} APIs matching '{query}'")
        return formatted

    except Exception as e:
        error_msg = f"Error searching for ArkTS APIs with query '{query}': {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


@tool(
    name="handle_agent_query",
    description="Handle queries in the format used by Agno agents. Args: agent_query (str): Query string in the format 'ArkTS search: query', limit (int): Maximum number of results to return (default: 5). Returns: Formatted search results as string."
)
def handle_agent_query_tool(agent_query: str, limit: int = 5) -> str:
    """Handle queries in the format used by Agno agents.

    Args:
        agent_query: Query string in the format "ArkTS search: 'query'"
        limit: Maximum number of results to return

    Returns:
        Formatted search results
    """
    logger.info(f"Handling agent query: {agent_query}")

    try:
        # Get global instance
        global _arkts_tools_instance
        if _arkts_tools_instance is None:
            return "Error: ArkTS tools not initialized"

        # Run async search
        results = _arkts_tools_instance._run_async(
            _arkts_tools_instance.query_engine.handle_agent_query_async(agent_query, limit)
        )

        # Determine query type
        query_type = "general"
        if "component" in agent_query.lower():
            query_type = "component"
        elif "import path" in agent_query.lower():
            query_type = "import_path"

        # Format results
        formatted = _arkts_tools_instance.formatter.format_results_for_agent(results, query_type)

        logger.info(f"Found {len(results)} results for agent query: '{agent_query}'")
        return formatted

    except Exception as e:
        error_msg = f"Error handling agent query '{agent_query}': {str(e)}"
        logger.error(error_msg)
        return f"Error: {error_msg}"


# Add main function for command-line usage
def main():
    """Main function for testing the toolkit."""
    import argparse

    parser = argparse.ArgumentParser(description='ArkTS Import Tools for Agno Agents')
    parser.add_argument('--qdrant-url', type=str, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, help='Ollama embedding model to use')
    parser.add_argument('--component', type=str, help='Search for a specific component')
    parser.add_argument('--import-path', type=str, help='Search for import paths for a symbol')
    parser.add_argument('--query', type=str, help='Search for ArkTS APIs by query')
    parser.add_argument('--agent-query', type=str, help='Process an Agno agent query format')
    parser.add_argument('--limit', type=int, default=5, help='Maximum number of results')
    parser.add_argument('--cache-ttl', type=int, default=3600, help='Cache TTL in seconds')
    parser.add_argument('--cache-max-size', type=int, default=1000, help='Maximum cache size')
    parser.add_argument('--max-description', type=int, default=200, help='Maximum description length')
    parser.add_argument('--no-scores', action='store_true', help='Do not include scores in output')

    args = parser.parse_args()

    # Initialize toolkit
    toolkit = ArkTSImportTools(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model,
        cache_ttl=args.cache_ttl,
        cache_max_size=args.cache_max_size,
        max_description_length=args.max_description,
        include_scores=not args.no_scores
    )

    # Process based on arguments
    if args.component:
        result = toolkit.search_component(args.component, args.limit)
        print(result)
    elif args.import_path:
        result = toolkit.search_import_path(args.import_path, args.limit)
        print(result)
    elif args.query:
        result = toolkit.search_arkts_api(args.query, args.limit)
        print(result)
    elif args.agent_query:
        result = toolkit.handle_agent_query(args.agent_query, args.limit)
        print(result)
    else:
        print("Please provide a search parameter (--component, --import-path, --query, or --agent-query).")
        print("Use --help for more information.")


if __name__ == "__main__":
    main()
