# OH_Drawing_Image_Info


## Overview

The **OH_Drawing_Image_Info** struct describes the image information.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| int32_t [width](#width) | Width, in px.| 
| int32_t [height](#height) | Height, in px.| 
| [OH_Drawing_ColorFormat](_drawing.md#oh_drawing_colorformat) [colorType](#colortype) | Color format. For details, see [OH_Drawing_ColorFormat](_drawing.md#oh_drawing_colorformat).| 
| [OH_Drawing_AlphaFormat](_drawing.md#oh_drawing_alphaformat) [alphaType](#alphatype) | Alpha format. For details, see [OH_Drawing_AlphaFormat](_drawing.md#oh_drawing_alphaformat).| 


## Member Variable Description


### alphaType

```
OH_Drawing_AlphaFormat OH_Drawing_Image_Info::alphaType
```

**Description**

Alpha format. For details, see [OH_Drawing_AlphaFormat](_drawing.md#oh_drawing_alphaformat).


### colorType

```
OH_Drawing_ColorFormat OH_Drawing_Image_Info::colorType
```

**Description**

Color format. For details, see [OH_Drawing_ColorFormat](_drawing.md#oh_drawing_colorformat).


### height

```
int32_t OH_Drawing_Image_Info::height
```

**Description**

Height, in pixels.


### width

```
int32_t OH_Drawing_Image_Info::width
```

**Description**

Width, in pixels.
