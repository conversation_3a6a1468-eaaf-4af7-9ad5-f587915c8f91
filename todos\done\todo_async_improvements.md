# Asenkron İşlemlerin İyileştirilmesi Planı

## Sorun <PERSON>zi
- Mevcut asenkron işlemlerde "no current event loop in thread" hatası oluşuyor
- Event loop'un kapatılması durumunda yeni bir event loop oluşturma mantığı iyileştirilmeli
- Test ortamında asenkron işlemler düzgün çalışmıyor

## İyileştirme Adımları

### 1. Asenkron İşlem Yönetimi Sınıfı Oluşturma [TAMAMLANDI]
- `AsyncManager` sınıfı oluşturuldu
- Event loop yönetimi için merkezi bir yapı sağlandı

### 2. Event Loop Yönetiminin İyileştirilmesi ✅ COMPLETED
- [x] `arkts_query_async.py` dosyasında event loop yönetimini iyileştir ✅
  - [x] Event loop'un durumunu kontrol et (açık/kapalı) ✅
  - [x] Kapalıysa yeni bir event loop oluştur ✅
  - [x] İşlem tamamlandıktan sonra event loop'u kapat ✅
  - [x] Hata durumunda event loop'u düzgün şekilde kapat ✅

### 3. Asenkron İşlemlerde Hata Yönetimi ✅ COMPLETED
- [x] Asenkron işlemlerde try/except bloklarını iyileştir ✅
  - [x] Spesifik hata türlerini yakala (ConnectionError, TimeoutError, vb.) ✅
  - [x] Hata durumunda senkron işlemlere geçiş mantığını iyileştir ✅
  - [x] Hata mesajlarını daha açıklayıcı hale getir ✅

### 4. Asenkron İşlemlerde Timeout Yönetimi ✅ COMPLETED
- [x] Asenkron işlemlerde timeout mekanizması ekle ✅
  - [x] `asyncio.wait_for()` kullanarak timeout ekle ✅
  - [x] Timeout durumunda senkron işlemlere geçiş yap ✅
  - [x] Timeout sürelerini yapılandırılabilir hale getir ✅

### 5. Asenkron İşlemlerde Önbellek Yönetimi ✅ COMPLETED
- [x] Asenkron işlemlerde önbellek kullanımını iyileştir ✅
  - [x] Önbellek anahtarlarını daha etkili hale getir ✅
  - [x] Önbellek TTL (Time To Live) değerlerini optimize et ✅
  - [x] Önbellek boyutunu yapılandırılabilir hale getir ✅

### 6. Test Ortamında Asenkron İşlemlerin İyileştirilmesi ✅ COMPLETED
- [x] Test ortamında asenkron işlemleri düzgün çalıştırmak için iyileştirmeler yap ✅
  - [x] Test ortamında event loop yönetimini iyileştir ✅
  - [x] Test ortamında mock event loop kullan ✅
  - [x] Test ortamında asenkron işlemleri senkron işlemlere dönüştür ✅

### 7. Asenkron İşlemlerde Loglama ✅ COMPLETED
- [x] Asenkron işlemlerde loglama mekanizmasını iyileştir ✅
  - [x] Asenkron işlemlerin başlangıç ve bitiş zamanlarını logla ✅
  - [x] Hata durumlarını detaylı şekilde logla ✅
  - [x] Performans metriklerini logla ✅

## Örnek Kod

```python
class AsyncManager:
    """Manages async operations and event loops."""

    @staticmethod
    async def run_with_timeout(coro, timeout=10):
        """Run an async coroutine with a timeout.

        Args:
            coro: Async coroutine to run
            timeout: Timeout in seconds

        Returns:
            Result of the coroutine

        Raises:
            asyncio.TimeoutError: If the coroutine times out
        """
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Async operation timed out after {timeout}s")
            raise

    @staticmethod
    def get_or_create_event_loop():
        """Get the current event loop or create a new one if it doesn't exist or is closed.

        Returns:
            An event loop
        """
        try:
            loop = asyncio.get_event_loop()
            if loop.is_closed():
                logger.info("Current event loop is closed, creating a new one")
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            return loop
        except RuntimeError:
            logger.info("No current event loop, creating a new one")
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            return loop

    @staticmethod
    def run_coroutine(coro, timeout=10):
        """Run a coroutine in the current event loop or a new one.

        Args:
            coro: Async coroutine to run
            timeout: Timeout in seconds

        Returns:
            Result of the coroutine
        """
        loop = AsyncManager.get_or_create_event_loop()
        try:
            return loop.run_until_complete(
                AsyncManager.run_with_timeout(coro, timeout)
            )
        except Exception as e:
            logger.error(f"Error running coroutine: {str(e)}")
            raise
```

## Beklenen Sonuçlar
- Asenkron işlemlerde "no current event loop in thread" hatası ortadan kalkacak
- Event loop'un kapatılması durumunda yeni bir event loop oluşturma mantığı iyileşecek
- Test ortamında asenkron işlemler düzgün çalışacak
- Asenkron işlemlerde hata yönetimi daha güvenilir olacak
- Asenkron işlemlerde timeout yönetimi daha etkili olacak
- Asenkron işlemlerde önbellek kullanımı daha etkili olacak
- Asenkron işlemlerde loglama mekanizması daha detaylı olacak
