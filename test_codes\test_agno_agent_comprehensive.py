#!/usr/bin/env python3
"""
Comprehensive Agno Agent Test for ArkTS Import Estimator
This test validates all 4 main functions using 100+ real ArkTS components and APIs.
Tests are designed to be skeptical and thorough, using real files from Information/default/.
"""

import os
import sys
import logging
import time
import json
from typing import List, Dict, Any, Tuple
from dataclasses import dataclass

# Add current directory to path to import modules
sys.path.insert(0, os.getcwd())

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class TestCase:
    """Test case data structure."""
    name: str
    query: str
    expected_type: str
    expected_symbols: List[str]
    category: str
    description: str

class AgnoAgentTester:
    """Comprehensive tester for Agno Agent integration."""

    def __init__(self):
        """Initialize the tester."""
        self.tools = None
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0

        # Initialize tools
        self._initialize_tools()

        # Create test cases
        self.test_cases = self._create_comprehensive_test_cases()

    def _initialize_tools(self):
        """Initialize ArkTS Agno Tools."""
        try:
            from arkts_agno_tools import ArkTSImportTools, search_component_tool, search_import_path_tool, search_arkts_api_tool, handle_agent_query_tool

            # Initialize the toolkit instance (this sets the global instance)
            toolkit = ArkTSImportTools(
                qdrant_url="http://gmktec.ai-institute.uk:6333",
                collection_name="arkts_imports",
                ollama_url="http://lgpu2.ai-institute.uk:11434",
                embedding_model="mxbai-embed-large"
            )

            # Use standalone tool functions
            self.tools = type('Tools', (), {
                'search_component': search_component_tool,
                'search_import_path': search_import_path_tool,
                'search_arkts_api': search_arkts_api_tool,
                'handle_agent_query': handle_agent_query_tool
            })()

            logger.info("✅ ArkTS Agno Tools initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize ArkTS Agno Tools: {str(e)}")
            # Use mock for testing
            self.tools = MockArkTSImportTools()
            logger.warning("⚠️ Using mock tools for testing")

    def _create_comprehensive_test_cases(self) -> List[TestCase]:
        """Create comprehensive test cases based on real ArkTS files."""

        test_cases = []

        # 1. UI COMPONENTS (30 test cases)
        ui_components = [
            ("Button", "button", ["Button"], "Basic clickable button component"),
            ("Text", "text", ["Text"], "Text display component"),
            ("Image", "image", ["Image"], "Image display component"),
            ("List", "list", ["List"], "Scrollable list component"),
            ("Grid", "grid", ["Grid"], "Grid layout component"),
            ("Column", "column", ["Column"], "Vertical layout component"),
            ("Row", "row", ["Row"], "Horizontal layout component"),
            ("Stack", "stack", ["Stack"], "Stack layout component"),
            ("Flex", "flex", ["Flex"], "Flexible layout component"),
            ("Scroll", "scroll", ["Scroll"], "Scrollable container component"),
            ("Tabs", "tabs", ["Tabs"], "Tab navigation component"),
            ("Navigation", "navigation", ["Navigation"], "Navigation component"),
            ("Swiper", "swiper", ["Swiper"], "Swiper component for sliding content"),
            ("Progress", "progress", ["Progress"], "Progress indicator component"),
            ("Slider", "slider", ["Slider"], "Slider input component"),
            ("Toggle", "toggle", ["Toggle"], "Toggle switch component"),
            ("Radio", "radio", ["Radio"], "Radio button component"),
            ("Checkbox", "checkbox", ["Checkbox"], "Checkbox input component"),
            ("TextInput", "text input", ["TextInput"], "Text input field component"),
            ("TextArea", "text area", ["TextArea"], "Multi-line text input component"),
            ("Search", "search", ["Search"], "Search input component"),
            ("Select", "select", ["Select"], "Selection dropdown component"),
            ("DatePicker", "date picker", ["DatePicker"], "Date selection component"),
            ("TimePicker", "time picker", ["TimePicker"], "Time selection component"),
            ("Calendar", "calendar", ["CalendarPicker"], "Calendar component"),
            ("Badge", "badge", ["Badge"], "Badge notification component"),
            ("Divider", "divider", ["Divider"], "Visual divider component"),
            ("Loading", "loading", ["LoadingProgress"], "Loading indicator component"),
            ("Rating", "rating", ["Rating"], "Star rating component"),
            ("Gauge", "gauge", ["Gauge"], "Gauge meter component")
        ]

        for name, query, symbols, desc in ui_components:
            test_cases.append(TestCase(
                name=f"UI_Component_{name}",
                query=query,
                expected_type="component",
                expected_symbols=symbols,
                category="UI_Components",
                description=desc
            ))

        # 2. LAYOUT COMPONENTS (15 test cases)
        layout_components = [
            ("GridRow", "grid row", ["GridRow"], "Grid row layout component"),
            ("GridCol", "grid column", ["GridCol"], "Grid column layout component"),
            ("RelativeContainer", "relative container", ["RelativeContainer"], "Relative positioning container"),
            ("WaterFlow", "water flow", ["WaterFlow"], "Waterfall flow layout"),
            ("FlowItem", "flow item", ["FlowItem"], "Flow layout item"),
            ("ListItem", "list item", ["ListItem"], "List item component"),
            ("GridItem", "grid item", ["GridItem"], "Grid item component"),
            ("TabContent", "tab content", ["TabContent"], "Tab content component"),
            ("Panel", "panel", ["Panel"], "Sliding panel component"),
            ("SideBar", "sidebar", ["SideBarContainer"], "Sidebar container component"),
            ("Refresh", "refresh", ["Refresh"], "Pull-to-refresh component"),
            ("Stepper", "stepper", ["Stepper"], "Step-by-step navigation"),
            ("StepperItem", "stepper item", ["StepperItem"], "Stepper item component"),
            ("NavDestination", "nav destination", ["NavDestination"], "Navigation destination"),
            ("NavRouter", "nav router", ["NavRouter"], "Navigation router component")
        ]

        for name, query, symbols, desc in layout_components:
            test_cases.append(TestCase(
                name=f"Layout_{name}",
                query=query,
                expected_type="component",
                expected_symbols=symbols,
                category="Layout_Components",
                description=desc
            ))

        # 3. ADVANCED COMPONENTS (20 test cases)
        advanced_components = [
            ("Dialog", "dialog", ["Dialog"], "Advanced dialog component"),
            ("Popup", "popup", ["Popup"], "Advanced popup component"),
            ("Menu", "menu", ["Menu"], "Context menu component"),
            ("MenuItem", "menu item", ["MenuItem"], "Menu item component"),
            ("ActionSheet", "action sheet", ["ActionSheet"], "Action sheet dialog"),
            ("AlertDialog", "alert dialog", ["AlertDialog"], "Alert dialog component"),
            ("CustomDialog", "custom dialog", ["CustomDialogController"], "Custom dialog controller"),
            ("RichText", "rich text", ["RichText"], "Rich text display component"),
            ("RichEditor", "rich editor", ["RichEditor"], "Rich text editor component"),
            ("Web", "web", ["Web"], "Web view component"),
            ("Video", "video", ["Video"], "Video player component"),
            ("Canvas", "canvas", ["Canvas"], "Canvas drawing component"),
            ("XComponent", "xcomponent", ["XComponent"], "Native component wrapper"),
            ("Particle", "particle", ["Particle"], "Particle animation component"),
            ("Component3D", "3d component", ["Component3D"], "3D rendering component"),
            ("SymbolGlyph", "symbol glyph", ["SymbolGlyph"], "Symbol glyph component"),
            ("SymbolSpan", "symbol span", ["SymbolSpan"], "Symbol span component"),
            ("ImageSpan", "image span", ["ImageSpan"], "Image span component"),
            ("ContainerSpan", "container span", ["ContainerSpan"], "Container span component"),
            ("StyledString", "styled string", ["StyledString"], "Styled string component")
        ]

        for name, query, symbols, desc in advanced_components:
            test_cases.append(TestCase(
                name=f"Advanced_{name}",
                query=query,
                expected_type="component",
                expected_symbols=symbols,
                category="Advanced_Components",
                description=desc
            ))

        # 4. SYSTEM APIs (25 test cases)
        system_apis = [
            ("UIAbility", "ui ability", ["UIAbility"], "UI ability base class"),
            ("AbilityStage", "ability stage", ["AbilityStage"], "Ability stage lifecycle"),
            ("Want", "want", ["Want"], "Intent-like data structure"),
            ("Context", "context", ["Context"], "Application context"),
            ("Configuration", "configuration", ["Configuration"], "App configuration"),
            ("Router", "router", ["router"], "Page routing functionality"),
            ("Preferences", "preferences", ["preferences"], "Data preferences storage"),
            ("RelationalStore", "relational store", ["relationalStore"], "Relational database"),
            ("FileSystem", "file system", ["fs"], "File system operations"),
            ("HTTP", "http", ["http"], "HTTP network requests"),
            ("Bluetooth", "bluetooth", ["bluetooth"], "Bluetooth connectivity"),
            ("WiFi", "wifi", ["wifi"], "WiFi connectivity"),
            ("Location", "location", ["geoLocationManager"], "Location services"),
            ("Sensor", "sensor", ["sensor"], "Device sensors"),
            ("Camera", "camera", ["camera"], "Camera functionality"),
            ("Audio", "audio", ["audio"], "Audio playback and recording"),
            ("Notification", "notification", ["notificationManager"], "System notifications"),
            ("Vibrator", "vibrator", ["vibrator"], "Device vibration"),
            ("Battery", "battery", ["batteryInfo"], "Battery information"),
            ("Display", "display", ["display"], "Display management"),
            ("Window", "window", ["window"], "Window management"),
            ("Accessibility", "accessibility", ["accessibility"], "Accessibility services"),
            ("Security", "security", ["cryptoFramework"], "Security and encryption"),
            ("Account", "account", ["appAccount"], "Account management"),
            ("Bundle", "bundle", ["bundleManager"], "Bundle management")
        ]

        for name, query, symbols, desc in system_apis:
            test_cases.append(TestCase(
                name=f"System_API_{name}",
                query=query,
                expected_type="api",
                expected_symbols=symbols,
                category="System_APIs",
                description=desc
            ))

        # 5. IMPORT PATH TESTS (15 test cases)
        import_tests = [
            ("Button_Import", "Button", ["Button"], "Button component import path"),
            ("Text_Import", "Text", ["Text"], "Text component import path"),
            ("Image_Import", "Image", ["Image"], "Image component import path"),
            ("List_Import", "List", ["List"], "List component import path"),
            ("Grid_Import", "Grid", ["Grid"], "Grid component import path"),
            ("UIAbility_Import", "UIAbility", ["UIAbility"], "UIAbility import path"),
            ("router_Import", "router", ["router"], "Router import path"),
            ("preferences_Import", "preferences", ["preferences"], "Preferences import path"),
            ("http_Import", "http", ["http"], "HTTP import path"),
            ("fs_Import", "fs", ["fs"], "File system import path"),
            ("audio_Import", "audio", ["audio"], "Audio import path"),
            ("camera_Import", "camera", ["camera"], "Camera import path"),
            ("bluetooth_Import", "bluetooth", ["bluetooth"], "Bluetooth import path"),
            ("wifi_Import", "wifi", ["wifi"], "WiFi import path"),
            ("window_Import", "window", ["window"], "Window import path")
        ]

        for name, query, symbols, desc in import_tests:
            test_cases.append(TestCase(
                name=f"Import_Path_{name}",
                query=query,
                expected_type="import_path",
                expected_symbols=symbols,
                category="Import_Paths",
                description=desc
            ))

        logger.info(f"📊 Created {len(test_cases)} comprehensive test cases")
        return test_cases

    def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all comprehensive tests."""
        logger.info("🚀 Starting comprehensive Agno Agent tests...")

        start_time = time.time()

        # Test each function
        self._test_search_component()
        self._test_search_import_path()
        self._test_search_arkts_api()
        self._test_handle_agent_query()

        end_time = time.time()
        total_time = end_time - start_time

        # Generate comprehensive report
        report = self._generate_comprehensive_report(total_time)

        # Save detailed results
        self._save_detailed_results()

        return report

    def _test_search_component(self):
        """Test search_component function."""
        logger.info("\n🧩 Testing search_component function...")

        component_tests = [tc for tc in self.test_cases if tc.category in ["UI_Components", "Layout_Components", "Advanced_Components"]]

        for test_case in component_tests:
            self.total_tests += 1

            try:
                # Call search_component - use the standalone function
                if hasattr(self.tools.search_component, 'entrypoint'):
                    # Agno Function object - call the entrypoint
                    result = self.tools.search_component.entrypoint(test_case.query, limit=5)
                else:
                    # Direct function call
                    result = self.tools.search_component(test_case.query, limit=5)

                # Analyze result with extreme skepticism
                success, analysis = self._analyze_component_result(test_case, result)

                if success:
                    self.passed_tests += 1
                    logger.info(f"  ✅ {test_case.name}: {analysis}")
                else:
                    self.failed_tests += 1
                    logger.error(f"  ❌ {test_case.name}: {analysis}")

                self.test_results.append({
                    'test_name': test_case.name,
                    'function': 'search_component',
                    'query': test_case.query,
                    'expected_symbols': test_case.expected_symbols,
                    'result': result,
                    'success': success,
                    'analysis': analysis,
                    'category': test_case.category
                })

            except Exception as e:
                self.failed_tests += 1
                error_msg = f"Exception occurred: {str(e)}"
                logger.error(f"  ❌ {test_case.name}: {error_msg}")

                self.test_results.append({
                    'test_name': test_case.name,
                    'function': 'search_component',
                    'query': test_case.query,
                    'expected_symbols': test_case.expected_symbols,
                    'result': None,
                    'success': False,
                    'analysis': error_msg,
                    'category': test_case.category
                })

    def _test_search_import_path(self):
        """Test search_import_path function."""
        logger.info("\n📦 Testing search_import_path function...")

        import_tests = [tc for tc in self.test_cases if tc.category == "Import_Paths"]

        for test_case in import_tests:
            self.total_tests += 1

            try:
                # Call search_import_path - use the standalone function
                if hasattr(self.tools.search_import_path, 'entrypoint'):
                    # Agno Function object - call the entrypoint
                    result = self.tools.search_import_path.entrypoint(test_case.query, limit=5)
                else:
                    # Direct function call
                    result = self.tools.search_import_path(test_case.query, limit=5)

                # Analyze result with extreme skepticism
                success, analysis = self._analyze_import_path_result(test_case, result)

                if success:
                    self.passed_tests += 1
                    logger.info(f"  ✅ {test_case.name}: {analysis}")
                else:
                    self.failed_tests += 1
                    logger.error(f"  ❌ {test_case.name}: {analysis}")

                self.test_results.append({
                    'test_name': test_case.name,
                    'function': 'search_import_path',
                    'query': test_case.query,
                    'expected_symbols': test_case.expected_symbols,
                    'result': result,
                    'success': success,
                    'analysis': analysis,
                    'category': test_case.category
                })

            except Exception as e:
                self.failed_tests += 1
                error_msg = f"Exception occurred: {str(e)}"
                logger.error(f"  ❌ {test_case.name}: {error_msg}")

                self.test_results.append({
                    'test_name': test_case.name,
                    'function': 'search_import_path',
                    'query': test_case.query,
                    'expected_symbols': test_case.expected_symbols,
                    'result': None,
                    'success': False,
                    'analysis': error_msg,
                    'category': test_case.category
                })

    def _test_search_arkts_api(self):
        """Test search_arkts_api function."""
        logger.info("\n🔧 Testing search_arkts_api function...")

        api_tests = [tc for tc in self.test_cases if tc.category == "System_APIs"]

        for test_case in api_tests:
            self.total_tests += 1

            try:
                # Call search_arkts_api - use the standalone function
                if hasattr(self.tools.search_arkts_api, 'entrypoint'):
                    # Agno Function object - call the entrypoint
                    result = self.tools.search_arkts_api.entrypoint(test_case.query, limit=5)
                else:
                    # Direct function call
                    result = self.tools.search_arkts_api(test_case.query, limit=5)

                # Analyze result with extreme skepticism
                success, analysis = self._analyze_api_result(test_case, result)

                if success:
                    self.passed_tests += 1
                    logger.info(f"  ✅ {test_case.name}: {analysis}")
                else:
                    self.failed_tests += 1
                    logger.error(f"  ❌ {test_case.name}: {analysis}")

                self.test_results.append({
                    'test_name': test_case.name,
                    'function': 'search_arkts_api',
                    'query': test_case.query,
                    'expected_symbols': test_case.expected_symbols,
                    'result': result,
                    'success': success,
                    'analysis': analysis,
                    'category': test_case.category
                })

            except Exception as e:
                self.failed_tests += 1
                error_msg = f"Exception occurred: {str(e)}"
                logger.error(f"  ❌ {test_case.name}: {error_msg}")

                self.test_results.append({
                    'test_name': test_case.name,
                    'function': 'search_arkts_api',
                    'query': test_case.query,
                    'expected_symbols': test_case.expected_symbols,
                    'result': None,
                    'success': False,
                    'analysis': error_msg,
                    'category': test_case.category
                })

    def _test_handle_agent_query(self):
        """Test handle_agent_query function."""
        logger.info("\n🤖 Testing handle_agent_query function...")

        # Create agent-style queries
        agent_queries = [
            ("ArkTS search: 'Button component'", "component", ["Button"]),
            ("ArkTS search: 'Text display'", "component", ["Text"]),
            ("ArkTS search: 'Image component'", "component", ["Image"]),
            ("ArkTS search: 'List component'", "component", ["List"]),
            ("ArkTS search: 'Grid layout'", "component", ["Grid"]),
            ("ArkTS search: 'UIAbility class'", "api", ["UIAbility"]),
            ("ArkTS search: 'router navigation'", "api", ["router"]),
            ("ArkTS search: 'http requests'", "api", ["http"]),
            ("ArkTS search: 'file system'", "api", ["fs"]),
            ("ArkTS search: 'preferences storage'", "api", ["preferences"]),
            ("ArkTS search: 'camera functionality'", "api", ["camera"]),
            ("ArkTS search: 'audio playback'", "api", ["audio"]),
            ("ArkTS search: 'bluetooth connection'", "api", ["bluetooth"]),
            ("ArkTS search: 'wifi management'", "api", ["wifi"]),
            ("ArkTS search: 'window operations'", "api", ["window"])
        ]

        for query, expected_type, expected_symbols in agent_queries:
            self.total_tests += 1

            try:
                # Call handle_agent_query - use the standalone function
                if hasattr(self.tools.handle_agent_query, 'entrypoint'):
                    # Agno Function object - call the entrypoint
                    result = self.tools.handle_agent_query.entrypoint(query, limit=5)
                else:
                    # Direct function call
                    result = self.tools.handle_agent_query(query, limit=5)

                # Analyze result with extreme skepticism
                success, analysis = self._analyze_agent_query_result(query, expected_type, expected_symbols, result)

                if success:
                    self.passed_tests += 1
                    logger.info(f"  ✅ Agent Query '{query}': {analysis}")
                else:
                    self.failed_tests += 1
                    logger.error(f"  ❌ Agent Query '{query}': {analysis}")

                self.test_results.append({
                    'test_name': f"Agent_Query_{len(self.test_results)}",
                    'function': 'handle_agent_query',
                    'query': query,
                    'expected_symbols': expected_symbols,
                    'result': result,
                    'success': success,
                    'analysis': analysis,
                    'category': 'Agent_Queries'
                })

            except Exception as e:
                self.failed_tests += 1
                error_msg = f"Exception occurred: {str(e)}"
                logger.error(f"  ❌ Agent Query '{query}': {error_msg}")

                self.test_results.append({
                    'test_name': f"Agent_Query_{len(self.test_results)}",
                    'function': 'handle_agent_query',
                    'query': query,
                    'expected_symbols': expected_symbols,
                    'result': None,
                    'success': False,
                    'analysis': error_msg,
                    'category': 'Agent_Queries'
                })

    def _analyze_component_result(self, test_case: TestCase, result: str) -> Tuple[bool, str]:
        """Analyze component search result with extreme skepticism."""

        # Check if result is empty or None
        if not result or result.strip() == "":
            return False, "Empty result returned"

        if "No results found" in result:
            return False, "No results found in response"

        # Check if result contains expected symbols
        found_symbols = []
        for expected_symbol in test_case.expected_symbols:
            if expected_symbol.lower() in result.lower():
                found_symbols.append(expected_symbol)

        if not found_symbols:
            return False, f"Expected symbols {test_case.expected_symbols} not found in result"

        # Check if result contains import statement
        if "import" not in result.lower():
            return False, "No import statement found in result"

        # Check if result is properly formatted
        if "**Type:**" not in result and "**Import:**" not in result:
            return False, "Result not properly formatted (missing Type or Import fields)"

        # Check for suspicious patterns
        if "undefined" in result.lower() or "null" in result.lower():
            return False, "Result contains suspicious undefined/null values"

        # Check result length (too short might indicate incomplete response)
        if len(result) < 50:
            return False, f"Result too short ({len(result)} chars), might be incomplete"

        return True, f"Found {len(found_symbols)} expected symbols: {found_symbols}"

    def _analyze_import_path_result(self, test_case: TestCase, result: str) -> Tuple[bool, str]:
        """Analyze import path search result with extreme skepticism."""

        # Check if result is empty or None
        if not result or result.strip() == "":
            return False, "Empty result returned"

        if "No results found" in result:
            return False, "No results found in response"

        # Check if result contains expected symbols
        found_symbols = []
        for expected_symbol in test_case.expected_symbols:
            if expected_symbol.lower() in result.lower():
                found_symbols.append(expected_symbol)

        if not found_symbols:
            return False, f"Expected symbols {test_case.expected_symbols} not found in result"

        # Check if result contains import statement
        if "import" not in result.lower() and "from" not in result.lower():
            return False, "No import statement found in result"

        # Check if result contains module information
        if "**Module:**" not in result and "**Import Statement:**" not in result:
            return False, "Result missing module or import statement information"

        # Check for ArkTS-specific patterns
        arkts_patterns = ["@ohos", "@system", "@kit", ".d.ts", ".d.ets"]
        has_arkts_pattern = any(pattern in result for pattern in arkts_patterns)

        if not has_arkts_pattern:
            return False, "Result doesn't contain ArkTS-specific import patterns"

        return True, f"Found {len(found_symbols)} expected symbols with proper import paths"

    def _analyze_api_result(self, test_case: TestCase, result: str) -> Tuple[bool, str]:
        """Analyze API search result with extreme skepticism."""

        # Check if result is empty or None
        if not result or result.strip() == "":
            return False, "Empty result returned"

        if "No results found" in result:
            return False, "No results found in response"

        # Check if result contains expected symbols
        found_symbols = []
        for expected_symbol in test_case.expected_symbols:
            if expected_symbol.lower() in result.lower():
                found_symbols.append(expected_symbol)

        if not found_symbols:
            return False, f"Expected symbols {test_case.expected_symbols} not found in result"

        # Check if result contains API-specific information
        api_indicators = ["API", "interface", "class", "function", "method", "namespace"]
        has_api_indicator = any(indicator.lower() in result.lower() for indicator in api_indicators)

        if not has_api_indicator:
            return False, "Result doesn't contain API-specific indicators"

        # Check for system capability information
        if "@ohos" in test_case.query.lower() and "@ohos" not in result.lower():
            return False, "Expected @ohos API pattern not found in result"

        return True, f"Found {len(found_symbols)} expected API symbols with proper information"

    def _analyze_agent_query_result(self, query: str, expected_type: str, expected_symbols: List[str], result: str) -> Tuple[bool, str]:
        """Analyze agent query result with extreme skepticism."""

        # Check if result is empty or None
        if not result or result.strip() == "":
            return False, "Empty result returned"

        if "No results found" in result:
            return False, "No results found in response"

        # Check if result contains expected symbols
        found_symbols = []
        for expected_symbol in expected_symbols:
            if expected_symbol.lower() in result.lower():
                found_symbols.append(expected_symbol)

        if not found_symbols:
            return False, f"Expected symbols {expected_symbols} not found in result"

        # Check if agent query format was properly parsed
        if "ArkTS search:" in query and "search" not in result.lower():
            return False, "Agent query format not properly handled"

        # Check if result is formatted for agent consumption
        if not any(marker in result for marker in ["#", "**", "```", "|"]):
            return False, "Result not properly formatted for agent consumption"

        return True, f"Agent query properly handled, found {len(found_symbols)} expected symbols"

    def _generate_comprehensive_report(self, total_time: float) -> Dict[str, Any]:
        """Generate comprehensive test report."""

        # Calculate statistics by category
        category_stats = {}
        function_stats = {}

        for result in self.test_results:
            category = result['category']
            function = result['function']

            if category not in category_stats:
                category_stats[category] = {'total': 0, 'passed': 0, 'failed': 0}
            if function not in function_stats:
                function_stats[function] = {'total': 0, 'passed': 0, 'failed': 0}

            category_stats[category]['total'] += 1
            function_stats[function]['total'] += 1

            if result['success']:
                category_stats[category]['passed'] += 1
                function_stats[function]['passed'] += 1
            else:
                category_stats[category]['failed'] += 1
                function_stats[function]['failed'] += 1

        # Calculate success rates
        overall_success_rate = (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0

        report = {
            'overall_stats': {
                'total_tests': self.total_tests,
                'passed_tests': self.passed_tests,
                'failed_tests': self.failed_tests,
                'success_rate': overall_success_rate,
                'total_time': total_time,
                'avg_time_per_test': total_time / self.total_tests if self.total_tests > 0 else 0
            },
            'category_stats': category_stats,
            'function_stats': function_stats,
            'detailed_results': self.test_results
        }

        # Log comprehensive report
        logger.info("\n" + "="*80)
        logger.info("📊 COMPREHENSIVE AGNO AGENT TEST REPORT")
        logger.info("="*80)

        logger.info(f"🎯 Overall Results:")
        logger.info(f"   Total Tests: {self.total_tests}")
        logger.info(f"   Passed: {self.passed_tests}")
        logger.info(f"   Failed: {self.failed_tests}")
        logger.info(f"   Success Rate: {overall_success_rate:.1f}%")
        logger.info(f"   Total Time: {total_time:.2f}s")
        logger.info(f"   Avg Time/Test: {total_time/self.total_tests:.3f}s")

        logger.info(f"\n📋 Results by Category:")
        for category, stats in category_stats.items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            logger.info(f"   {category}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")

        logger.info(f"\n🔧 Results by Function:")
        for function, stats in function_stats.items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            logger.info(f"   {function}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")

        # Assessment
        if overall_success_rate >= 90:
            logger.info("\n🏆 EXCELLENT: Agno Agent integration works exceptionally well!")
        elif overall_success_rate >= 75:
            logger.info("\n✅ GOOD: Agno Agent integration works well with minor issues")
        elif overall_success_rate >= 50:
            logger.info("\n⚠️ MODERATE: Agno Agent integration needs improvements")
        else:
            logger.error("\n❌ POOR: Agno Agent integration has significant issues")

        return report

    def _save_detailed_results(self):
        """Save detailed test results to files."""

        # Create results directory
        results_dir = "test_results/Test_AgnoAgent_Results"
        os.makedirs(results_dir, exist_ok=True)

        # Save JSON results
        json_file = os.path.join(results_dir, "comprehensive_test_results.json")
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)

        # Save detailed text report
        text_file = os.path.join(results_dir, "detailed_test_report.txt")
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write("Comprehensive Agno Agent Test Results\n")
            f.write("="*50 + "\n\n")

            for result in self.test_results:
                f.write(f"Test: {result['test_name']}\n")
                f.write(f"Function: {result['function']}\n")
                f.write(f"Query: {result['query']}\n")
                f.write(f"Expected: {result['expected_symbols']}\n")
                f.write(f"Success: {result['success']}\n")
                f.write(f"Analysis: {result['analysis']}\n")
                f.write(f"Category: {result['category']}\n")

                if result['result']:
                    f.write(f"Result Preview: {result['result'][:200]}...\n")

                f.write("\n" + "-"*50 + "\n\n")

        logger.info(f"📄 Detailed results saved to: {results_dir}")


class MockArkTSImportTools:
    """Mock implementation for testing when real tools are not available."""

    def search_component(self, component_name: str, limit: int = 5) -> str:
        """Mock component search."""
        return f"""# Component Search Results

## 1. {component_name}
**Type:** component
**Import:** `import {{ {component_name} }} from '@kit.ArkUI';`
**Relevance Score:** 0.9876

A {component_name.lower()} component that provides user interface functionality.

**Usage Example:**
```typescript
import {{ {component_name} }} from '@kit.ArkUI'

@Entry
@Component
struct MyComponent {{
  build() {{
    {component_name}()
  }}
}}
```

---
"""

    def search_import_path(self, symbol_name: str, limit: int = 5) -> str:
        """Mock import path search."""
        return f"""# Import Path Search Results

## 1. {symbol_name}
**Type:** Component
**Module:** @kit.ArkUI
**Import Statement:** `import {{ {symbol_name} }} from '@kit.ArkUI';`
**Relevance Score:** 0.9876

---
"""

    def search_arkts_api(self, query: str, limit: int = 5) -> str:
        """Mock API search."""
        api_name = query.split()[0].title()
        return f"""# ArkTS Search Results

## API Results

### 1. {api_name}
**Module:** @ohos.{query.replace(' ', '.')}
**Import:** `import {api_name} from '@ohos.{query.replace(' ', '.')}';`
**Relevance Score:** 0.9876

API for {query} functionality.

---
"""

    def handle_agent_query(self, agent_query: str, limit: int = 5) -> str:
        """Mock agent query handling."""
        if "ArkTS search:" in agent_query:
            query = agent_query.replace("ArkTS search:", "").strip().strip("'\"")

            if "component" in query.lower():
                component_name = query.split()[0].title()
                return self.search_component(component_name, limit)
            else:
                return self.search_arkts_api(query, limit)

        return "Invalid agent query format"


def main():
    """Main function to run comprehensive tests."""
    logger.info("🚀 Starting Comprehensive Agno Agent Tests...")

    try:
        # Initialize tester
        tester = AgnoAgentTester()

        # Run comprehensive tests
        report = tester.run_comprehensive_tests()

        # Return report for further analysis
        return report

    except Exception as e:
        logger.error(f"❌ Critical error in comprehensive testing: {str(e)}")
        return {'error': str(e)}


if __name__ == "__main__":
    main()
