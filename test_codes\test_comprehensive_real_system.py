"""
Comprehensive Real System Test for ArkTS Import Estimator

This test examines the current codebase completeness for ArkTS import statement generation
using real large d.ts and d.ets files from Information/default/ folders.

Tests:
1. Parse and index real ArkTS files
2. Test agent usage with real data
3. Verify all ArkTS features are supported
4. Test with qwen3:8b model for faster processing
"""

import os
import sys
import json
import time
import logging
import traceback
from typing import List, Dict, Any, Tuple
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Import our modules
from component_cards import ArkTSImportEstimatorFixed, ArkTSSymbolParser
from arkts_agno_tools import ArkTSImportTools
import config

# Configure logging (will add file handler later after directory creation)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ComprehensiveRealSystemTest")

class ComprehensiveRealSystemTest:
    """Comprehensive test for real ArkTS system capabilities."""

    def __init__(self):
        """Initialize the test."""
        self.test_results_dir = "test_results/Test_ComprehensiveRealSystem_Results"
        self.ensure_test_directory()

        # Test configuration
        self.test_files = []
        self.total_symbols_indexed = 0
        self.total_files_processed = 0
        self.test_start_time = None
        self.test_results = {
            "parsing_results": [],
            "indexing_results": [],
            "agent_test_results": [],
            "search_test_results": [],
            "performance_metrics": {},
            "errors": [],
            "warnings": []
        }

        logger.info("ComprehensiveRealSystemTest initialized")
        logger.info(f"Using model: {config.AGENT_MODEL}")
        logger.info(f"Test results directory: {self.test_results_dir}")

    def ensure_test_directory(self):
        """Ensure test results directory exists."""
        os.makedirs(self.test_results_dir, exist_ok=True)

        # Add file handler now that directory exists
        log_file = os.path.join(self.test_results_dir, "comprehensive_real_system_test.log")
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        logger.addHandler(file_handler)

        logger.info(f"Test results directory: {self.test_results_dir}")
        logger.info(f"Log file: {log_file}")

    def find_large_arkts_files(self, min_size: int = 50000) -> List[Tuple[str, int]]:
        """Find large d.ts and d.ets files for testing.

        Args:
            min_size: Minimum file size in bytes

        Returns:
            List of (file_path, file_size) tuples
        """
        logger.info(f"Searching for large ArkTS files (min size: {min_size:,} bytes)")

        # Search directories (relative to project root)
        search_dirs = [
            "../Information/default/openharmony/ets/api",
            "../Information/default/openharmony/ets/component",
            "../Information/default/openharmony/ets/build-tools",
            "../Information/default/hms/ets/api",
            "../comprehensive_dataset"
        ]

        large_files = []

        for search_dir in search_dirs:
            if not os.path.exists(search_dir):
                logger.warning(f"Directory not found: {search_dir}")
                continue

            logger.info(f"Searching in: {search_dir}")

            for root, _, files in os.walk(search_dir):
                for file in files:
                    if file.endswith(('.d.ts', '.d.ets')):
                        file_path = os.path.join(root, file)
                        try:
                            file_size = os.path.getsize(file_path)
                            if file_size >= min_size:
                                large_files.append((file_path, file_size))
                                logger.info(f"Found large file: {file_path} ({file_size:,} bytes)")
                        except OSError as e:
                            logger.warning(f"Could not get size for {file_path}: {e}")

        # Sort by size (largest first)
        large_files.sort(key=lambda x: x[1], reverse=True)

        # Limit to top 30 files for comprehensive testing
        large_files = large_files[:30]

        logger.info(f"Found {len(large_files)} large ArkTS files for testing")
        return large_files

    def test_parsing_capabilities(self, file_paths: List[str]) -> Dict[str, Any]:
        """Test parsing capabilities on real files.

        Args:
            file_paths: List of file paths to test

        Returns:
            Parsing test results
        """
        logger.info("=== Testing Parsing Capabilities ===")

        parser = ArkTSSymbolParser()
        parsing_results = {
            "total_files": len(file_paths),
            "successful_files": 0,
            "failed_files": 0,
            "total_symbols": 0,
            "symbol_types": {},
            "file_results": [],
            "errors": []
        }

        for i, file_path in enumerate(file_paths, 1):
            logger.info(f"Parsing file {i}/{len(file_paths)}: {file_path}")

            try:
                start_time = time.time()
                symbols = parser.parse_file(file_path)
                parse_time = time.time() - start_time

                if symbols:
                    parsing_results["successful_files"] += 1
                    parsing_results["total_symbols"] += len(symbols)

                    # Count symbol types
                    for symbol in symbols:
                        symbol_type = symbol.get('symbol_type', 'unknown')
                        parsing_results["symbol_types"][symbol_type] = parsing_results["symbol_types"].get(symbol_type, 0) + 1

                    file_result = {
                        "file_path": file_path,
                        "file_size": os.path.getsize(file_path),
                        "success": True,
                        "symbols_count": len(symbols),
                        "parse_time": parse_time,
                        "symbol_types": {}
                    }

                    # Count symbol types for this file
                    for symbol in symbols:
                        symbol_type = symbol.get('symbol_type', 'unknown')
                        file_result["symbol_types"][symbol_type] = file_result["symbol_types"].get(symbol_type, 0) + 1

                    parsing_results["file_results"].append(file_result)

                    logger.info(f"Parsed {len(symbols)} symbols in {parse_time:.3f}s")

                    # Log symbol type breakdown
                    type_counts = {}
                    for symbol in symbols:
                        symbol_type = symbol.get('symbol_type', 'unknown')
                        type_counts[symbol_type] = type_counts.get(symbol_type, 0) + 1

                    logger.info(f"   Symbol types: {type_counts}")

                else:
                    parsing_results["failed_files"] += 1
                    file_result = {
                        "file_path": file_path,
                        "file_size": os.path.getsize(file_path),
                        "success": False,
                        "symbols_count": 0,
                        "parse_time": parse_time,
                        "error": "No symbols found"
                    }
                    parsing_results["file_results"].append(file_result)
                    logger.warning(f"No symbols found in {file_path}")

            except Exception as e:
                parsing_results["failed_files"] += 1
                error_msg = f"Error parsing {file_path}: {str(e)}"
                parsing_results["errors"].append(error_msg)
                logger.error(error_msg)
                logger.error(traceback.format_exc())

                file_result = {
                    "file_path": file_path,
                    "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                    "success": False,
                    "symbols_count": 0,
                    "parse_time": 0,
                    "error": str(e)
                }
                parsing_results["file_results"].append(file_result)

        # Save parsing results
        with open(f"{self.test_results_dir}/parsing_results.json", "w") as f:
            json.dump(parsing_results, f, indent=2)

        logger.info(f"Parsing completed: {parsing_results['successful_files']}/{parsing_results['total_files']} files successful")
        logger.info(f"Total symbols parsed: {parsing_results['total_symbols']}")
        logger.info(f"Symbol types found: {parsing_results['symbol_types']}")

        return parsing_results

    def test_indexing_capabilities(self, file_paths: List[str]) -> Dict[str, Any]:
        """Test indexing capabilities with real files.

        Args:
            file_paths: List of file paths to index

        Returns:
            Indexing test results
        """
        logger.info("=== Testing Indexing Capabilities ===")

        indexing_results = {
            "total_files": len(file_paths),
            "successful_files": 0,
            "failed_files": 0,
            "total_symbols_indexed": 0,
            "file_results": [],
            "errors": []
        }

        try:
            # Initialize the estimator
            with ArkTSImportEstimatorFixed() as estimator:
                # Ensure collection exists
                if not estimator.ensure_collection_exists(force_recreate=True):
                    raise Exception("Failed to create Qdrant collection")

                logger.info("Qdrant collection created successfully")

                # Index files one by one
                for i, file_path in enumerate(file_paths, 1):
                    logger.info(f"Indexing file {i}/{len(file_paths)}: {file_path}")

                    try:
                        start_time = time.time()
                        symbols_indexed = estimator.index_file(file_path)
                        index_time = time.time() - start_time

                        if symbols_indexed > 0:
                            indexing_results["successful_files"] += 1
                            indexing_results["total_symbols_indexed"] += symbols_indexed

                            file_result = {
                                "file_path": file_path,
                                "file_size": os.path.getsize(file_path),
                                "success": True,
                                "symbols_indexed": symbols_indexed,
                                "index_time": index_time
                            }
                            indexing_results["file_results"].append(file_result)

                            logger.info(f"Indexed {symbols_indexed} symbols in {index_time:.3f}s")

                        else:
                            indexing_results["failed_files"] += 1
                            file_result = {
                                "file_path": file_path,
                                "file_size": os.path.getsize(file_path),
                                "success": False,
                                "symbols_indexed": 0,
                                "index_time": index_time,
                                "error": "No symbols indexed"
                            }
                            indexing_results["file_results"].append(file_result)
                            logger.warning(f"No symbols indexed from {file_path}")

                    except Exception as e:
                        indexing_results["failed_files"] += 1
                        error_msg = f"Error indexing {file_path}: {str(e)}"
                        indexing_results["errors"].append(error_msg)
                        logger.error(error_msg)

                        file_result = {
                            "file_path": file_path,
                            "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                            "success": False,
                            "symbols_indexed": 0,
                            "index_time": 0,
                            "error": str(e)
                        }
                        indexing_results["file_results"].append(file_result)

        except Exception as e:
            error_msg = f"Critical error during indexing: {str(e)}"
            indexing_results["errors"].append(error_msg)
            logger.error(error_msg)
            logger.error(traceback.format_exc())

        # Save indexing results
        with open(f"{self.test_results_dir}/indexing_results.json", "w") as f:
            json.dump(indexing_results, f, indent=2)

        logger.info(f"Indexing completed: {indexing_results['successful_files']}/{indexing_results['total_files']} files successful")
        logger.info(f"Total symbols indexed: {indexing_results['total_symbols_indexed']}")

        self.total_symbols_indexed = indexing_results['total_symbols_indexed']
        self.total_files_processed = indexing_results['successful_files']

        return indexing_results

    def test_agent_capabilities(self) -> Dict[str, Any]:
        """Test agent capabilities with real indexed data.

        Returns:
            Agent test results
        """
        logger.info("=== Testing Agent Capabilities ===")

        agent_results = {
            "total_queries": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "query_results": [],
            "errors": []
        }

        # Test queries for different ArkTS features
        test_queries = [
            # Component searches
            ("Button", "component"),
            ("Dialog", "component"),
            ("Text", "component"),
            ("Image", "component"),
            ("List", "component"),

            # API searches
            ("UIAbility", "api"),
            ("router", "api"),
            ("preferences", "api"),
            ("http", "api"),
            ("bluetooth", "api"),

            # Import path searches
            ("@ohos.app.ability.UIAbility", "import_path"),
            ("@ohos.router", "import_path"),
            ("@ohos.data.preferences", "import_path"),
            ("@ohos.net.http", "import_path"),
            ("@ohos.bluetooth", "import_path"),

            # Complex searches
            ("file system", "api"),
            ("data storage", "api"),
            ("network request", "api"),
            ("device sensor", "api"),
            ("multimedia audio", "api")
        ]

        try:
            # Initialize agent tools
            agent_tools = ArkTSImportTools()

            for query, query_type in test_queries:
                agent_results["total_queries"] += 1
                logger.info(f"Testing query: '{query}' (type: {query_type})")

                try:
                    start_time = time.time()

                    # Test different agent functions based on query type
                    if query_type == "component":
                        results = agent_tools.search_component(query, limit=5)
                    elif query_type == "import_path":
                        results = agent_tools.search_import_path(query, limit=5)
                    else:  # api
                        results = agent_tools.search_arkts_api(query, limit=5)

                    query_time = time.time() - start_time

                    # Check if results are meaningful
                    if results and "Error:" not in results and len(results.strip()) > 10:
                        agent_results["successful_queries"] += 1

                        query_result = {
                            "query": query,
                            "query_type": query_type,
                            "success": True,
                            "query_time": query_time,
                            "results_length": len(results),
                            "results_preview": results[:200] + "..." if len(results) > 200 else results
                        }
                        agent_results["query_results"].append(query_result)

                        logger.info(f"Query successful in {query_time:.3f}s, results length: {len(results)}")

                    else:
                        agent_results["failed_queries"] += 1

                        query_result = {
                            "query": query,
                            "query_type": query_type,
                            "success": False,
                            "query_time": query_time,
                            "results_length": len(results) if results else 0,
                            "error": "No meaningful results or error in response"
                        }
                        agent_results["query_results"].append(query_result)

                        logger.warning(f"Query failed: {query}")

                except Exception as e:
                    agent_results["failed_queries"] += 1
                    error_msg = f"Error testing query '{query}': {str(e)}"
                    agent_results["errors"].append(error_msg)
                    logger.error(error_msg)

                    query_result = {
                        "query": query,
                        "query_type": query_type,
                        "success": False,
                        "query_time": 0,
                        "error": str(e)
                    }
                    agent_results["query_results"].append(query_result)

        except Exception as e:
            error_msg = f"Critical error during agent testing: {str(e)}"
            agent_results["errors"].append(error_msg)
            logger.error(error_msg)
            logger.error(traceback.format_exc())

        # Save agent results
        with open(f"{self.test_results_dir}/agent_results.json", "w") as f:
            json.dump(agent_results, f, indent=2)

        logger.info(f"Agent testing completed: {agent_results['successful_queries']}/{agent_results['total_queries']} queries successful")

        return agent_results

    def run_comprehensive_test(self):
        """Run the comprehensive test suite."""
        logger.info("Starting Comprehensive Real System Test")
        self.test_start_time = time.time()

        try:
            # Step 1: Find large ArkTS files
            logger.info("Step 1: Finding large ArkTS files...")
            large_files = self.find_large_arkts_files(min_size=50000)

            if not large_files:
                logger.error("No large ArkTS files found for testing")
                return

            # Extract file paths
            file_paths = [file_path for file_path, _ in large_files]

            logger.info(f"Found {len(file_paths)} large files for testing")

            # Step 2: Test parsing capabilities
            logger.info("Step 2: Testing parsing capabilities...")
            parsing_results = self.test_parsing_capabilities(file_paths)
            self.test_results["parsing_results"] = parsing_results

            # Step 3: Test indexing capabilities
            logger.info("Step 3: Testing indexing capabilities...")
            indexing_results = self.test_indexing_capabilities(file_paths)
            self.test_results["indexing_results"] = indexing_results

            # Step 4: Test agent capabilities (only if indexing was successful)
            if self.total_symbols_indexed > 0:
                logger.info("Step 4: Testing agent capabilities...")
                agent_results = self.test_agent_capabilities()
                self.test_results["agent_test_results"] = agent_results
            else:
                logger.warning("Skipping agent tests - no symbols were indexed")

            # Step 5: Generate comprehensive report
            self.generate_comprehensive_report()

            total_time = time.time() - self.test_start_time
            logger.info(f"Comprehensive test completed in {total_time:.2f} seconds")

        except Exception as e:
            logger.error(f"Critical error in comprehensive test: {str(e)}")
            logger.error(traceback.format_exc())
            self.test_results["errors"].append(str(e))

    def generate_comprehensive_report(self):
        """Generate a comprehensive test report."""
        logger.info("=== Generating Comprehensive Report ===")

        total_time = time.time() - self.test_start_time

        # Calculate performance metrics
        self.test_results["performance_metrics"] = {
            "total_test_time": total_time,
            "files_processed": self.total_files_processed,
            "symbols_indexed": self.total_symbols_indexed,
            "files_per_second": self.total_files_processed / total_time if total_time > 0 else 0,
            "symbols_per_second": self.total_symbols_indexed / total_time if total_time > 0 else 0
        }

        # Save complete test results
        with open(f"{self.test_results_dir}/comprehensive_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)

        # Generate markdown report
        report_lines = [
            "# 🎯 **COMPREHENSIVE REAL SYSTEM TEST REPORT**",
            "",
            f"**Date**: {time.strftime('%Y-%m-%d %H:%M:%S')}",
            f"**Test Duration**: {total_time:.2f} seconds",
            f"**Model Used**: {config.AGENT_MODEL}",
            "",
            "## 📊 **SUMMARY**",
            "",
            f"- **Files Processed**: {self.total_files_processed}",
            f"- **Symbols Indexed**: {self.total_symbols_indexed:,}",
            f"- **Performance**: {self.test_results['performance_metrics']['symbols_per_second']:.0f} symbols/sec",
            "",
            "## 📋 **DETAILED RESULTS**",
            ""
        ]

        # Add parsing results
        if "parsing_results" in self.test_results:
            parsing = self.test_results["parsing_results"]
            report_lines.extend([
                "### 🔍 **Parsing Results**",
                "",
                f"- **Total Files**: {parsing['total_files']}",
                f"- **Successful**: {parsing['successful_files']} ({parsing['successful_files']/parsing['total_files']*100:.1f}%)",
                f"- **Failed**: {parsing['failed_files']}",
                f"- **Total Symbols**: {parsing['total_symbols']:,}",
                f"- **Symbol Types**: {len(parsing['symbol_types'])}",
                ""
            ])

        # Add indexing results
        if "indexing_results" in self.test_results:
            indexing = self.test_results["indexing_results"]
            report_lines.extend([
                "### 📚 **Indexing Results**",
                "",
                f"- **Total Files**: {indexing['total_files']}",
                f"- **Successful**: {indexing['successful_files']} ({indexing['successful_files']/indexing['total_files']*100:.1f}%)",
                f"- **Failed**: {indexing['failed_files']}",
                f"- **Symbols Indexed**: {indexing['total_symbols_indexed']:,}",
                ""
            ])

        # Add agent results
        if "agent_test_results" in self.test_results:
            agent = self.test_results["agent_test_results"]
            report_lines.extend([
                "### 🤖 **Agent Test Results**",
                "",
                f"- **Total Queries**: {agent['total_queries']}",
                f"- **Successful**: {agent['successful_queries']} ({agent['successful_queries']/agent['total_queries']*100:.1f}%)",
                f"- **Failed**: {agent['failed_queries']}",
                ""
            ])

        # Add errors and warnings
        if self.test_results["errors"]:
            report_lines.extend([
                "### ⚠️ **Errors**",
                ""
            ])
            for error in self.test_results["errors"]:
                report_lines.append(f"- {error}")
            report_lines.append("")

        # Save markdown report (fix Unicode encoding issue)
        try:
            with open(f"{self.test_results_dir}/comprehensive_test_report.md", "w", encoding='utf-8') as f:
                # Remove emoji characters that cause encoding issues
                safe_report_lines = []
                for line in report_lines:
                    # Replace problematic Unicode characters
                    safe_line = line.encode('ascii', 'ignore').decode('ascii')
                    safe_report_lines.append(safe_line)
                f.write("\n".join(safe_report_lines))
        except Exception as e:
            self.logger.error(f"Error writing report file: {str(e)}")
            # Fallback: write without problematic characters
            with open(f"{self.test_results_dir}/comprehensive_test_report.md", "w", encoding='ascii', errors='ignore') as f:
                f.write("\n".join(report_lines))

        logger.info(f"Comprehensive report saved to {self.test_results_dir}/")
        logger.info("Test completed successfully!")


if __name__ == "__main__":
    test = ComprehensiveRealSystemTest()
    test.run_comprehensive_test()
