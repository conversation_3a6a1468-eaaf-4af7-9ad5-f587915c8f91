# Introduction to Distributed Service Kit

Distributed Service Kit implements distributed device management, distributed hardware management, and distributed keyboard and mouse traversal.

Distributed services are underpinned by distributed device management, which involves discovering and authenticating nearby devices, querying device information, and listening for device status. That is, distributed services can be performed only between authenticated devices.

## Working Principles

The application is required to initiate a request for device discovery, authentication, query, and listening.

## Constraints

The application needs user authorization before implementing distributed device management.
