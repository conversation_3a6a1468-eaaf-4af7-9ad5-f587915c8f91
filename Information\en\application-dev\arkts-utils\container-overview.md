# Container Overview

The container classes provide a set of methods to process elements of various data types stored in containers. It has advantages as a pure data structure container.

The container classes are implemented in a way similar to static languages. By restricting storage locations and attributes, they remove redundant logic while providing the complete functionalities for each type of data, ensuring efficient data access and improving application performance.

There are 14 types of linear and nonlinear containers, each of which has its own features and uses cases. For details, see [Linear Containers](linear-container.md) and [Nonlinear Containers](nonlinear-container.md).
