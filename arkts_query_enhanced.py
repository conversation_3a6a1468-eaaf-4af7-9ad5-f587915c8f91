"""
ArkTS Query Enhanced

This module extends the ArkTSQueryCached class with enhanced search capabilities.
It provides improved hybrid search, result re-ranking, and fallback mechanisms.
"""

import time
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple, Union, Callable
from qdrant_client.http import models

# Import the cached query class
from arkts_query_cached import ArkTSQueryCached

# Import compatibility layer
from qdrant_compatibility import qdrant_compatibility

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSQueryEnhanced")


class ArkTSQueryEnhanced(ArkTSQueryCached):
    """ArkTS Query with enhanced search capabilities."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None,
                 ollama_url: str = None, embedding_model: str = None,
                 cache_ttl: int = 3600, cache_max_size: int = 1000,
                 reranking_weight: float = 0.5):
        """Initialize the enhanced query.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
            cache_ttl: Time-to-live for cache entries in seconds
            cache_max_size: Maximum number of entries in the cache
            reranking_weight: Weight for semantic vs. text match in reranking (0.0-1.0)
        """
        # Initialize the parent class
        super().__init__(
            qdrant_url=qdrant_url,
            collection_name=collection_name,
            ollama_url=ollama_url,
            embedding_model=embedding_model,
            cache_ttl=cache_ttl,
            cache_max_size=cache_max_size
        )

        # Set reranking weight
        self.reranking_weight = max(0.0, min(1.0, reranking_weight))  # Clamp to [0.0, 1.0]
        logger.info(f"ArkTSQueryEnhanced initialized with reranking weight: {self.reranking_weight}")

    def suggest_imports_hybrid(self, query: str, limit: int = None,
                              text_weight: float = 0.3) -> List[Dict[str, Any]]:
        """Suggest imports using enhanced hybrid search.

        This method combines vector search and text search with custom weighting
        and applies result reranking for better relevance.

        Args:
            query: Query string
            limit: Maximum number of results
            text_weight: Weight for text search vs. vector search (0.0-1.0)

        Returns:
            List of import suggestions
        """
        limit = limit or config.DEFAULT_LIMIT
        text_weight = max(0.0, min(1.0, text_weight))  # Clamp to [0.0, 1.0]
        vector_weight = 1.0 - text_weight

        try:
            # Generate query embedding
            query_vector = self._get_embedding(query)

            # Perform hybrid search with custom scoring
            try:
                # First, get more results than needed for reranking
                extended_limit = min(limit * 3, 100)  # Get more results but cap at 100

                # Define text search conditions
                text_conditions = [
                    models.FieldCondition(
                        key="symbol_name",
                        match=models.MatchText(text=query)
                    ),
                    models.FieldCondition(
                        key="description",
                        match=models.MatchText(text=query)
                    )
                ]

                # Perform hybrid search with custom scoring
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        should=text_conditions
                    ),
                    score_threshold=0.0,  # Include all results for reranking
                    limit=extended_limit,
                    with_payload=True
                )

                # Rerank results
                reranked_results = self._rerank_results(results, query, vector_weight, text_weight)

                # Limit to requested number
                reranked_results = reranked_results[:limit]

                logger.info(f"Enhanced hybrid search for '{query}' returned {len(reranked_results)} results")
                return reranked_results

            except Exception as e:
                logger.warning(f"Enhanced hybrid search failed: {str(e)}")
                # Fall back to standard hybrid search
                logger.info("Falling back to standard hybrid search")
                return super().suggest_imports(query, limit=limit, use_hybrid=True)

        except Exception as e:
            logger.error(f"Error in suggest_imports_hybrid: {str(e)}")
            # Fall back to standard search
            return super().suggest_imports(query, limit=limit)

    def _rerank_results(self, results: List, query: str,
                       vector_weight: float, text_weight: float) -> List[Dict[str, Any]]:
        """Rerank search results using a combination of semantic and text matching.

        Args:
            results: Search results from Qdrant
            query: Original query string
            vector_weight: Weight for vector search score
            text_weight: Weight for text match score

        Returns:
            Reranked list of results
        """
        reranked = []
        query_terms = set(query.lower().split())

        for result in results:
            # Get original vector score
            vector_score = result.score

            # Calculate text match score
            symbol_name = result.payload.get('symbol_name', '').lower()
            description = result.payload.get('description', '').lower()

            # Count matching terms in symbol name (weighted higher)
            name_matches = sum(1 for term in query_terms if term in symbol_name)
            name_score = name_matches / max(1, len(query_terms))

            # Count matching terms in description
            desc_matches = sum(1 for term in query_terms if term in description)
            desc_score = desc_matches / max(1, len(query_terms))

            # Combined text score (name is weighted higher)
            text_score = (name_score * 0.7) + (desc_score * 0.3)

            # Calculate combined score
            combined_score = (vector_score * vector_weight) + (text_score * text_weight)

            # Create result with new score
            result_dict = result.payload
            result_dict['score'] = combined_score
            result_dict['vector_score'] = vector_score
            result_dict['text_score'] = text_score

            reranked.append(result_dict)

        # Sort by combined score
        reranked.sort(key=lambda x: x['score'], reverse=True)

        return reranked

    def search_with_fallback(self, primary_func: Callable, fallback_func: Callable,
                           *args, **kwargs) -> List[Dict[str, Any]]:
        """Execute a search function with fallback to another function if it fails.

        Args:
            primary_func: Primary search function to try first
            fallback_func: Fallback search function to use if primary fails
            *args: Positional arguments for the search functions
            **kwargs: Keyword arguments for the search functions

        Returns:
            Search results from either primary or fallback function
        """
        try:
            # Try primary function
            results = primary_func(*args, **kwargs)

            # If no results, try fallback
            if not results:
                logger.info(f"Primary search returned no results, trying fallback")
                results = fallback_func(*args, **kwargs)

            return results

        except Exception as e:
            logger.warning(f"Primary search failed: {str(e)}, trying fallback")

            try:
                # Try fallback function
                return fallback_func(*args, **kwargs)
            except Exception as fallback_error:
                logger.error(f"Fallback search also failed: {str(fallback_error)}")
                return []

    def suggest_imports(self, query: str, limit: int = None, use_hybrid: bool = None) -> List[Dict[str, Any]]:
        """Override suggest_imports to use enhanced hybrid search by default.

        Args:
            query: Query string
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search (ignored, always True for enhanced)

        Returns:
            List of import suggestions
        """
        # Always use enhanced hybrid search
        return self.suggest_imports_hybrid(query, limit)

    def search_component(self, component_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for a specific component with fallback.

        Args:
            component_name: Name of the component to search for
            limit: Maximum number of results

        Returns:
            List of component search results
        """
        return self.search_with_fallback(
            super().search_component,
            lambda name, limit: self.suggest_imports_hybrid(f"component {name}", limit),
            component_name, limit
        )

    def search_import_path(self, symbol_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for import paths with fallback.

        Args:
            symbol_name: Name of the symbol to find import paths for
            limit: Maximum number of results

        Returns:
            List of import path search results
        """
        return self.search_with_fallback(
            super().search_import_path,
            lambda name, limit: self.suggest_imports_hybrid(f"import {name}", limit),
            symbol_name, limit
        )

    def search_by_symbol_type(self, query: str, symbol_type: str, limit: int = None,
                             use_hybrid: bool = None) -> List[Dict[str, Any]]:
        """Search for symbols of a specific type with fallback.

        Args:
            query: Query string
            symbol_type: Symbol type to search for
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search (ignored, always True for enhanced)

        Returns:
            List of symbols matching the type and query
        """
        return self.search_with_fallback(
            super().search_by_symbol_type,
            lambda q, type, limit, hybrid: self.suggest_imports_hybrid(f"{type} {q}", limit),
            query, symbol_type, limit, True
        )


# Add main function for command-line usage
def main():
    """Main function for enhanced querying."""
    import argparse

    parser = argparse.ArgumentParser(description='ArkTS Query Enhanced')
    parser.add_argument('--qdrant-url', type=str, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, help='Ollama embedding model to use')
    parser.add_argument('--query', type=str, help='Query for import suggestions')
    parser.add_argument('--limit', type=int, help='Maximum number of results')
    parser.add_argument('--type', type=str, help='Filter by symbol type')
    parser.add_argument('--nested', type=str, help='Search for nested symbols within a parent')
    parser.add_argument('--nested-type', type=str, help='Type of nested symbols to search for')
    parser.add_argument('--component', type=str, help='Search for a specific component')
    parser.add_argument('--import-path', type=str, help='Search for import paths for a symbol')
    parser.add_argument('--agent-query', type=str, help='Process an Agno agent query format')
    parser.add_argument('--cache-ttl', type=int, default=3600, help='Cache TTL in seconds')
    parser.add_argument('--cache-max-size', type=int, default=1000, help='Maximum cache size')
    parser.add_argument('--clear-cache', action='store_true', help='Clear the cache before querying')
    parser.add_argument('--reranking-weight', type=float, default=0.5, help='Reranking weight (0.0-1.0)')
    parser.add_argument('--text-weight', type=float, default=0.3, help='Text search weight (0.0-1.0)')

    args = parser.parse_args()

    # Initialize enhanced query
    query = ArkTSQueryEnhanced(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model,
        cache_ttl=args.cache_ttl,
        cache_max_size=args.cache_max_size,
        reranking_weight=args.reranking_weight
    )

    # Clear cache if requested
    if args.clear_cache:
        query.clear_cache()
        print("Cache cleared")

    # Process query based on arguments
    if args.agent_query:
        # Handle agent query format
        results = query.handle_agent_query(args.agent_query, limit=args.limit)
        print(f"Found {len(results)} results for agent query: '{args.agent_query}'")

    elif args.component:
        # Search for component
        results = query.search_component(args.component, limit=args.limit)
        print(f"Found {len(results)} components matching '{args.component}':")

    elif args.import_path:
        # Search for import paths
        results = query.search_import_path(args.import_path, limit=args.limit)
        print(f"Found {len(results)} import paths for '{args.import_path}':")

    elif args.nested and args.nested_type:
        # Search for nested symbols of specific type
        results = query.search_nested_by_type(args.nested, args.nested_type, limit=args.limit)
        print(f"Found {len(results)} nested {args.nested_type} symbols in '{args.nested}':")

    elif args.nested:
        # Search for all nested symbols
        results = query.search_nested_symbols(args.nested, limit=args.limit)
        print(f"Found {len(results)} nested symbols in '{args.nested}':")

    elif args.type and args.query:
        # Search by symbol type
        results = query.search_by_symbol_type(args.query, args.type, limit=args.limit)
        print(f"Found {len(results)} {args.type} symbols matching '{args.query}':")

    elif args.query:
        # General import suggestions with enhanced hybrid search
        results = query.suggest_imports_hybrid(args.query, limit=args.limit, text_weight=args.text_weight)
        print(f"Found {len(results)} suggestions for '{args.query}':")

    else:
        print("Please provide a query parameter. Use --help for available options.")
        return

    # Print results
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['symbol_name']} ({result['symbol_type']})")
        print(f"   Import: {result.get('import_statement', 'N/A')}")
        print(f"   Score: {result['score']:.4f}")

        if 'vector_score' in result and 'text_score' in result:
            print(f"   Vector Score: {result['vector_score']:.4f}, Text Score: {result['text_score']:.4f}")

        if result.get('parent_symbol'):
            print(f"   Nested in: {result['parent_symbol']}")

        if result.get('module_name'):
            print(f"   Module: {result['module_name']}")

        if result.get('description'):
            # Truncate long descriptions
            desc = result['description']
            if len(desc) > 100:
                desc = desc[:97] + "..."
            print(f"   Description: {desc}")


if __name__ == "__main__":
    main()
