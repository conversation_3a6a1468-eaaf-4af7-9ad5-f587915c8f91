# oh_predicates.h


## Overview

Defines the predicates for RDB stores.

**File to include**: <database/rdb/oh_predicates.h>

**Library**: libnative_rdb_ndk.z.so

**Since**: 10

**Related module**: [RDB](_r_d_b.md)


## Summary


### Structs

| Name| Description|
| -------- | -------- |
| [OH_Predicates](_o_h___predicates.md) | Defines a **predicates** object. |


### Types

| Name| Description|
| -------- | -------- |
| [OH_OrderType](_r_d_b.md#oh_ordertype) | Enumerates the sorting types.|
| [OH_Predicates](_r_d_b.md#oh_predicates) | Indicates a **predicates** object. |


### Enums

| Name| Description|
| -------- | -------- |
| [OH_OrderType](_r_d_b.md#oh_ordertype-1) { ASC = 0, DESC = 1 } | Enumerates the sorting types.|
