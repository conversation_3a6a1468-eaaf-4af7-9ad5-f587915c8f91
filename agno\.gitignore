# See https://help.github.com/ignore-files/ for more about ignoring files.

.DS_Store

# Python cache
.mypy_cache
*__pycache__*
*.egg-info
*.pyc
*.pytest_cache
*.ruff_cache
*.cache*
*.config*

# Machine specific
.idea
.vscode
*.code-workspace

# Ignore .env files
.env

# Ignore .envrc files (used by direnv: https://direnv.net/)
.envrc

certs/

# ignore storage dir
/storage
.images
/qdrant_storage

# ignore wip dir
wip

# ignore dist dir
dist

# ignore virtualenvs
.venv*
.perfenv*
venv*
agnoenv*
aienv*
phienv*

# ignore tmp dirs
scratch
junk
tmp
agents.db
data.db
.ipynb_checkpoints
build
audio_generations

*.db

# Google OAuth token
token.json

.composio.lock

# Coverage
coverage_report
.coverage
