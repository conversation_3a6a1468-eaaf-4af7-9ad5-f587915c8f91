# ArkTS Import Tools for Agno Agents

Bu proje, ArkTS programlama dili için import önerilerini Agno Agent'lar ile entegre eden bir araç seti sağlar. Agno Agent'lar, web sorguları yerine bu araç setini kullanarak ArkTS bileşenleri, import yolları ve API'leri hakkında bilgi alabilirler.

## Özellikler

- **Komponent Aramaları**: ArkTS bileşenlerini isimlerine göre arama
- **Import Path Aramaları**: ArkTS sembolleri için import yollarını arama
- **Genel ArkTS API Aramaları**: ArkTS API'lerini anahtar kelimelerle arama
- **Agno Agent Sorgu Formatı Desteği**: "ArkTS search: 'query'" formatındaki sorguları işleme
- **Önbellek Mekanizması**: Tekrarlanan sorgular için performans iyileştirmesi
- **Hibrit Arama**: Vektör tabanlı semantik arama ve metin tabanlı arama kombinasyonu
- **Asenkron Sorgu Desteği**: Asenkron/await desteği ile daha iyi performans
- **Sonuç Formatlaması**: Farklı sorgu tipleri için özelleştirilmiş sonuç formatları

## Mimari

Proje, aşağıdaki bileşenlerden oluşur:

1. **ArkTSQueryCached**: Önbellek mekanizması eklenmiş ArkTS sorgu sınıfı
2. **ArkTSQueryEnhanced**: Hibrit arama iyileştirmeleri eklenmiş ArkTS sorgu sınıfı
3. **ArkTSQueryAsync**: Asenkron sorgu desteği eklenmiş ArkTS sorgu sınıfı
4. **ArkTSFormatter**: ArkTS sorgu sonuçlarını formatlama araçları
5. **ArkTSImportTools**: Agno Agent entegrasyonu için araç seti

## Kurulum

### Gereksinimler

- Python 3.8+
- Qdrant veritabanı
- Ollama (embedding modeli için)
- Agno framework (opsiyonel)

### Bağımlılıklar

```bash
pip install -r requirements.txt
```

### Qdrant Kurulumu

Qdrant veritabanını Docker ile çalıştırmak için:

```bash
docker run -p 6333:6333 -p 6334:6334 -v $(pwd)/qdrant_data:/qdrant/storage qdrant/qdrant
```

### Ollama Kurulumu

Ollama'yı [resmi web sitesinden](https://ollama.ai/) indirip kurabilirsiniz.

## Kullanım

### Komut Satırı Kullanımı

```bash
# Komponent arama
python arkts_agno_tools.py --component "Button" --limit 5

# Import path arama
python arkts_agno_tools.py --import-path "Button" --limit 5

# Genel API arama
python arkts_agno_tools.py --query "image display" --limit 5

# Agno agent sorgu formatı
python arkts_agno_tools.py --agent-query "ArkTS search: 'Button component'" --limit 5
```

### Python API Kullanımı

```python
from arkts_agno_tools import ArkTSImportTools

# Araç setini başlat
tools = ArkTSImportTools(
    qdrant_url="http://localhost:6333",
    collection_name="arkts",
    ollama_url="http://localhost:11434",
    embedding_model="llama2"
)

# Komponent arama
result = tools.search_component("Button", limit=5)
print(result)

# Import path arama
result = tools.search_import_path("Button", limit=5)
print(result)

# Genel API arama
result = tools.search_arkts_api("image display", limit=5)
print(result)

# Agno agent sorgu formatı
result = tools.handle_agent_query("ArkTS search: 'Button component'", limit=5)
print(result)
```

### Agno Agent Entegrasyonu

```python
from agno.agent import Agent
from arkts_agno_tools import ArkTSImportTools

# Araç setini başlat
arkts_tools = ArkTSImportTools(
    qdrant_url="http://localhost:6333",
    collection_name="arkts",
    ollama_url="http://localhost:11434",
    embedding_model="llama2"
)

# Agno agent oluştur
agent = Agent(
    tools=[arkts_tools],
    model="gpt-4"
)

# Agent'a sorgu gönder
response = agent.run("ArkTS'de Button bileşeni nasıl kullanılır?")
print(response)
```

## Asenkron Kullanım

```python
import asyncio
from arkts_query_async import ArkTSQueryAsync

async def main():
    # Asenkron sorgu sınıfını başlat
    query = ArkTSQueryAsync(
        qdrant_url="http://localhost:6333",
        collection_name="arkts",
        ollama_url="http://localhost:11434",
        embedding_model="llama2"
    )
    
    # Asenkron sorgu yap
    results = await query.suggest_imports_async("Button", limit=5)
    print(results)

# Asenkron fonksiyonu çalıştır
asyncio.run(main())
```

## Özelleştirme

### Önbellek Ayarları

```python
from arkts_query_cached import ArkTSQueryCached

# Önbellek ayarlarını özelleştir
query = ArkTSQueryCached(
    qdrant_url="http://localhost:6333",
    collection_name="arkts",
    ollama_url="http://localhost:11434",
    embedding_model="llama2",
    cache_ttl=7200,  # 2 saat
    cache_max_size=2000  # Maksimum 2000 giriş
)

# Önbelleği temizle
query.clear_cache()
```

### Hibrit Arama Ayarları

```python
from arkts_query_enhanced import ArkTSQueryEnhanced

# Hibrit arama ayarlarını özelleştir
query = ArkTSQueryEnhanced(
    qdrant_url="http://localhost:6333",
    collection_name="arkts",
    ollama_url="http://localhost:11434",
    embedding_model="llama2",
    reranking_weight=0.7  # Semantik arama ağırlığı (0.0-1.0)
)

# Hibrit arama yap
results = query.suggest_imports_hybrid(
    "Button",
    limit=5,
    text_weight=0.4  # Metin araması ağırlığı (0.0-1.0)
)
```

### Formatlama Ayarları

```python
from arkts_formatter import ArkTSFormatter

# Formatlama ayarlarını özelleştir
formatter = ArkTSFormatter(
    max_description_length=300,  # Maksimum açıklama uzunluğu
    include_scores=True  # Skorları dahil et
)

# Sonuçları formatla
formatted = formatter.format_results_for_agent(results, query_type="component")
print(formatted)

# JSON formatında sonuçlar
json_formatted = formatter.format_as_json(results, include_full_details=True)
print(json_formatted)

# Markdown tablosu formatında sonuçlar
table_formatted = formatter.format_as_markdown_table(
    results,
    fields=["symbol_name", "symbol_type", "import_statement", "score"]
)
print(table_formatted)
```

## Testler

Testleri çalıştırmak için:

```bash
python -m unittest discover -s test_codes
```

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır. Detaylar için `LICENSE` dosyasına bakınız.
