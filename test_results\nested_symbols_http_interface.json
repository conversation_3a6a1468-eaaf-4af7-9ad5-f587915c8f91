[{"symbol_name": "is", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "This", "import_statement": "import { http.is } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.is", "score": 0.0}, {"symbol_name": "MultiFormData", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "Represents the properties of a form object. @interface MultiFormData @syscap SystemCapability.Communication.NetStack @crossplatform @since 12 export", "import_statement": "import { http.MultiFormData } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.MultiFormData", "score": 0.0}, {"symbol_name": "PerformanceTiming", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "Counting the time taken of various stages of HTTP request. @interface PerformanceTiming @syscap SystemCapability.Communication.NetStack @crossplatform @since 12 export", "import_statement": "import { http.PerformanceTiming } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.PerformanceTiming", "score": 0.0}]