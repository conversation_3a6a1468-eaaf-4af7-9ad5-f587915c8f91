# drawing_color.h


## Overview

The **drawing_color.h** file declares the functions related to the color in the drawing module.

**File to include**: &lt;native_drawing/drawing_color.h&gt;

**Library**: libnative_drawing.so

**Since**: 8

**Related module**: [Drawing](_drawing.md)


## Summary


### Functions

| Name| Description|
| -------- | -------- |
| uint32_t [OH_Drawing_ColorSetArgb](_drawing.md#oh_drawing_colorsetargb) (uint32_t alpha, uint32_t red, uint32_t green, uint32_t blue) | Converts four variables (alpha, red, green, and blue) into a 32-bit (ARGB) variable that describes a color.|
