# @ohos.app.appstartup.StartupConfig

The StartupConfig module provides APIs for startup task configuration.

> **NOTE**
>
> The initial APIs of this module are supported since API version 12. Newly added APIs will be marked with a superscript to indicate their earliest API version.
>
> The APIs of this module can be used only in the stage model.

## Modules to Import

```js
import { StartupConfig } from '@kit.AbilityKit';
```

## Properties

**System capability**: SystemCapability.Ability.AppStartup

  | Name| Type| Read Only| Optional| Description|
| -------- | -------- | -------- | -------- | -------- |
| timeoutMs | number | Yes| Yes| Timeout for executing all startup tasks. The default value is 10000 ms.|
| startupListener | [StartupListener](./js-apis-app-appstartup-startupListener.md) | Yes| Yes| AppStartup listener, which is called when all the startup tasks are complete.|

**Example**

```ts
import { StartupConfig, StartupConfigEntry, StartupListener } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';

export default class MyStartupConfigEntry extends StartupConfigEntry {
  onConfig() {
    hilog.info(0x0000, 'testTag', `onConfig`);
    let onCompletedCallback = (error: BusinessError<void>) => {
      hilog.info(0x0000, 'testTag', `onCompletedCallback`);
      if (error) {
        hilog.info(0x0000, 'testTag', 'onCompletedCallback: %{public}d, message: %{public}s', error.code, error.message);
      } else {
        hilog.info(0x0000, 'testTag', `onCompletedCallback: success.`);
      }
    }
    let startupListener: StartupListener = {
      'onCompleted': onCompletedCallback
    }
    let config: StartupConfig = {
      'timeoutMs': 10000,
      'startupListener': startupListener
    }
    return config;
  }
}
```
