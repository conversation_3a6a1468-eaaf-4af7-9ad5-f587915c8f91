{"standard": [{"symbol_name": "is", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "This", "import_statement": "import { http.is } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.is", "score": 0.7539061}, {"symbol_name": "http", "symbol_type": "namespace", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "Provides http related APIs. @namespace http @syscap SystemCapability.Communication.NetStack @crossplatform @atomicservice @since 11", "import_statement": "import { http } from '@ohos.net.http';", "parent_symbol": null, "is_nested": false, "score": 0.70208865}, {"symbol_name": "HttpRequest", "symbol_type": "interface", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "<p>Defines an HTTP request task. Before invoking APIs provided by HttpRequest, you must call createHttp() to create an HttpRequestTask object.</p> @interface HttpRequest @syscap SystemCapability.Communication.NetStack @crossplatform @atomicservice @since 11 export", "import_statement": "import { http.HttpRequest } from '@ohos.net.http';", "parent_symbol": "http", "is_nested": true, "full_name": "http.HttpRequest", "score": 0.69952816}], "hybrid": [{"symbol_name": "http", "symbol_type": "namespace", "module_name": "@ohos.net.http", "is_default": false, "is_ets": false, "description": "Provides http related APIs. @namespace http @syscap SystemCapability.Communication.NetStack @crossplatform @atomicservice @since 11", "import_statement": "import { http } from '@ohos.net.http';", "parent_symbol": null, "is_nested": false, "score": 0.70208865}]}