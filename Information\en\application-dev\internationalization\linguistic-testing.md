# Linguistic Testing

Linguistic testing comes after internationalization and localization. It is a process of inspecting whether the application UI and content comply with local usage habits before the application is officially released and launched.

In a multilingual environment, the quality of localization is critical to product acceptance in the target market. Professional UI content, consistent translation, idiomatic word usage, and UI display affect user experience. Any minor error may cause user churn. Therefore, before an application is released globally, linguistic testing can be conducted to identify and fix potential problems, improving user experience of global users. In addition, exercise caution when using taboos as improper use of taboos may have a significant negative impact on an enterprise's image. A complete taboo solution can help safeguard your product for global use.
