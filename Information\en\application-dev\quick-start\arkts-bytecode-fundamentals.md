# Ark Bytecode Fundamentals

## Overall Design
### Overview
Ark Bytecode is a binary file generated by the ArkCompiler in ArkTS, TS or JS and provided for <PERSON> to interpret and execute. Bytecode mainly contains Ark Bytecode instructions.<br>
This topic describes the design of Ark Bytecode instructions. The following sections describe the important concepts, formats, and meanings of Ark Bytecode instructions, helping you understand these instructions and develop instruction-related features.<br>
An Ark Bytecode instruction consists of an operation code (instruction name) and an instruction input parameter list. An operation code can be a code without a prefix or with a prefix. Registers, immediates, string id, method id, and literal id can be used as input parameters of instructions. In addition, accumulators are used as default parameters in some instructions.<br>
In Ark Bytecode, in addition to register and accumulator, there are four value storage modes: **global variable**, **[module](https://262.ecma-international.org/12.0/#sec-ecmascript-language-scripts-and-modules) namespace and variable**, **lexical environment and variable**, and **patch variable**. The instruction can use the values stored by these four modes as input parameters.

### Terms and Constraints
#### Term
The following table lists the terms involved in this topic.

|     Term     |      Description       |
|   ----------  |    ----------   |
|  acc         |	Accumulator, a special register in the Ark Bytecode.  |
|  bit	|  Used as a unit in this topic.  |
|  hole	|  Objects or variables that have not been initialized.  |
|  id	|  Index, which is a general name of string id, method id, or literal id.  |
|  string id	|  String index, which is a 16-bit number used to link to the corresponding string.  |
|  method id	|  Method index, which is a 16-bit number and is used to link to the corresponding method.  |
|  literal id  |     Literal index, which is a 16-bit number and is used to link to the corresponding literal array.  |
|  lexical environment	|  Lexical environment, which is used to store the semantic environment of closure variables.  |
|  lexical variable	|  Lexical variable, a closure variable stored in the lexical environment.  |

#### Constraints
* All content described in code in this topic follows the ArkTS Language Specifications. For details, see [Introduction](introduction-to-arkts.md).
* This topic applies only to Ark Bytecode whose version number is 11.0.2.0. (The version number is an internal reserved field of the ArkCompiler.)

### Bytecode Composition
#### Operation Code and Prefix
The operation code in the Ark Bytecode is usually encoded as an 8-bit value, so there can be a maximum of 256 operation codes. With the runtime features of the ArkCompiler are getting evolved, the number of bytecode is increasing and has exceeded 256. Therefore, the Ark Bytecode introduces a prefix to extend the maximum width of the operation code from 8 bits to 16 bits. An 8-bit operation code (without a prefix) is used to indicate an instruction that appears frequently, and a 16-bit operation code (with a prefix) is used to indicate an instruction that appears occasionally <br>
An operation code with a prefix is a 16-bit value stored in little-endian mode. It consists of an 8-bit operation code and an 8-bit prefix. You can shift the operation code leftwards by 8 bits and run the **OR** logic with the code and the prefix to encode.
|     Prefix Operation Code     |      Mnemonic       |      Description       |
|     ----------  |    ----------   |    ----------   |
|  0xfe	  |  throw	|  Conditional/Unconditional **throw** instruction. |
|  0xfd	  |  wide	|  An instruction that contains an immediate, id, or register index with a wider encoding width. |
|  0xfc	  |  deprecated	 |  Instructions that are no longer generated by the ArkCompiler are used only to maintain runtime compatibility.<br>Following sections will no longer describe such instructions. |
|  0xfb	  |  callruntime | 	Instructions for calling runtime methods. |

The mnemonic of a prefix operation code is in the form of **prefix mnemonic.operation code mnemonic**, for example, **wide.stlexvar**. If the operation code of the **stlexvar** instruction is **0x0d** and the prefix **wide** is **0xfd**, the operation code of **wide.stlexvar** is **0x0dfd**.

#### Register and Accumulator
The Ark VM model is built based on registers. All registers are virtual registers. When the value of the original type is stored in the register, the width of the register is 64 bits. When the value of the object type is stored in the register, the width of the register is adapted to be wide enough to store the reference to the object.<br>
In Ark Bytecode, there is an invisible register called accumulator (also referred to as acc for short). acc is the default destination register of many instructions and the default parameter of many instructions. It does not occupy the encoding width, which helps generate more compact bytecode.<br>

Example:
```ts
function foo(): number {
    return 1;
}
```
Related instructions in the bytecode:
```assembly
.function any .foo(any a0, any a1, any a2) {
    ldai 0x1
    return
}
```
*ldai 0x1*: loads integer literal **1** to acc.<br>
*return*: returns the value in acc.

#### Immediate
Some instructions in Ark Bytecode use constants to represent data such as integer values, double-precision floating-point values, and jump offsets. Such constants are called immediate, which can be 8 bits, 16 bits, 32 bits, or 64 bits.

#### Method Index, String Index, and Literal Index
The Ark Bytecode stores the offsets of all methods, strings, and literal arrays used in the source file. The literal array stores various literal data, such as an integer number, a string offset, and a method offset. In the Ark Bytecode instruction, indexes of these methods, strings, and literal arrays are 16 bits, which are respectively referred to as a method index (method id), a string index (string id), and a literal index (literal id). These indexes are encoded in instructions to reference methods, strings, and literal arrays.

### Value Storage Mode
#### Global Variables
In [Script](https://262.ecma-international.org/12.0/#sec-ecmascript-language-scripts-and-modules) build mode, a global variable is a variable stored in a globally unique mapping. Its key is the name of the global variable, and its value is that of the global variable. Global variables can be accessed through global-related instructions.<br>

Example:
```ts
function foo(): void {
    a += 2;
    b = 5;
}
```
Related instructions in the bytecode:
```assembly
.function any .foo(any a0, any a1, any a2) {
    tryldglobalbyname 0x0, a
    sta v4
    ldai 0x2
    add2 0x1, v4
    trystglobalbyname 0x2, a
    ldai 0x5
    trystglobalbyname 0x3, b
    ...
}
```
*tryldglobalbyname 0x0, a*: loads the global variable named **a** to acc. If this variable does not exist, an exception is thrown.<br>
*trystglobalbyname 0x2, a*: stores the value of acc to the global variable named **a**. If this variable does not exist, an exception is thrown.<br>
*trystglobalbyname 0x3, b*: stores the value of acc to the global variable named **b**. If this variable does not exist, an exception is thrown.<br>
**NOTE**<br>
**0x0**, **0x2**, and **0x3** in the preceding instructions are reserved for internal use in Ark runtime.

#### Module Namespace and Module Variable
All [module namespaces](https://262.ecma-international.org/12.0/#module-namespace-exotic-object) used in the source file are compiled into an array. An index is used in the instruction to reference a module namespace. For example, *getmodulenamespace 0x1* references the module namespace at the *0x1* index.<br>
All module variables used in the source file are compiled into an array. In the instruction, an index is used to reference a module variable. For example, *stmodulevar 0x1* references a module variable at *0x1* index.<br>
In a function, if the declaration of a module variable is in the same source file as the function, the variable is called a local module variable. Otherwise, it is called an external module variable. For example, *ldlocalmodulevar* and *ldexternalmodulevar* are used respectively to load local module variables and external module variables.<br>
The scenarios for generating module instructions include [import](https://262.ecma-international.org/12.0/#sec-imports) and [export](https://262.ecma-international.org/12.0/#sec-exports). The main scenarios are as follows:
* **import * as**: module namespace
* **import { }**: module variable
* **export**: local export

**NOTE**<br>
The logic related to the module is implemented within the compiler. With the evolution of the ArkCompiler, new scenarios involving module instructions may occur. On the other hand, existing scenarios related to module namespaces and module variable instructions may no longer generate module-related instructions as requirements evolve and code is reconstructed.<br>

Example:
```ts
import { a, b } from "./module_foo"
import * as c from "./module_bar"

export let d: number = 3;

a + b + d;
```
Related instructions in the bytecode:
```assembly
.function any .func_main_0(any a0, any a1, any a2) {
    getmodulenamespace 0x1
    ldai 0x3
    stmodulevar 0x0
    ldexternalmodulevar 0x0
    sta v0
    throw.undefinedifholewithname a
    ldexternalmodulevar 0x1
    sta v1
    throw.undefinedifholewithname b
    lda v1
    add2 0x0, v0
    sta v0
    ldlocalmodulevar 0x0
    sta v1
    throw.undefinedifholewithname d
    lda v1
    add2 0x1, v0
    ...
}
```
*getmodulenamespace 0x1*: obtains the module namespace (c) of slot 1 and store it in acc.<br>
*stmodulevar 0x0*: stores the values in acc to slot 0 of the current module.<br>
*ldexternalmodulevar 0x0*: loads the value (a) of slot 0 of the external module and stores it in acc.<br>
*ldlocalmodulevar 0x0*: loads the value (d) of slot 0 of the current local module and stores it in acc.

#### Lexical Environment and Lexical Variable
In Ark Bytecode, a lexical environment may be considered as an array with multiple slots, and each slot corresponds to one lexical variable. Multiple lexical environments may exist in one method. The relative level number and slot index of the lexical environment are used in the instruction to represent a lexical variable. For example, *ldlexvar 0x1, 0x2* is used to store the value of slot 2 in the lexical environment beyond one level to acc.
```
|xxx|xxx|xxx|xxx|   <-- First lexical environment beyond the current lexical environment.
         ^
         |------------ ldlexvar 0x1, 0x2

|xxx|xxx|xxx|xxx|   <-- Current lexical environment.
```
**NOTE**<br>
The logic related to **lexical** is used in the compiler. With subsequent evolution of the ArkCompiler, new scenarios involving lexical instructions may emerge. On the other hand, existing **lexical** instruction-related scenarios may no longer generates **lexical** instructions as requirements evolve and code is reconstructed.
Example:
```ts
function foo(): void {
    let a: number = 1;
    function bar(): number {
        return a;
    }
}
```
Related instructions in the bytecode:
```assembly
.function any .foo(any a0, any a1, any a2) {
    newlexenv 0x1
    ...
    definefunc 0x0, .bar, 0x0
    sta v3
    ldai 0x1 
    ...
    stlexvar 0x0, 0x0
    ...
}    

.function any .bar(any a0, any a1, any a2) {
    ...
    ldlexvar 0x0, 0x0
    ...
}
```
*newlexenv 0x1*: creates a lexical environment whose slot number is 1, stores it in acc, and enters this environment.<br>
*stlexvar 0x0, 0x0*: stores the value in acc to slot 0 of the lexical environment beyond 0 level.<br>
*ldlexvar 0x0, 0x0*: stores the value of slot 0 in the lexical environment beyond 0 level to acc.

#### Patch Variable
The ArkCompiler supports patch mode. When a source file is modified, you can compile it in the patch mode and generate a patch bytecode. The patch bytecode works with the original bytecode to update features. When the ArkCompiler compiles in patch mode, the generated patch variables are stored in a special patch lexical environment. The Ark Bytecode uses the slot number in the patch lexical environment to reference the patch variable. For example, *ldpatchvar 0x1* is used to load the patch variable of slot 1.<br>

Example:
```ts
function bar(): void {} // Add a statement to compile the patch.

function foo(): void {
    bar(); // Add a statement to compile the patch.
}
```
Related instructions in the bytecode:
```assembly
.function any foo(...) {
    ...
    wide.ldpatchvar 0x0
    sta v4
    lda v4
    callarg0 0x0
    ...
}

.function any patch_main_0(...) {
    newlexenv 0x1
    definefunc 0x1, bar:(any,any,any), 0x0
    wide.stpatchvar 0x0
    ...
}
```
*wide.stpatchvar 0x0*: stores the **bar** function to slot 0 in the patch lexical environment.<br>
*wide.ldpatchvar 0x0*: stores the value of slot 0 in the patch lexical environment to acc.

### Function Calling Specifications
For a method that contains N pieces of formal parameters, the last N+3 registers used by the method are used to pass parameters. In this case, the first three registers represent the function (FunctionObject) itself, [new.target](https://262.ecma-international.org/12.0/#sec-function-environment-records) (NewTarget), and **this** in the lexical environment. The subsequent N registers correspond to these N pieces of formal parameters.<br>

Example:
```ts
function foo(a: number, b: number): void {}
```
Related instructions in the bytecode:
```assembly
.function any .foo(any a0, any a1, any a2, any a3, any a4) {
    // a0: FunctionObject
    // a1: NewTarget
    // a2: this 
    // a3: a
    // a4: b
}
```

## Bytecode Format Description
|     Mnemonic     |      Semantic Description      |
|   ----------   |    ----------   |
|  ID16	 |  8-bit operation code and 16-bit id  |
|  IMM16	 |  8-bit operation code and 16-bit immediate  |
|  IMM16_ID16	 |  8-bit operation code, 16-bit immediate, and 16-bit id  |
|  IMM16_ID16_ID16_IMM16_V8	 |  8-bit operation code, 16-bit immediate, two 16-bit id, 16-bit immediate, and 8-bit register  |
|  IMM16_ID16_IMM8	 |  8-bit operation code, 16-bit immediate, 16-bit id, and 8-bit immediate  |
|  IMM16_ID16_V8	 |  8-bit operation code, 16-bit immediate, 16-bit id, and 8-bit register  |
|  IMM16_IMM16	 |  8-bit operation code and two 16-bit immediates  |
|  IMM16_IMM8_V8	 |  8-bit operation code, 16-bit immediate, 8-bit immediate, and 8-bit register  |
|  IMM16_V8	 |  8-bit operation code, 16-bit immediate, and 8-bit register  |
|  IMM16_V8_IMM16	 |  8-bit operation code, 16-bit immediate, 8-bit register, and 16-bit immediate  |
|  IMM16_V8_V8	 |  8-bit operation code, 16-bit immediate, and two 8-bit registers  |
|  IMM32	 |  8-bit operation code and 32-bit immediate  |
|  IMM4_IMM4	 |  8-bit operation code, two 4-bit immediates  |
|  IMM64	 |  8-bit operation code and 64-bit immediate  |
|  IMM8	 |  8-bit operation code and 8-bit immediate  |
|  IMM8_ID16	 |  8-bit operation code, 8-bit immediate, and 16-bit id  |
|  IMM8_ID16_ID16_IMM16_V8	 |  8-bit operation code, 8-bit immediate, two 16-bit id, 16-bit immediate, and 8-bit register  |
|  IMM8_ID16_IMM8	 |  8-bit operation code, 8-bit immediate, 16-bit id, 8-bit immediate  |
|  IMM8_ID16_V8	 |  8-bit operation code, 8-bit immediate, 16-bit id, and 8-bit register  |
|  IMM8_IMM16	 |  8-bit operation code, 8-bit immediate, and 16-bit immediate  |
|  IMM8_IMM8	 |  8-bit operation code, two 8-bit immediates  |
|  IMM8_IMM8_V8	 |  8-bit operation code, two 8-bit immediates, and 8-bit register  |
|  IMM8_V8	 |  8-bit operation code, 8-bit immediate, and 8-bit register  |
|  IMM8_V8_IMM16	 |  8-bit operation code, 8-bit immediate, 8-bit register, and 16-bit immediate  |
|  IMM8_V8_V8	 |  8-bit operation code, 8-bit immediate, and two 8-bit registers  |
|  IMM8_V8_V8_V8	 |  8-bit operation code, 8-bit immediate, and three 8-bit registers  |
|  IMM8_V8_V8_V8_V8	 |  8-bit operation code, 8-bit immediate, and four 8-bit registers  |
|  NONE	 |  8-bit operation code  |
|  PREF_IMM16	 |  16-bit prefix operation code, 16-bit immediate  |
|  PREF_IMM16_ID16	 |  16-bit prefix operation code, 16-bit immediate, and 16-bit id  |
|  PREF_IMM16_V8	 |  16-bit prefix operation code, 16-bit immediate, and 8-bit register  |
|  PREF_IMM16_V8_V8	 |  16-bit prefix operation code, 16-bit immediate, and two 8-bit registers  |
|  PREF_IMM8	 |  16-bit prefix operation code and 8-bit immediate  |
|  PREF_NONE	 |  16-bit prefix operation code  |
|  PREF_V8	 |  16-bit prefix operation code and 8-bit register  |
|  PREF_V8_ID16	 |  16-bit prefix operation code, 8-bit register, and16-bit id  |
|  PREF_V8_IMM32	 |  16-bit prefix operation code, 8-bit register, and 32-bit immediate  |
|  V16_V16	 |  8-bit operation code and two 16-bit registers  |
|  V4_V4	 |  8-bit operation code and two 4-bit registers  |
|  V8	 |  8-bit operation code and 8-bit register  |
|  V8_IMM16	 |  8-bit operation code, 8-bit register, and 16-bit immediate  |
|  V8_IMM8	 |  8-bit operation code, 8-bit register, and 8-bit immediate  |
|  V8_V8	 |  8-bit operation code and two 8-bit registers  |
|  V8_V8_V8	 |  8-bit operation code and three 8-bit registers  |
|  V8_V8_V8_V8	 |  8-bit operation code and four 8-bit registers  |

## Bytecode Summary
The table below summarizes all Ark Bytecodes in the current version. The register index, immediate, and id are described in the form of one character for every four-bit width.<br>
Take the *defineclasswithbuffer RR, @AAAA, @BBBB, +CCCC, vDD* instruction as an example:<br>
* *defineclasswithbuffer*: indicates the operation code mnemonic.
* *RR*: 8-bit reserved number used internally during Ark runtime. The number mentioned here is just an example showing a complete instruction format.
* *@AAAA, @BBBB*: 16-bit id
* *+CCCC*: 16-bit immediate
* *vDD*: 8-bit register index

|    Operation Code  |   Format   |  Mnemonic/Syntax  |     Parameter      |      Description    |
|   -------   |  -------  |  ----------  |   ----------   |   --------   |
|  0x00	|  NONE	|  ldundefined	|   |  	Load **undefined** to acc.  |
|  0x01	|  NONE	|  ldnull	|   |  	Load **null** to acc.  |
|  0x02	|  NONE	|  ldtrue	|   |  	Load **true** to acc.  |
|  0x03	|  NONE	|  ldfalse	|   |  	Load **false** to acc.  |
|  0x04	|  NONE	|  createemptyobject	|   |  	Create an empty object and store it in acc.  |
|  0x05	|  IMM8|  	createemptyarray RR	|  R: 8-bit reserved number used in Ark runtime	|  Create an empty array and store it in acc.  |
|  0x06	|  IMM8_ID16	|  createarraywithbuffer RR, @AAAA	|  R: 8-bit reserved number used in Ark runtime<br>A: 16-bit literal id	|  Use the literal array corresponding to index A to create an array object and store it in acc.  |
|  0x07	|  IMM8_ID16	|  createobjectwithbuffer RR, @AAAA	|  R: 8-bit reserved number used in Ark runtime<br>A: 16-bit literal id	|  Use the literal array corresponding to index A to create an object and store it in acc.  |
|  0x08	|  IMM8_IMM8_V8	|  newobjrange RR, +AA, vBB	|  R: 8-bit reserved number used in Ark runtime<br>A: number of parameters<br>B: class object<br>B + 1, ..., B + A - 1: parameter passed to the constructor |  	Use **B + 1, ..., B + A - 1** as a parameter to create an instance of class B and store it in acc.  |
|  0x09	|  IMM8	|  newlexenv +AA	|  A: number of slots in the lexical environment	|  Create a lexical environment with slot A, store it in acc, and enter the lexical environment.  |
|  0x0a	|  IMM8_V8	|  add2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A + acc** and store the result in acc.  |
|  0x0b	|  IMM8_V8	|  sub2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A - acc** and store the result in acc.  |
|  0x0c	|  IMM8_V8	|  mul2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A * acc** and store the result in acc.  |
|  0x0d	|  IMM8_V8	|  div2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A / acc** and store the result in acc.  |
|  0x0e	|  IMM8_V8	|  mod2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A % acc** and store the result in acc.  |
|  0x0f	|  IMM8_V8	|  eq RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A == acc** and store the result in acc.  |
|  0x10	|  IMM8_V8	|  noteq RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A != acc** and store the result in acc.  |
|  0x11	|  IMM8_V8	|  less RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A < acc** and store the result in acc.  |
|  0x12	|  IMM8_V8	|  lesseq RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A <= acc** and store the result in acc.  |
|  0x13	|  IMM8_V8	|  greater RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A > acc** and store the result in acc.  |
|  0x14	|  IMM8_V8	|  greatereq RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A >= acc** and store the result in acc.  |
|  0x15	|  IMM8_V8	|  shl2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A << acc** and store the result in acc.  |
|  0x16	|  IMM8_V8	|  shr2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A >>> acc** and store the result in acc.  |
|  0x17	|  IMM8_V8	|  ashr2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A >> acc** and store the result in acc.  |
|  0x18	|  IMM8_V8	|  and2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A & acc** and store the result in acc.  |
|  0x19	|  IMM8_V8	|  or2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A \| acc** and store the result in acc.  |
|  0x1a	|  IMM8_V8	|  xor2 RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A ^ acc** and store the result in acc. |
|  0x1b	|  IMM8_V8	|  exp RR, vAA	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime<br>A: operand	|  Calculate **A** **\**** **acc** and store the result in acc.  |
|  0x1c	|  IMM8	|  typeof RR	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime	|  Calculate **typeof acc** and store the result in acc.  |
|  0x1d	|  IMM8	|  tonumber RR	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime	|  Use acc as a parameter, execute [ToNumber](https://262.ecma-international.org/12.0/#sec-tonumber), and store the result in acc.  |
|  0x1e	|  IMM8	|  tonumeric RR	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime	|  Use acc as a parameter, execute [ToNumeric](https://262.ecma-international.org/12.0/#sec-tonumeric), and store the result in acc.  |
|  0x1f	|  IMM8	|  neg RR	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime	|  Calculate **-acc** and store the result in acc.  |
|  0x20	|  IMM8	|  not RR	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime	|  Calculate **~acc** and store the result in acc.  |
|  0x21	|  IMM8	|  inc RR	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime	|  Calculate **acc + 1** and store the result in acc.  |
|  0x22	|  IMM8	|  dec RR	|  Default input parameter: acc: operand<br>R: 8-bit reserved number used in Ark runtime	|  Calculate **acc - 1** and store the result in acc.  |
|  0x23	|  NONE	|  istrue	|  Default input parameter: acc: object	|  Calculate **acc == true** and store the result in acc.  |
|  0x24	|  NONE	|  isfalse	|  Default input parameter: acc: object	|  Calculate **acc == false** and store the result in acc.  |
|  0x25	|  IMM8_V8	|  isin RR, vAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Calculate **A in acc** and store the result in acc.  |
|  0x26	|  IMM8_V8	|  instanceof RR, vAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Calculate **A instanceof acc** and store the result in acc.  |
|  0x27	|  IMM8_V8	|  strictnoteq RR, vAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Calculate **acc !== A** and store the result in acc.  |
|  0x28	|  IMM8_V8	|  stricteq RR, vAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Calculate **acc === A** and store the result in acc.  |
|  0x29	|  IMM8	|  callarg0 RR	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime	|  Directly call the function object stored in acc without passing parameters and store the result in acc.  |
|  0x2a	|  IMM8_V8	|  callarg1 RR, vAA	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: parameter	|  Use **A** as a parameter to call the function object stored in acc and store the result in acc.  |
|  0x2b	|  IMM8_V8_V8	|  callargs2 RR, vAA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A, B: parameter	|  Use **A** and **B** as parameters to call the function object stored in acc and store the result in acc.  |
|  0x2c	|  IMM8_V8_V8_V8	|  callargs3 RR, vAA, vBB, vCC	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A, B, C: parameter	|  Use **A**, **B**, and **C** as parameters to call the function object stored in acc and store the result in acc.  |
|  0x2d	|  IMM8_V8	|  callthis0 RR, vAA	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Set **this** to **A**, call the function object stored in acc without passing parameters, and store the result in acc.  |
|  0x2e	|  IMM8_V8_V8	|  callthis1 RR, vAA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: parameter	|  Set **this** to **A**, call the function object stored in acc by setting **B** as the parameter, and store the result in acc.  |
|  0x2f	|  IMM8_V8_V8_V8	|  callthis2 RR, vAA, vBB, vCC	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B, C: parameter |  Set **this** to **A**, call the function object stored in acc by setting **B** and **C** as parameters, and store the result in acc.  |
|  0x30	|  IMM8_V8_V8_V8_V8	|  callthis3 RR, vAA, vBB, vCC, vDD	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B, C, D: parameter |  Set **this** to **A**, call the function object stored in acc by setting **B**, **C**, and **D** as parameters, and store the result in acc.  |
|  0x31	|  IMM8_IMM8_V8	|  callthisrange RR, +AA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: number of parameters<br>B: object<br>B + 1, ..., B + A: parameter	|  Set **this** to **B**, call the function object stored in acc by setting **B + 1, ..., B + A** as the parameter, and store the result in acc.  |
|  0x32	|  IMM8_IMM8_V8	|  supercallthisrange RR, +AA, vBB	|  R: 8-bit reserved number used in Ark runtime<br>A: number of parameters<br>B, ..., B + A - 1: parameter	|  Use **B, ..., B + A - 1** as the parameter to call the **super** function and store the result in acc.<br>When the value of **A** is **0**, the value of **B** is **undefined**.<br>This instruction appears only in non-arrow functions.  |
|  0x33	|  IMM8_ID16_IMM8	|  definefunc RR, @AAAA, +BB	|  R: 8-bit reserved number used in Ark runtime<br>A: method id<br>B: number of formal parameters of method A | Create the function object of method A and store it in acc.  |
|  0x34	|  IMM8_ID16_IMM8	|  definemethod RR, @AAAA, +BB	|  Default input parameter: acc: class object or its prototype. When the static method is used, the parameter is a class object in acc.<br>R: 8-bit reserved number used in Ark runtime<br>A: method id<br>B: number of formal parameters of method A	|  Create the function object of method A, set the object of acc to the [[[HomeObject]]](https://262.ecma-international.org/12.0/#sec-ecmascript-function-objects) attribute of the function object, and store this function object in acc.  |
|  0x35	|  IMM8_ID16_ID16_IMM16_V8	|  defineclasswithbuffer RR, @AAAA, @BBBB, +CCCC, vDD	|  R: 8-bit reserved number used in Ark runtime<br>A: **method id** of the constructor of a class<br>B: literal id<br>C: number of formal parameters of method A<br>D: parent class	|  Use the literal array corresponding to index B and parent class D to create a class object of A and store it in acc.  |
|  0x36	|  V8	|  getnextpropname vAA	| A: iterator	|  Execute the [next](https://262.ecma-international.org/12.0/#sec-%25foriniteratorprototype%25.next) method of [for-in iterator](https://262.ecma-international.org/12.0/#sec-createiterresultobject) A and store the result in acc.  |
|  0x37	|  IMM8_V8	|  ldobjbyvalue RR, vAA	|  Default input parameter: acc: attribute key<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Load the attribute whose key is acc of object A and store the result in acc. |
|  0x38	|  IMM8_V8_V8	|  stobjbyvalue RR, vAA, vBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x39	|  IMM8_V8	|  ldsuperbyvalue RR, vAA	|  Default input parameter: acc: attribute key<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  In the current function, obtain the attribute whose key of **super** is acc and store the attribute in acc. If the attribute is of an accessor, the object in A is used as the **this** parameter when the **getter** function of the attribute is called.  |
|  0x3a	|  IMM8_IMM16	|  ldobjbyindex RR, +AAAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: attribute key	|  Load the attribute whose key is A of the object stored in acc and store the attribute in acc.  |
|  0x3b	|  IMM8_V8_IMM16	|  stobjbyindex RR, vAA, +BBBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x3c	|  IMM4_IMM4	|  ldlexvar +A, +B	|  A: lexical environment level<br>B: slot number	|  Store the value of slot B in the lexical environment beyond A levels in acc.  |
|  0x3d	|  IMM4_IMM4	|  stlexvar +A, +B	|  Default input parameter: acc: value<br>A: lexical environment level<br>B: slot number	|  Store the value in acc to slot B in the lexical environment beyond A levels.  |
|  0x3e	|  ID16	|  lda.str @AAAA	|  A: string id	|  Store the string corresponding to index A to acc.  |
|  0x3f	|  IMM8_ID16	|  tryldglobalbyname RR, @AAAA	|  R: 8-bit reserved number used in Ark runtime<br>A: string id	|  Store the global variable whose name is the string corresponding to index A in acc. If the global variable named A does not exist, an exception is thrown.   |
|  0x40	|  IMM8_ID16	|  trystglobalbyname RR, @AAAA	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: string id	|  Store the value in acc to the global variable whose name is the string corresponding to index A. If the global variable named A does not exist, an exception is thrown.  |
|  0x41	|  IMM16_ID16	|  ldglobalvar RRRR, @AAAA	|  R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the value of the global variable whose name is the string corresponding to index A in acc. The variable must exist.  |
|  0x42	|  IMM8_ID16	|  ldobjbyname RR, @AAAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: string id	|  Load the attribute whose key of the object stored in acc is the string corresponding to index A and store the attribute in acc.  |
|  0x43	|  IMM8_ID16_V8	|  stobjbyname RR, @AAAA, vBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  Store the value of acc to the attribute whose key of object B is the string corresponding to index A.  |
|  0x44	|  V4_V4	|  mov vA, vB	|  A, B: register index	|  Copy the contents in register B to register A.  |
|  0x45	|  V8_V8	|  mov vAA, vBB	|  A, B: register index	|  Copy the contents in register B to register A.  |
|  0x46	|  IMM8_ID16	|  ldsuperbyname RR, @AAAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: string id	|  In the current function, obtain the attribute whose key of **super** is the string corresponding to index A and store the attribute in acc. If the attribute is of an accessor, the object in acc is used as the **this** parameter when the **getter** function of the attribute is called.  |
|  0x47	|  IMM16_ID16	|  stconsttoglobalrecord RRRR, @AAAA	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the value of acc to the constant of the string corresponding to index A defined by **const** in the global variable.  |
|  0x48	|  IMM16_ID16	|  sttoglobalrecord RRRR, @AAAA	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the value of acc to the variable of the string corresponding to index A defined by **let** in the global variable.  |
|  0x49	|  IMM8_ID16	|  ldthisbyname RR, @AAAA	|  R: 8-bit reserved number used in Ark runtime<br>A: string id	|  Load the attribute whose key of **this** is the string corresponding to index A and store the result in acc.  |
|  0x4a	|  IMM8_ID16	|  stthisbyname RR, @AAAA	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: string id	|  Store the value of acc to the attribute whose key of **this** is the string corresponding to index A.  |
|  0x4b	|  IMM8	|  ldthisbyvalue RR	|  Default input parameter: acc: attribute key<br>R: 8-bit reserved number used in Ark runtime	|  Load the attribute whose key of **this** is acc and store the result in acc.  |
|  0x4c	|  IMM8_V8	|  stthisbyvalue RR, vAA	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: attribute key	|  Store the value of acc to the attribute whose key of **this** is A.  |
|  0x4d	|  IMM8	|  jmp +AA	|  A: signed branch offset	|  Jump to branch A unconditionally.  |
|  0x4e	|  IMM16	|  jmp +AAAA	|  A: signed branch offset	|  Jump to branch A unconditionally.  |
|  0x4f	|  IMM8	|  jeqz +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == 0** and jump to branch A if it is true.  |
|  0x50	|  IMM16	|  jeqz +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == 0** and jump to branch A if it is true.  |
|  0x51	|  IMM8	|  jnez +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != 0** and jump to branch A if it is true.  |
|  0x52	|  IMM8	|  jstricteqz +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc === 0** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x53	|  IMM8	|  jnstricteqz +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc !== 0** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x54	|  IMM8	|  jeqnull +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x55	|  IMM8	|  jnenull +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x56	|  IMM8	|  jstricteqnull +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc === null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x57	|  IMM8	|  jnstricteqnull +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc !== null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x58	|  IMM8	|  jequndefined +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x59	|  IMM8	|  jneundefined +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x5a	|  IMM8	|  jstrictequndefined +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc === undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x5b	|  IMM8	|  jnstrictequndefined +AA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc !== undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x5c	|  V8_IMM8	|  jeq vAA, +BB	|  Default input parameter: acc: value<br>A: value<br>B: signed branch offset	|  Calculate **acc == A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x5d	|  V8_IMM8	|  jne vAA, +BB	|  Default input parameter: acc: value<br>A: value<br>B: signed branch offset	|  Calculate **acc != A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x5e	|  V8_IMM8	|  jstricteq vAA, +BB	|  Default input parameter: acc: object<br>A: object<br>B: signed branch offset	|  Calculate **acc === A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x5f	|  V8_IMM8	|  jnstricteq vAA, +BB	|  Default input parameter: acc: object<br>A: object<br>B: signed branch offset	|  Calculate **acc !== A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x60	|  V8	|  lda vAA	|  A: register index	|  Store the contents of register A in acc.  |
|  0x61	|  V8	|  sta vAA	|  Default input parameter: acc<br>A: register index	|  Store the contents of acc in register A.  |
|  0x62	|  IMM32	|  ldai +AAAAAAAA	|  A: constant literal	|  Store the integer literal A in acc.  |
|  0x63	|  IMM64	|  fldai +AAAAAAAAAAAAAAAA	|  A: constant literal	|  Store the double-precision floating-point literal A in acc.  |
|  0x64	|  NONE	|  return	|  Default input parameter: acc: value	|  Returns the value in acc.  |
|  0x65	|  NONE	|  returnundefined	 | 	|  **undefined** is returned.  |
|  0x66	|  NONE	|  getpropiterator	|  Default input parameter: acc: object	|  Stores the [for-in iterator](https://262.ecma-international.org/12.0/#sec-createiterresultobject) of the object in acc.  |
|  0x67	|  IMM8	|  getiterator RR	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime	|  Execute the [GetIterator](https://262.ecma-international.org/12.0/#sec-getiterator) (acc, sync) method and store the result in acc.  |
|  0x68	|  IMM8_V8	|  closeiterator RR, vAA	|  R: 8-bit reserved number used in Ark runtime<br>A: object	|  Use A of the *[iteratorRecord](https://262.ecma-international.org/12.0/#sec-iterator-records)* type as a parameter to execute [IteratorClose](https://262.ecma-international.org/12.0/#sec-iteratorclose) and store the result in acc.  |
|  0x69	|  NONE	|  poplexenv	|   	|  Jump out of the current lexical environment and enter the outer lexical environment.  |
|  0x6a	|  NONE	|  ldnan	|   	|  Store the **nan** value in acc.  |
|  0x6b	|  NONE	|  ldinfinity	|   	|  Store the **infinity** value in acc.  |
|  0x6c	|  NONE	|  getunmappedargs	 |  	|  Store the **arguments** of the current function in acc.  |
|  0x6d	|  NONE	|  ldglobal	 |  	|  Store the **global** object in acc. |
|  0x6e	|  NONE	|  ldnewtarget	 |  	|  Store the **NewTarget** implicit parameter of the current function in acc.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x6f	|  NONE	|  ldthis	|   	|  Store the `this` value in acc.  |
|  0x70	|  NONE	|  ldhole	 |  	|  Store the **hole** in acc.  |
|  0x71	|  IMM8_ID16_IMM8	|  createregexpwithliteral RR, @AAAA, +BB	|  R: 8-bit reserved number used in Ark runtime<br>A: string id<br>B: regular expression modifier	|  Use the string corresponding to index A and the modifier corresponding to index B to create a regular expression and store it in acc.<br>The correspondence between B and a specified modifier is: 0 (default value with no modifier), 1 (g), 2 (i), 4 (m), 8 (s), 16 (u), 32 (y); B may also refer to a combination of modifiers that comply with syntax specifications, for example, **3** and its modifier is **gi**. |
|  0x72	|  IMM16_ID16_IMM8	|  createregexpwithliteral RRRR, @AAAA, +BB	|  R: 16-bit reserved number used in Ark runtime<br>A: string id<br>B: regular expression modifier	|  Use the string corresponding to index A and the modifier corresponding to index B to create a regular expression and store it in acc.<br>The correspondence between B and a specified modifier is: 0 (default value with no modifier), 1 (g), 2 (i), 4 (m), 8 (s), 16 (u), 32 (y); B may also refer to a combination of modifiers that comply with syntax specifications, for example, **3** and its modifier is **gi**. |
|  0x73	|  IMM8_IMM8_V8	|  callrange RR, +AA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: number of parameters<br>B, ..., B + A - 1: parameter	|  Use **B, ..., B + A - 1** as a parameter to call the function object stored in acc and store the result in acc.  |
|  0x74	|  IMM16_ID16_IMM8	|  definefunc RRRR, @AAAA, +BB	|  R: 16-bit reserved number used in Ark runtime<br>A: method id<br>B: number of formal parameters of method A	|  Create the function object of method A and store it in acc.  |
|  0x75	|  IMM16_ID16_ID16_IMM16_V8	|  defineclasswithbuffer RRRR, @AAAA, @BBBB, +CCCC, vDD	|  R: 16-bit reserved number used in Ark runtime<br>A: **method id** of the constructor of a class<br>B: literal id<br>C: number of formal parameters of method A<br>D: parent class	|  Use the literal array corresponding to index B and parent class D to create a class object of A and store it in acc.  |
|  0x76	|  IMM8	|  gettemplateobject RR	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime	|  Execute [GetTemplateObject](https://262.ecma-international.org/12.0/#sec-gettemplateobject) (acc) and store the result in acc.  |
|  0x77	| IMM8_V8	| setobjectwithproto RR, vAA	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime<br>A: value	|  Set the **\_\_proto\_\_** attribute of the object stored in acc to A.  |
|  0x78	|  IMM8_V8_V8	|  stownbyvalue RR, vAA, vBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x79	|  IMM8_V8_IMM16	|  stownbyindex RR, vAA, +BBBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x7a	|  IMM8_ID16_V8	|  stownbyname RR, @AAAA, vBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  Store the value of acc to the attribute whose key of object B is the string corresponding to index A.  |
|  0x7b	|  IMM8	|  getmodulenamespace +AA	|  A: module index	|  Execute the [GetModuleNamespace](https://262.ecma-international.org/12.0/#sec-getmodulenamespace) instruction for module A and store the result in acc.  |
|  0x7c	|  IMM8	|  stmodulevar +AA	|  Default input parameter: acc: value<br>A: slot number	|  Store the value of acc to the module variable of slot A.  |
|  0x7d	|  IMM8	|  ldlocalmodulevar +AA	|  A: slot number	|  Store the local module variables of slot A in acc.  |
|  0x7e	|  IMM8	|  ldexternalmodulevar +AA	|  A: slot number	|  Store the external module variable of slot A in acc.  |
|  0x7f	|  IMM16_ID16	|  stglobalvar RRRR, @AAAA	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the value in acc to the global variable whose name is the string corresponding to index A. This variable must exist.  |
|  0x80	|  IMM16	|  createemptyarray RRRR	|  R: 16-bit reserved number used in Ark runtime	|  Create an empty array and store it in acc.  |
|  0x81	|  IMM16_ID16	|  createarraywithbuffer RRRR, @AAAA	|  R: 16-bit reserved number used in Ark runtime<br>A: literal id	|  Use the literal array corresponding to index A to create an array object and store it in acc.  |
|  0x82	|  IMM16_ID16	|  createobjectwithbuffer RRRR, @AAAA	|  R: 16-bit reserved number used in Ark runtime<br>A: literal id	|  Use the literal array corresponding to index A to create an object and store it in acc.  |
|  0x83	|  IMM16_IMM8_V8	|  newobjrange RRRR, +AA, vBB	|  R: 16-bit reserved number used in Ark runtime<br>A: number of parameters<br>B: class object<br>B + 1, ..., B + A - 1: parameter passed to the constructor	|  Use **B + 1, ..., B + A - 1** as a parameter to create an instance of class B and store it in acc.  |
|  0x84	|  IMM16	|  typeof RRRR	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime	|  Calculate **typeof acc** and store the result in acc.  |
|  0x85	|  IMM16_V8	|  ldobjbyvalue RRRR, vAA	|  Default input parameter: acc: attribute key<br>R: 16-bit reserved number used in Ark runtime<br>A: object	|  Load the attribute whose key is acc of object A and store the result in acc.  |
|  0x86	|  IMM16_V8_V8	|  stobjbyvalue RRRR, vAA, vBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x87	|  IMM16_V8	|  ldsuperbyvalue RRRR, vAA	|  Default input parameter: acc: attribute key<br>R: 16-bit reserved number used in Ark runtime<br>A: object	|  In the current function, obtain the attribute whose key of **super** is acc and store the attribute in acc. If the attribute is of an accessor, the object in A is used as the **this** parameter when the **getter** function of the attribute is called.  |
|  0x88	|  IMM16_IMM16	|  ldobjbyindex RRRR, +AAAA	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime<br>A: attribute key	|  Load the attribute whose key is A of the object stored in acc and store the attribute in acc.  |
|  0x89	|  IMM16_V8_IMM16	|  stobjbyindex RRRR, vAA, +BBBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x8a	|  IMM8_IMM8	|  ldlexvar +AA, +BB	|  A: lexical environment level<br>B: slot number	|  Store the value of slot B in the lexical environment beyond A levels in acc.  |
|  0x8b	|  IMM8_IMM8	|  stlexvar +AA, +BB	|  Default input parameter: acc: value<br>A: lexical environment level<br>B: slot number	|  Store the value in acc to slot B in the lexical environment beyond A levels.  |
|  0x8c	|  IMM16_ID16	|  tryldglobalbyname RRRR, @AAAA	|  R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the global variable whose name is the string corresponding to index A in acc. If the global variable named A does not exist, an exception is thrown.   |
|  0x8d	|  IMM16_ID16	|  trystglobalbyname RRRR, @AAAA	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the value in acc to the global variable whose name is the string corresponding to index A. If the global variable named A does not exist, an exception is thrown.  |
|  0x8e	|  IMM8_ID16_V8	|  stownbynamewithnameset RR, @AAAA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  Store the function object in acc to the attribute whose key of object B is the string corresponding to index A and set the function name to the string corresponding to index A.  |
|  0x8f	|  V16_V16	|  mov vAAAA, vBBBB	|  A, B: register index	|  Copy the contents in register B to register A.  |
|  0x90	|  IMM16_ID16	|  ldobjbyname RRRR, @AAAA	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Load the attribute whose key of the object stored in acc is the string corresponding to index A and store the attribute in acc.  |
|  0x91	|  IMM16_ID16_V8	|  stobjbyname RRRR, @AAAA, vBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  Store the value of acc to the attribute whose key of object B is the string corresponding to index A.  |
|  0x92	|  IMM16_ID16	|  ldsuperbyname RRRR, @AAAA	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  In the current function, obtain the attribute whose key of **super** is the string corresponding to index A and store the attribute in acc. If the attribute is of an accessor, the object in acc is used as the **this** parameter when the **getter** function of the attribute is called.  |
|  0x93	|  IMM16_ID16	|  ldthisbyname RRRR, @AAAA	|  R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Load the attribute whose key of **this** is the string corresponding to index A and store the result in acc.  |
|  0x94	|  IMM16_ID16	|  stthisbyname RRRR, @AAAA	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id	|  Store the value of acc to the attribute whose key of **this** is the string corresponding to index A.  |
|  0x95	|  IMM16	|  ldthisbyvalue RRRR	|  Default input parameter: acc: attribute key<br>R: 16-bit reserved number used in Ark runtime	|  Load the attribute whose key of **this** is acc and store the result in acc.  |
|  0x96	|  IMM16_V8	|  stthisbyvalue RRRR, vAA	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: attribute key	|  Store the value of acc to the attribute whose key of **this** is A.  |
|  0x97	|  V8	|  asyncgeneratorreject vAA	 |  Default input parameter: acc: exception<br>A: generator	|  Use the exception stored in *[generator](https://262.ecma-international.org/12.0/#sec-generator-objects)* A and acc, execute [AsyncGeneratorReject](https://262.ecma-international.org/12.0/#sec-asyncgeneratorreject), and store the result in acc.  |
|  0x98	|  IMM32	|  jmp +AAAAAAAA	|  A: signed branch offset	|  Jump to branch A unconditionally.  |
|  0x99	|  IMM8_V8_V8	|  stownbyvaluewithnameset RR, vAA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value of acc to the attribute whose key of object A is B and set the function name to B.  |
|  0x9a	|  IMM32	|  jeqz +AAAAAAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == 0** and jump to branch A if it is true.  |
|  0x9b	|  IMM16	|  jnez +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != 0** and jump to branch A if it is true.  |
|  0x9c	|  IMM32	|  jnez +AAAAAAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != 0** and jump to branch A if it is true.  |
|  0x9d	|  IMM16	|  jstricteqz +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc === 0** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x9e	|  IMM16	|  jnstricteqz +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc !== 0** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0x9f	|  IMM16	|  jeqnull +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa0	|  IMM16	|  jnenull +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa1	|  IMM16	|  jstricteqnull +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc === null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa2	|  IMM16	|  jnstricteqnull +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc !== null** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa3	|  IMM16	|  jequndefined +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc == undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa4	|  IMM16	|  jneundefined +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc != undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa5	|  IMM16	|  jstrictequndefined +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc === undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa6	|  IMM16	|  jnstrictequndefined +AAAA	|  Default input parameter: acc: value<br>A: signed branch offset	|  Calculate **acc !== undefined** and jump to branch A if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa7	|  V8_IMM16	|  jeq vAA, +BBBB	|  Default input parameter: acc: value<br>A: value<br>B: signed branch offset	|  Calculate **acc == A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa8	|  V8_IMM16	|  jne vAA, +BBBB	|  Default input parameter: acc: value<br>A: value<br>B: signed branch offset	|  Calculate **acc != A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xa9	|  V8_IMM16	|  jstricteq vAA, +BBBB	|  Default input parameter: acc: value<br>A: value<br>B: signed branch offset	|  Calculate **acc === A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xaa	|  V8_IMM16	|  jnstricteq vAA, +BBBB	|  Default input parameter: acc: value<br>A: value<br>B: signed branch offset	|  Calculate **acc !== A** and jump to branch B if it is true.<br>The instruction feature is disabled and is unavailable currently.  |
|  0xab	|  IMM16	|  getiterator RRRR	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime	|  Execute the [GetIterator](https://262.ecma-international.org/12.0/#sec-getiterator) (acc, sync) method and store the result in acc.  |
|  0xac	|  IMM16_V8	|  closeiterator RRRR, vAA	|  R: 16-bit reserved number used in Ark runtime<br>A: object	|  Use A of the *[iteratorRecord](https://262.ecma-international.org/12.0/#sec-iterator-records)* type as a parameter to execute [IteratorClose](https://262.ecma-international.org/12.0/#sec-iteratorclose) and store the result in acc.  |
|  0xad	|  NONE	|  ldsymbol	 |  	|  Load the **Symbol** object in acc.  |
|  0xae	|  NONE	|  asyncfunctionenter	|   	|  Create an asynchronous function object and store the object in acc.  |
|  0xaf	|  NONE	|  ldfunction	|   	|  Load the current function object in acc.  |
|  0xb0	|  NONE	|  debugger	|   	|  This command is used to pause execution during debugging.  |
|  0xb1	|  V8	|  creategeneratorobj vAA	|  A: function object	|  Use function object A to create a *generator* and store it in acc.  |
|  0xb2	|  V8_V8	|  createiterresultobj vAA, vBB	|  A: object<br>B: Boolean value	|  Set *value* A and *done* B as parameters to execute [CreateIterResultObject](https://262.ecma-international.org/12.0/#sec-createiterresultobject) instruction and store the result in acc.  |
|  0xb3	|  IMM8_V8_V8	|  createobjectwithexcludedkeys +AA, vBB, vCC	|  A: number of range registers<br>B: object<br>C, ..., C + A: attribute key value.	|  Based on object B, create an object excluding the key **C, ..., C + A** and store it in acc.<br>This instruction supports object creation by using destructor and extension syntax.  |
|  0xb4	|  IMM8_V8	|  newobjapply RR, vAA	|  Default input parameter: acc: parameter list<br>R: 8-bit reserved number used in Ark runtime<br>A: class object	|  Use the parameter list stored in acc to create an instance of class A and store it in acc.  |
|  0xb5	|  IMM16_V8	|  newobjapply RRRR, vAA	|  Default input parameter: acc: parameter list<br>R: 16-bit reserved number used in Ark runtime<br>A: class object	|  Use the parameter list stored in acc to create an instance of class A and store it in acc.  |
|  0xb6	|  IMM8_ID16	|  newlexenvwithname +AA, @BBBB	|  A: number of slots in the lexical environment<br>B: literal id	|  Use the lexical variable name stored in the literal array corresponding to index B to create a lexical environment with A slots, store this lexical environment in acc, and enter.  |
|  0xb7	|  V8	|  createasyncgeneratorobj vAA	|  A: function object	|  Create an asynchronous *generator* based on function object A and store it in acc.  |
|  0xb8	|  V8_V8_V8	|  asyncgeneratorresolve vAA, vBB, vCC	|  A: generator<br>B: object<br>C: boolean value	|  Use *generator* A, *value* B, and *done* C as parameters to execute [AsyncGeneratorResolve](https://262.ecma-international.org/12.0/#sec-asyncgeneratorresolve) and store the result in acc.  |
|  0xb9	|  IMM8_V8	|  supercallspread RR, vAA	|  Default input parameter: acc: class object<br>R: 8-bit reserved number used in Ark runtime<br>A: parameter list	|  Use parameter list A as a parameter, call the parent class constructor of the class stored in acc, and store the result in acc.  |
|  0xba	|  IMM8_V8_V8	|  apply RR, vAA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: parameter list	|  Set **this** to A, use parameter list B as the parameter, call the function object stored in acc, and store the return value in acc.  |
|  0xbb	|  IMM8_IMM8_V8	|  supercallarrowrange RR, +AA, vBB	|  Default input parameter: acc: class object<br>R: 8-bit reserved number used in Ark runtime<br>A: number of parameters<br>B, ..., B + A - 1: parameter	|  Use **B, ..., B + A - 1** as a parameter to call the constructor function of the parent class of the class stored in acc and store the result in acc.<br>If the value of A is **0**, B is **undefined**.<br>This instruction appears only in arrow functions.  |
|  0xbc	|  V8_V8_V8_V8	|  definegettersetterbyvalue vAA, vBB, vCC, vDD	|  Default input parameter: acc: boolean value, indicating whether to set a name for the accessor.<br>A: object<br>B: attribute key<br>C: **getter** function object<br>D: **setter** function object	|  Use **getter** method C and **setter** method D as parameters to define the accessor of the attribute whose key of object A is B and store the result object in acc.<br>If C is **undefined** and D is **undefined**, **getter** and **setter** are not set respectively.  |
|  0xbd	|  NONE	|  dynamicimport	|  Default input parameter: acc: value	|  Use the value of acc as a parameter to execute [ImportCalls](https://262.ecma-international.org/12.0/#sec-import-calls) and store the result in acc.  |
|  0xbe	|  IMM16_ID16_IMM8	|  definemethod RRRR, @AAAA, +BB	|  Default input parameter: acc: class object or its prototype. When the static method is used, the parameter is a class object in acc.<br>R: 16-bit reserved number used in Ark runtime<br>A: method id<br>B: number of formal parameters of method A	|  Create the function object of method A, set the object of acc to the [[[HomeObject]]](https://262.ecma-international.org/12.0/#sec-ecmascript-function-objects) attribute of the function object, and store this function object in acc.  |
|  0xbf	|  NONE	|  resumegenerator	|  Default input parameter: acc: generator	|  Execute [GeneratorResume](https://262.ecma-international.org/12.0/#sec-generatorresume) based on the generator stored in acc and store the result in acc.  |
|  0xc0	|  NONE	|  getresumemode	|  Default input parameter: acc: generator	|  After the generator finishes executing, obtain the restored value and store it in acc.  |
|  0xc1	|  IMM16	|  gettemplateobject RRRR	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime	|  Execute [GetTemplateObject](https://262.ecma-international.org/12.0/#sec-gettemplateobject) (acc) and store the result in acc.  |
|  0xc2	|  V8	|  delobjprop vAA	|  Default input parameter: acc: attribute key<br>A: object	|  Delete the attribute whose key is acc of object A.  |
|  0xc3	|  V8	|  suspendgenerator vAA	|  Default input parameter: acc: value<br>A: generator	|  Use the value stored in acc to suspend *generator* A and store the result in acc.  |
|  0xc4	|  V8	|  asyncfunctionawaituncaught vAA	|  Default input parameter: acc: value<br>A: function object	|  Use the function object A and the value of acc to execute [AwaitExpression](https://262.ecma-international.org/12.0/#prod-AwaitExpression) and store the result in acc.  |
|  0xc5	|  V8	|  copydataproperties vAA	|  Default input parameter: acc: object<br>A: target object	|  Copy all attributes of the object stored in acc to A and store A in acc.  |
|  0xc6	|  V8_V8	|  starrayspread vAA, vBB	|  Default input parameter: acc: value<br>A: array<br>B: array index	|  Store the value in acc to the position starting with index B of array A in the format of [SpreadElement](https://262.ecma-international.org/12.0/#prod-SpreadElement), and store the length of the result array in acc.  |
|  0xc7	|  IMM16_V8	|  setobjectwithproto RRRR, vAA	|  Default input parameter: acc: object<br>R: 16-bit reserved number used in Ark runtime<br>A: value	|  Set the **\_\_proto\_\_** attribute of the object stored in acc to A.  |
|  0xc8	|  IMM16_V8_V8	|  stownbyvalue RRRR, vAA, vBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0xc9	|  IMM8_V8_V8	|  stsuperbyvalue RR, vAA, vBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  In the current function, the value of acc is stored to the attribute whose key of **super** is B. If the attribute is of an accessor, the object in A is used as the **this** parameter when the **setter** function of the attribute is called.  |
|  0xca	|  IMM16_V8_V8	|  stsuperbyvalue RRRR, vAA, vBB | Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  In the current function, the value of acc is stored to the attribute whose key of **super** is B. If the attribute is of an accessor, the object in A is used as the **this** parameter when the **setter** function of the attribute is called.  |
|  0xcb	|  IMM16_V8_IMM16	|  stownbyindex RRRR, vAA, +BBBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0xcc	|  IMM16_ID16_V8	|  stownbyname RRRR, @AAAA, vBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  Store the value of acc to the attribute whose key of object B is the string corresponding to index A.  |
|  0xcd	|  V8	|  asyncfunctionresolve vAA	|  Default input parameter: acc: value<br>A: asynchronous function object	|  Use the value in acc to parse the **Promise** object of object A and store the result in acc.  |
|  0xce	|  V8	|  asyncfunctionreject vAA	|  Default input parameter: acc: value<br>A: asynchronous function object	|  Use the value in acc to reject the **Promise** object of object A and store the result in acc.  |
|  0xcf	|  IMM8	|  copyrestargs +AA	|  A: position of the [rest parameter](https://262.ecma-international.org/12.0/#prod-FunctionRestParameter) in the formal parameter list	|  Copy the rest parameters and store the parameter array copy in acc.  |
|  0xd0	|  IMM8_ID16_V8	|  stsuperbyname RR, @AAAA, vBB	|  Default input parameter: acc: value<br>R: 8-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  In the current function, store the value of acc to the attribute whose key of **super** is the string corresponding to index A.<br>If the attribute is of an accessor, the object in B is used as the **this** parameter when the **setter** function of the attribute is called.  |
|  0xd1	|  IMM16_ID16_V8	|  stsuperbyname RRRR, @AAAA, vBB	|  Default input parameter: acc: value<br>R: 16-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  In the current function, store the value of acc to the attribute whose key of **super** is the string corresponding to index A.<br>If the attribute is of an accessor, the object in B is used as the **this** parameter when the **setter** function of the attribute is called.  |
|  0xd2	|  IMM16_V8_V8	|  stownbyvaluewithnameset RRRR, vAA, vBB	|  Default input parameter: acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object<br>B: attribute key	|  Store the value of acc to the attribute whose key of object A is B and set the function name to B.  |
|  0xd3	|  ID16	|  ldbigint @AAAA	|  A: string id	|  Create a value of the **BigInt** type based on the string corresponding to index A and store the value in acc.  |
|  0xd4	|  IMM16_ID16_V8	|  stownbynamewithnameset RRRR, @AAAA, vBB	|  Default input parameter: acc: function object<br>R: 16-bit reserved number used in Ark runtime<br>A: string id<br>B: object	|  Store the function object in acc to the attribute whose key of object B is the string corresponding to index A and set the function name to the string corresponding to index A.  |
|  0xd5	|  NONE	|  nop	 |  	|  No operation.  |
|  0xd6	|  IMM8	|  setgeneratorstate +AA	|  Default input parameter: acc: generator object<br>A: generator state	|  Set the generator state stored in acc to A. For details, see [GeneratorState](https://262.ecma-international.org/12.0/#sec-properties-of-generator-instances) and [AsyncGeneratorState](https://262.ecma-international.org/12.0/#sec-properties-of-asyncgenerator-intances).<br>A may have the following values: undefined(0x0), suspendedStart(0x1), suspendedYield(0x2), executing(0x3), completed (0x4), and awaitingReturn (0x5).  |
|  0xd7	|  IMM8	|  getasynciterator RR	|  Default input parameter: acc: object<br>R: 8-bit reserved number used in Ark runtime	|  Execute the [GetIterator](https://262.ecma-international.org/12.0/#sec-getiterator) (acc, async) and store the result in acc.  |
|  0xd8	|  IMM8_IMM16_IMM16	|  ldprivateproperty RR, +AAAA, +BBBB	|  Default input parameter: acc: object<br>A: lexical environment level<br>B: slot number	|  Load and set the value of slot B in the lexical environment beyond A levels as the attribute key, and store the value corresponding to the key of the object in acc.  |
|  0xd9	|  IMM8_IMM16_IMM16_V8	|  stprivateproperty RR, +AAAA, +BBBB, vCC	|  A: lexical environment level<br>B: slot number<br>C: object	|  Load the value of slot B in the lexical environment beyond A levels as the attribute key and store the value in acc to the key of the object C stored in acc.  |
|  0xda	|  IMM8_IMM16_IMM16	|  testin RR, +AAAA, +BBBB	|  Default input parameter: acc: object<br>A: lexical environment level<br>B: slot number	|  Load the value of slot B in the lexical environment beyond A levels, calculate whether the value is **in acc**, and store the result in acc.  |
|  0xdb	|  IMM8_ID16_V8	|  definefieldbyname RR, @AAAA, vBB	|  Default input parameter: acc: value<br>A: string id<br>B: object	|  Define an attribute whose key is A for object B and store the value of acc to the attribute.  |
|  0xfb	|  PREF_NONE	|  callruntime.notifyconcurrentresult	|  Default input parameter: acc: return value of the concurrent function	|  Notify the runtime of the return value of the concurrent function.<br>This instruction is used only in concurrent functions.  |
|  0xfc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0xfd	|  PREF_IMM16_V8_V8	|  wide.createobjectwithexcludedkeys +AAAA, vBB, vCC	|  A: number of range registers<br>B: object<br>C, ..., C + A: attribute key value.	|  Based on object B, create an object excluding the key **C, ..., C + A** and store it in acc.<br>This instruction supports object creation by using destructor and extension syntax.  |
|  0xfe	|  PREF_NONE	|  throw	|  Default input parameter: acc: exception	|  Throws the exception stored in acc.  |
|  0x01fb	|  PREF_IMM8_V8_V8	|  callruntime.definefieldbyvalue RR, vAA, vBB	|  Default input parameter: acc: value<br>A: attribute key<br>B: object	|  Define an attribute whose key is A for object B and store the value of acc to the attribute.  |
|  0x01fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x01fd	|  PREF_IMM16_V8	|  wide.newobjrange +AAAA, vBB	|  A: number of parameters<br>B: class object<br>B + 1, ..., B + A - 1: parameter passed to the constructor	|  Use **B + 1, ..., B + A - 1** as a parameter to create an instance of class B and store it in acc.  |
|  0x01fe	|  PREF_NONE	|  throw.notexists	|   	|  Exception thrown: undefined method.  |
|  0x02fb	|  PREF_IMM8_IMM32_V8	|  callruntime.definefieldbyindex RR, +AAAAAAAA, vBB	|  Default input parameter: acc: value<br>A: attribute key<br>B: object	|  Define an attribute whose key is A for object B and store the value of acc to the attribute.  |
|  0x02fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x02fd	|  PREF_IMM16	|  wide.newlexenv +AAAA	|  A: number of slots in the lexical environment	|  Create a lexical environment with slot A, store it in acc, and enter the lexical environment.  |
|  0x02fe	|  PREF_NONE	|  throw.patternnoncoercible |   	|  Exception thrown: This object cannot be forcibly executed.  |
|  0x03fb	|  PREF_NONE	|  callruntime.topropertykey 	|  Default input parameter: acc: value	|  Convert the value in acc to the attribute value. If the conversion fails, an error is thrown.  |
|  0x03fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x03fd	|  PREF_IMM16_ID16	|  wide.newlexenvwithname +AAAA, @BBBB	|  A: number of slots in the lexical environment<br>B: literal id	|  Use the lexical variable name stored in the literal array corresponding to index B to create a lexical environment with A slots, store this lexical environment in acc, and enter.  |
|  0x03fe	|  PREF_NONE	|  throw.deletesuperproperty	|   |  	Exception thrown: Delete the attribute of the parent class.  |
|  0x04fb	|  PREF_IMM_16_ID16	|  callruntime.createprivateproperty +AAAA, @BBBB	|  A: number of symbols to be created<br>B: literal id	|  Create A symbols. Obtain the stored private method from the literal array corresponding to index B. If a private instance method exists, an additional symbol ("method") will be created. Based on the creation sequence, place the created symbols at the end of the lexical environment where the current class is located.<br>This instruction appears only when a class is defined.  |
|  0x04fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x04fd	|  PREF_IMM16_V8	|  wide.callrange +AAAA, vBB	|  Default input parameter: acc: function object<br>A: number of parameters<br>B, ..., B + A - 1: parameter	|  Use **B, ..., B + A - 1** as a parameter to call the function object stored in acc and store the result in acc.  |
|  0x04fe	|  PREF_V8	|  throw.constassignment vAA	|  A: constant variable name	|  Exception thrown: Assign a value to a constant variable.  |
|  0x05fb	|  PREF_IMM8_IMM_16_IMM_16_V8	|  callruntime.defineprivateproperty RR, +AAAA, +BBBB, vCC	|  Default input parameter: acc: value<br>A: lexical environment level<br>B: slot number<br>C: object	|  Load the value of slot B in the lexical environment beyond A levels, change value to acc, and add it to object C as a private attribute.  |
|  0x05fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x05fd	|  PREF_IMM16_V8	|  wide.callthisrange +AAAA, vBB	|  Default input parameter: acc: function object<br>A: number of parameters<br>B: object<br>B + 1, ..., B + A: parameter	|  Set **this** to **B**, call the function object stored in acc by setting **B + 1, ..., B + A** as the parameter, and store the result in acc.  |
|  0x05fe	|  PREF_V8	|  throw.ifnotobject vAA	|  A: object	|  If A is not an object, an exception is thrown.  |
|  0x06fb	|  PREF_IMM8_V8	|  callruntime.callinit +RR, vAA	|  acc: function object<br>R: 8-bit reserved number used in Ark runtime<br>A: object	|  Set **this** to **A**, call the function object stored in acc without passing parameters, and store the result in acc.  |
|  0x06fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x06fd	|  PREF_IMM16_V8	|  wide.supercallthisrange +AAAA, vBB	|  A: number of parameters<br>B, ..., B + A - 1: parameter	|  Use **B, ..., B + A - 1** as the parameter to call the **super** function and store the result in acc.<br>When the value of **A** is **0**, the value of **B** is **undefined**.<br>This instruction appears only in non-arrow functions.  |
|  0x06fe	|  PREF_V8_V8	|  throw.undefinedifhole vAA, vBB	|  A: object<br>B: object name	|  If the value of A is **hole**, the following exception is thrown: The value of B is **undefined**.  |
|  0x07fb	|  PREF_IMM16_ID16_ID16_IMM16_V8	|  callruntime.definesendableclass RRRR, @AAAA, @BBBB, +CCCC, vDD	|  R: 16-bit reserved number used in Ark runtime<br>A: **method id** of the constructor function from [sendable class](../arkts-utils/arkts-sendable.md#sendable-class)<br>B: literal id<br>C: number of formal parameters of method A<br>D: parent class	|  Use the literal array corresponding to index B and parent class D to create a class object of A and store it in acc.  |
|  0x07fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x07fd	|  PREF_IMM16_V8	|  wide.supercallarrowrange +AAAA, vBB	|  Default input parameter: acc: class object<br>A: number of parameters<br>B, ..., B + A - 1: parameter	|  Use **B, ..., B + A - 1** as a parameter to call the constructor function of the parent class of the class stored in acc and store the result in acc.<br>If the value of A is **0**, B is **undefined**.<br>This instruction appears only in arrow functions.  |
|  0x07fe	|  PREF_IMM8	|  throw.ifsupernotcorrectcall +AA	|  Default input parameter: acc: object<br>A: error type	|  If **super** is not called properly, an error is thrown.  |
|  0x08fb	|  PREF_IMM16	|  callruntime.ldsendableclass +AAAA	|  A: lexical environment level	|  Store the [sendable class](../arkts-utils/arkts-sendable.md#sendable-class) of the lexical environment beyond A levels in acc.  |
|  0x08fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x08fd	|  PREF_IMM32	|  wide.ldobjbyindex +AAAAAAAA	|  Default input parameter: acc: object<br>A: attribute key	|  Load the attribute whose key is A of the object stored in acc and store the attribute in acc.  |
|  0x08fe	|  PREF_IMM16	|  throw.ifsupernotcorrectcall +AAAA	|  Default input parameter: acc: object<br>A: error type	|  If **super** is not called properly, an error is thrown.  |
|  0x09fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x09fd	|  PREF_V8_IMM32	|  wide.stobjbyindex vAA, +BBBBBBBB	|  Default input parameter: acc: value<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x09fe	|  PREF_ID16	|  throw.undefinedifholewithname @AAAA	|  Default input parameter: acc: object<br>A: string id	|  If the value of acc is **hole**, the following exception is thrown: The value of A is **undefined**.  |
|  0x0afc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x0afd	|  PREF_V8_IMM32	|  wide.stownbyindex vAA, +BBBBBBBB	|  Default input parameter: acc: value<br>A: object<br>B: attribute key	|  Store the value in acc to the attribute whose key is B of object A.  |
|  0x0bfc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x0bfd	|  PREF_IMM16	|  wide.copyrestargs +AAAA	|  A: start position of the rest parameters in the formal parameter list	|  Copy the rest parameters and store the parameter array copy in acc.  |
|  0x0cfc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x0cfd	|  PREF_IMM16_IMM16	|  wide.ldlexvar +AAAA, +BBBB	|  A: lexical environment level<br>B: slot number	|  Store the value of slot B in the lexical environment beyond A levels in acc.  |
|  0x0dfc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x0dfd	|  PREF_IMM16_IMM16	|  wide.stlexvar +AAAA, +BBBB	|  Default input parameter: acc: value<br>A: lexical environment level<br>B: slot number	|  Store the value in acc to slot B in the lexical environment beyond A levels.  |
|  0x0efc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x0efd	|  PREF_IMM16	|  wide.getmodulenamespace +AAAA	|  A: module index	|  Execute the [GetModuleNamespace](https://262.ecma-international.org/12.0/#sec-getmodulenamespace) instruction for module A and store the result in acc.  |
|  0x0ffc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x0ffd	|  PREF_IMM16	|  wide.stmodulevar +AAAA	|  Default input parameter: acc: value<br>A: slot number	|  Store the value of acc to the module variable of slot A.  |
|  0x10fc	|  (deprecated)	 |  |  | Deprecated operation code. |
|  0x10fd	|  PREF_IMM16	|  wide.ldlocalmodulevar +AAAA	|  A: slot number	|  Store the local module variables of slot A in acc. |
| 0x11fc	| (deprecated)	| 	| 	| Deprecated operation code. |
| 0x11fd	| PREF_IMM16	| wide.ldexternalmodulevar +AAAA	| A: slot number	| Store the external module variable of slot A in acc. |
| 0x12fc	| (deprecated)	| 	| 	| Deprecated operation code. |
| 0x12fd	| PREF_IMM16	| wide.ldpatchvar +AAAA	| A: slot number of the patch variable	| Load the patch variable of slot A to acc.<br/>This instruction is used only build scenarios under the patch mode. |
| 0x13fc	| (deprecated)	| 	| 	| Deprecated operation code. |
| 0x13fd	| PREF_IMM16	| wide.stpatchvar +AAAA	| Default input parameter: acc: value<br/>A: slot number of the patch variable	| Store the value of acc to the patch variable of slot A.<br/>This instruction is used only build scenarios under the patch mode. |
| 0x14fc<br/>0x15fc<br/>...<br/>0x2efc	| (deprecated)	| 	| 	| Deprecated operation code. |