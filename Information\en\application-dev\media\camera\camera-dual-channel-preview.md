# Dual-Channel Preview (ArkTS)

Before developing a camera application, request the camera permission. For details, see [Camera Development Preparations](camera-preparation.md).

Dual-channel preview means that an application can use two preview streams at the same time. One preview stream is used for display on the screen, and the other is used for other operations such as image processing, so as to improve the processing efficiency.

The camera application controls the camera hardware to implement basic operations such as image display (preview), photo saving (photo capture), and video recording. The camera model is developed on the surface model. That is, an application transfers data through the surface. Specifically, it obtains the photo stream through the surface of an **ImageReceiver** object and the preview stream through the surface of an **XComponent** object.

To implement dual-channel preview (there are two preview streams instead of one preview stream plus one photo stream), you must create a **previewOutput** object through the surface of an **ImageReceiver** object. Other processes are the same as those of the photo stream and preview stream.

Read [Camera](../../reference/apis-camera-kit/js-apis-camera.md) for the API reference.

## Constraints

- Currently, streams cannot be dynamically added. In other words, you cannot call [addOutput](../../reference/apis-camera-kit/js-apis-camera.md#addoutput11) to add streams without calling [session.stop](../../reference/apis-camera-kit/js-apis-camera.md#stop11) first.
- After an **ImageReceiver** object processes image data obtained, it must release the image buffer so that the buffer queue of the surface properly rotates.

## API Calling Process

The figure below shows the recommended API calling process of the dual-channel preview solution.

![dual-preview-streams-instructions](figures/dual-preview-streams-instructions.png)

## How to Develop

1. Import the image module.

   Create a surface for the dual-channel preview stream. In addition to the surface of an **XComponent** object, the surface ID generated by an **ImageReceiver** object is needed. The APIs provided by the image module are also needed.

   ```ts
   import { image } from '@kit.ImageKit';
   ```
2. Create an **ImageReceiver** object.
   ```ts
   let size: image.Size = {
       width: 640,
       height: 480
     }
   let receiver: image.ImageReceiver = image.createImageReceiver(size, image.ImageFormat.JPEG, 8);
   ```
3. Obtain the surface ID of the **ImageReceiver** object.

   ```ts
   async function getImageReceiverSurfaceId(receiver: image.ImageReceiver): Promise<string | undefined> {
     let ImageReceiverSurfaceId: string | undefined = undefined;
     if (receiver !== undefined) {
       console.info('receiver is not undefined');
       let ImageReceiverSurfaceId: string = await receiver.getReceivingSurfaceId();
       console.info(`ImageReceived id: ${ImageReceiverSurfaceId}`);
     } else {
       console.error('createImageReceiver failed');
     }
     return ImageReceiverSurfaceId;
   }
   ```

4. Create a surface of an **XComponent** object.

   The **XComponent**, the capabilities of which are provided by the UI, offers the surface for preview streams. For details about how to obtain the surface ID, see [getXcomponentSurfaceId](../../reference/apis-arkui/arkui-ts/ts-basic-components-xcomponent.md#getxcomponentsurfaceid9). For details about the component, see [XComponent](../../reference/apis-arkui/arkui-ts/ts-basic-components-xcomponent.md).
   > **NOTE**
   >
   > The preview stream and video output stream must have the same aspect ratio of the resolution. For example, the aspect ratio of the surface of the **XComponent** is 1920:1080 (which is equal to 16:9), then the aspect ratio of the resolution of the preview stream must also be 16:9. This means that the resolution can be 640:360, 960:540, 1920:1080, or the like.

5. Implement dual-channel preview.

   Call [createPreviewOutput](../../reference/apis-camera-kit/js-apis-camera.md#createpreviewoutput) to transfer the two surface IDs generated in steps 2 and 3 to the camera service to create two preview streams. Develop other processes based on the normal preview process.

   ```ts
   import { camera } from '@kit.CameraKit';

   async function createDualChannelPreview(cameraManager: camera.CameraManager, XComponentSurfaceId: string, receiver: image.ImageReceiver): Promise<void> {
     // Obtain the supported camera devices.
     let camerasDevices: Array<camera.CameraDevice> = cameraManager.getSupportedCameras();

     // Obtain the supported modes.
     let sceneModes: Array<camera.SceneMode> = cameraManager.getSupportedSceneModes(camerasDevices[0]);
     let isSupportPhotoMode: boolean = sceneModes.indexOf(camera.SceneMode.NORMAL_PHOTO) >= 0;
     if (!isSupportPhotoMode) {
       console.error('photo mode not support');
       return;
     }

     // Obtain the profile object.
     let profiles: camera.CameraOutputCapability = cameraManager.getSupportedOutputCapability(camerasDevices[0], camera.SceneMode.NORMAL_PHOTO); // Obtain the profiles of the camera.
     let previewProfiles: Array<camera.Profile> = profiles.previewProfiles;

     // Preview stream 1.
     let previewProfilesObj: camera.Profile = previewProfiles[0];

     // Preview stream 2.
     let previewProfilesObj2: camera.Profile = previewProfiles[0];

     // Create an output object for preview stream 1.
     let previewOutput: camera.PreviewOutput = cameraManager.createPreviewOutput(previewProfilesObj, XComponentSurfaceId);

     // Create an output object for preview stream 2.
     let imageReceiverSurfaceId: string = await receiver.getReceivingSurfaceId();
     let previewOutput2: camera.PreviewOutput = cameraManager.createPreviewOutput(previewProfilesObj2, imageReceiverSurfaceId);

     // Create a CameraInput object.
     let cameraInput: camera.CameraInput = cameraManager.createCameraInput(camerasDevices[0]);

     // Open the camera.
     await cameraInput.open();

     // Create a session.
     let photoSession: camera.PhotoSession = cameraManager.createSession(camera.SceneMode.NORMAL_PHOTO) as camera.PhotoSession;

     // Start configuration for the session.
     photoSession.beginConfig();

     // Add the CameraInput object to the session.
     photoSession.addInput(cameraInput);

     // Add preview stream 1 to the session.
     photoSession.addOutput(previewOutput);

     // Add preview stream 2 to the session.
     photoSession.addOutput(previewOutput2);

     // Commit the configuration.
     await photoSession.commitConfig();

     // Start the session.
     await photoSession.start();

     // Stop the session.
     await photoSession.stop();

     // Release the camera input stream.
     await cameraInput.close();

     // Release the preview output stream.
     await previewOutput.release();

     // Release the photo output stream.
     await previewOutput2.release();

     // Release the session.
     await photoSession.release();
   }
   ```

6. Obtain preview images in real time through the **ImageReceiver** object.

   Use the **imageArrival** event of the **ImageReceiver** object to listen for and obtain image data returned by the bottom layer. For details, see [Image](../../reference/apis-image-kit/js-apis-image.md).

   ```ts
   import { BusinessError } from '@kit.BasicServicesKit';

   function onImageArrival(receiver: image.ImageReceiver): void {
     receiver.on('imageArrival', () => {
       receiver.readNextImage((err: BusinessError, nextImage: image.Image) => {
         if (err || nextImage === undefined) {
           console.error('readNextImage failed');
           return;
         }
         nextImage.getComponent(image.ComponentType.JPEG, async (err: BusinessError, imgComponent: image.Component) => {
           if (err || imgComponent === undefined) {
             console.error('getComponent failed');
           }
           if (imgComponent.byteBuffer) {
             // Parse the buffer data by referring to step 7. This example uses method 1 as an example.
             let width = 640; // width is the value of width in image.Size specified when the ImageReceiver is created.
             let height = 480; // height is the value of height in image.Size specified when the ImageReceiver is created.
             let stride = imgComponent.rowStride;
             console.debug(`getComponent with width:${width} height:${height} stride:${stride}`);
             // The value of stride is the same as that of width.
             if (stride == width) {
               let pixelMap = await image.createPixelMap(imgComponent.byteBuffer, {
                 size: { height: height, width: width },
                 srcPixelFormat: 8,
               })
             } else {
               // The value of stride is different from that of width.
               const dstBufferSize = width * height * 1.5
               const dstArr = new Uint8Array(dstBufferSize)
               for (let j = 0; j < height * 1.5; j++) {
                 const srcBuf = new Uint8Array(imgComponent.byteBuffer, j * stride, width)
                 dstArr.set(srcBuf, j * width)
               }
               let pixelMap = await image.createPixelMap(dstArr.buffer, {
                 size: { height: height, width: width },
                 srcPixelFormat: 8,
               })
             }
           } else {
             console.error('byteBuffer is null');
           }
           // Release the resource when the buffer is not in use.
           // If an asynchronous operation is performed on the buffer, call nextImage.release() to release the resource after the asynchronous operation is complete.
           nextImage.release();
         })
       })
     })
   }
   ```

7. Use [image.Component]() to parse the image buffer data.

   > **NOTE**
   >
   > Check whether the width of the image is the same as **rowStride**. If they are different, perform the following operations:

   Method 1: Remove the stride data from **component.byteBuffer**, obtain a new buffer by means of copy, and process the buffer by calling the API that does not support stride.

   ```ts
   // Currently, the camera preview stream supports only NV21 (YUV_420_SP images).
   const dstBufferSize = width * height * 1.5;
   const dstArr = new Uint8Array(dstBufferSize);
   // Read the buffer data line by line.
   for (let j = 0; j < height * 1.5; j++) {
     // Copy the first width bytes of each line of data in component.byteBuffer to dstArr.
     const srcBuf = new Uint8Array(component.byteBuffer, j * stride, width);
     dstArr.set(srcBuf, j * width);
   }
   let pixelMap = await image.createPixelMap(dstArr.buffer, {
     size: { height: height, width: width }, srcPixelFormat: 8
   });
   ```

   Method 2: Create a PixelMap based on the value of stride * height, and call **cropSync** of the PixelMap to crop redundant pixels.

   ```ts
   // Create a PixelMap, with width set to the value of stride.
   let pixelMap = await image.createPixelMap(component.byteBuffer, {
     size:{height: height, width: stride}, srcPixelFormat: 8});
   // Crop extra pixels.
   pixelMap.cropSync({size:{width:width, height:height}, x:0, y:0});
   ```

   Method 3: Pass **component.byteBuffer** and **stride** to the API that supports stride.

 <!--no_check--> 