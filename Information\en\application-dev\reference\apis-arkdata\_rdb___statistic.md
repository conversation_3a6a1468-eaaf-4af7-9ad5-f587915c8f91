# Rdb_Statistic


## Overview

Defines a struct for the device-cloud sync statistics of a database table.

**Since**: 11

**Related module**: [RDB](_r_d_b.md)


## Summary


### Member Variables

| Name| Description|
| -------- | -------- |
| [total](_r_d_b.md#total) | Total number of rows to be synchronized between the device and cloud in the database table.|
| [successful](_r_d_b.md#successful) | Number of rows that are successfully synchronized between the device and cloud in the database table.|
| [failed](_r_d_b.md#failed) | Number of rows that failed to be synchronized between the device and cloud in the database table.|
| [remained](_r_d_b.md#remained) | Number of rows that are not executed for device-cloud sync in the database table.|
