"""
Qdrant compatibility layer for handling API changes between versions.

This module provides a compatibility layer for the Qdrant client to handle
API changes between versions, particularly the transition from the deprecated
`search` method to the newer `query_points` method.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union
from qdrant_client import QdrantClient, AsyncQdrantClient
from qdrant_client.http import models

# Configure logging
logger = logging.getLogger("QdrantCompatibility")

# Check if we should use the new API (default to True for new installations)
USE_NEW_API = os.environ.get("USE_QDRANT_NEW_API", "1").lower() in ("1", "true", "yes")

# Function to detect Qdrant version and determine if we should use the new API
def detect_qdrant_version(client: QdrantClient) -> bool:
    """
    Detect Qdrant version and determine if we should use the new API.

    Args:
        client: Qdrant client

    Returns:
        True if we should use the new API, False otherwise
    """
    # For now, always use the deprecated API since it's more reliable
    # We'll handle the parameter differences in the fallback code
    return False


class QdrantCompatibility:
    """Compatibility layer for Qdrant client API changes."""

    @staticmethod
    def search(
        client: QdrantClient,
        collection_name: str,
        query_vector: List[float],
        query_filter: Optional[models.Filter] = None,
        limit: int = 10,
        offset: int = 0,
        with_payload: bool = True,
        with_vectors: bool = False,
        score_threshold: Optional[float] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Perform a search using either the deprecated `search` method or the newer `query_points` method.

        Args:
            client: Qdrant client
            collection_name: Name of the collection to search
            query_vector: Query vector
            query_filter: Filter to apply to the search
            limit: Maximum number of results to return
            offset: Offset for pagination
            with_payload: Whether to include payload in results
            with_vectors: Whether to include vectors in results
            score_threshold: Minimum score threshold
            **kwargs: Additional arguments to pass to the search method

        Returns:
            List of search results
        """
        try:
            # Detect if we should use the new API based on Qdrant version
            use_new_api = detect_qdrant_version(client)

            if use_new_api:
                logger.debug("Using new Qdrant API (query_points)")
                # For newer Qdrant versions, we need to use 'vector' parameter instead of 'query_vector'
                try:
                    response = client.query_points(
                        collection_name=collection_name,
                        query_vector=query_vector,  # Try with query_vector first
                        query_filter=query_filter,
                        limit=limit,
                        offset=offset,
                        with_payload=with_payload,
                        with_vectors=with_vectors,
                        score_threshold=score_threshold,
                        **kwargs
                    )
                except TypeError as e:
                    if "query_vector" in str(e):
                        # If query_vector is not recognized, try with vector parameter
                        logger.debug("query_vector not recognized, trying with vector parameter")
                        response = client.query_points(
                            collection_name=collection_name,
                            vector=query_vector,  # Use vector instead of query_vector
                            filter=query_filter,  # May also need to use filter instead of query_filter
                            limit=limit,
                            offset=offset,
                            with_payload=with_payload,
                            with_vectors=with_vectors,
                            score_threshold=score_threshold,
                            **kwargs
                        )
                    else:
                        raise
                return response.points
            else:
                logger.debug("Using deprecated Qdrant API (search) for compatibility reasons.")
                return client.search(
                    collection_name=collection_name,
                    query_vector=query_vector,
                    query_filter=query_filter,
                    limit=limit,
                    offset=offset,
                    with_payload=with_payload,
                    with_vectors=with_vectors,
                    score_threshold=score_threshold,
                    **kwargs
                )
        except Exception as e:
            logger.error(f"Error in Qdrant search: {str(e)}")
            # Try fallback to the other API method if the first one fails
            try:
                if use_new_api:
                    logger.debug("New API failed, falling back to deprecated API (search)")
                    return client.search(
                        collection_name=collection_name,
                        query_vector=query_vector,
                        query_filter=query_filter,
                        limit=limit,
                        offset=offset,
                        with_payload=with_payload,
                        with_vectors=with_vectors,
                        score_threshold=score_threshold,
                        **kwargs
                    )
                else:
                    logger.debug("Deprecated API failed, trying new API (query_points)")
                    # For newer Qdrant versions, we need to use 'vector' parameter instead of 'query_vector'
                    try:
                        response = client.query_points(
                            collection_name=collection_name,
                            query_vector=query_vector,  # Try with query_vector first
                            query_filter=query_filter,
                            limit=limit,
                            offset=offset,
                            with_payload=with_payload,
                            with_vectors=with_vectors,
                            score_threshold=score_threshold,
                            **kwargs
                        )
                    except TypeError as e:
                        if "query_vector" in str(e):
                            # If query_vector is not recognized, try with vector parameter
                            logger.debug("query_vector not recognized, trying with vector parameter")
                            response = client.query_points(
                                collection_name=collection_name,
                                vector=query_vector,  # Use vector instead of query_vector
                                filter=query_filter,  # May also need to use filter instead of query_filter
                                limit=limit,
                                offset=offset,
                                with_payload=with_payload,
                                with_vectors=with_vectors,
                                score_threshold=score_threshold,
                                **kwargs
                            )
                        else:
                            raise
                    return response.points
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {str(fallback_error)}")
                raise

    @staticmethod
    async def search_async(
        client: AsyncQdrantClient,
        collection_name: str,
        query_vector: List[float],
        query_filter: Optional[models.Filter] = None,
        limit: int = 10,
        offset: int = 0,
        with_payload: bool = True,
        with_vectors: bool = False,
        score_threshold: Optional[float] = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        Perform an async search using either the deprecated `search` method or the newer `query_points` method.

        Args:
            client: Async Qdrant client
            collection_name: Name of the collection to search
            query_vector: Query vector
            query_filter: Filter to apply to the search
            limit: Maximum number of results to return
            offset: Offset for pagination
            with_payload: Whether to include payload in results
            with_vectors: Whether to include vectors in results
            score_threshold: Minimum score threshold
            **kwargs: Additional arguments to pass to the search method

        Returns:
            List of search results
        """
        # For async clients, always use the deprecated API for consistency
        use_new_api = False

        try:
            if use_new_api:
                logger.debug("Using new Qdrant API (query_points) async")
                # For newer Qdrant versions, we need to use 'vector' parameter instead of 'query_vector'
                try:
                    response = await client.query_points(
                        collection_name=collection_name,
                        query_vector=query_vector,  # Try with query_vector first
                        query_filter=query_filter,
                        limit=limit,
                        offset=offset,
                        with_payload=with_payload,
                        with_vectors=with_vectors,
                        score_threshold=score_threshold,
                        **kwargs
                    )
                except TypeError as e:
                    if "query_vector" in str(e):
                        # If query_vector is not recognized, try with vector parameter
                        logger.debug("query_vector not recognized, trying with vector parameter in async mode")
                        response = await client.query_points(
                            collection_name=collection_name,
                            vector=query_vector,  # Use vector instead of query_vector
                            filter=query_filter,  # May also need to use filter instead of query_filter
                            limit=limit,
                            offset=offset,
                            with_payload=with_payload,
                            with_vectors=with_vectors,
                            score_threshold=score_threshold,
                            **kwargs
                        )
                    else:
                        raise
                return response.points
            else:
                logger.debug("Using deprecated Qdrant API (search) async for compatibility reasons.")
                return await client.search(
                    collection_name=collection_name,
                    query_vector=query_vector,
                    query_filter=query_filter,
                    limit=limit,
                    offset=offset,
                    with_payload=with_payload,
                    with_vectors=with_vectors,
                    score_threshold=score_threshold,
                    **kwargs
                )
        except Exception as e:
            logger.error(f"Error in async Qdrant search: {str(e)}")
            # Try fallback to the other API method if the first one fails
            try:
                if use_new_api:
                    logger.debug("New API failed, falling back to deprecated API (search) async")
                    return await client.search(
                        collection_name=collection_name,
                        query_vector=query_vector,
                        query_filter=query_filter,
                        limit=limit,
                        offset=offset,
                        with_payload=with_payload,
                        with_vectors=with_vectors,
                        score_threshold=score_threshold,
                        **kwargs
                    )
                else:
                    logger.debug("Deprecated API failed, trying new API (query_points) async")
                    # For newer Qdrant versions, we need to use 'vector' parameter instead of 'query_vector'
                    try:
                        response = await client.query_points(
                            collection_name=collection_name,
                            query_vector=query_vector,  # Try with query_vector first
                            query_filter=query_filter,
                            limit=limit,
                            offset=offset,
                            with_payload=with_payload,
                            with_vectors=with_vectors,
                            score_threshold=score_threshold,
                            **kwargs
                        )
                    except TypeError as e:
                        if "query_vector" in str(e):
                            # If query_vector is not recognized, try with vector parameter
                            logger.debug("query_vector not recognized, trying with vector parameter in async fallback")
                            response = await client.query_points(
                                collection_name=collection_name,
                                vector=query_vector,  # Use vector instead of query_vector
                                filter=query_filter,  # May also need to use filter instead of query_filter
                                limit=limit,
                                offset=offset,
                                with_payload=with_payload,
                                with_vectors=with_vectors,
                                score_threshold=score_threshold,
                                **kwargs
                            )
                        else:
                            raise
                    return response.points
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {str(fallback_error)}")
                raise


# Create a global instance for convenience
qdrant_compatibility = QdrantCompatibility()
