2025-05-25 16:12:32,373 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-25 16:12:32,373 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-25 16:12:32,373 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-25 16:12:32,373 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:8b
2025-05-25 16:12:32,373 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/openharmony/ets/api
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/openharmony/ets/component
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/openharmony/ets/build-tools
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - WARNING - Directory not found: Information/default/hms/ets/api
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - WARNING - Directory not found: comprehensive_dataset
2025-05-25 16:12:32,381 - ComprehensiveRealSystemTest - INFO - Found 0 large ArkTS files for testing
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Log file: test_results/Test_ComprehensiveRealSystem_Results\comprehensive_real_system_test.log
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - ComprehensiveRealSystemTest initialized
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Using model: qwen3:8b
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Test results directory: test_results/Test_ComprehensiveRealSystem_Results
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Starting Comprehensive Real System Test
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Step 1: Finding large ArkTS files...
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Searching for large ArkTS files (min size: 50,000 bytes)
2025-05-25 16:14:56,938 - ComprehensiveRealSystemTest - INFO - Searching in: ../Information/default/openharmony/ets/api
2025-05-25 16:14:56,949 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.account.appAccount.d.ts (136,704 bytes)
2025-05-25 16:14:56,949 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.account.osAccount.d.ts (59,446 bytes)
2025-05-25 16:14:56,959 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.arkui.UIContext.d.ts (123,406 bytes)
2025-05-25 16:14:56,960 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-25 16:14:56,960 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-25 16:14:56,960 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-25 16:14:56,962 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.buffer.d.ts (190,085 bytes)
2025-05-25 16:14:56,962 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-25 16:14:56,963 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.commonEvent.d.ts (65,130 bytes)
2025-05-25 16:14:56,963 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.commonEventManager.d.ts (68,365 bytes)
2025-05-25 16:14:56,963 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.contact.d.ts (143,754 bytes)
2025-05-25 16:14:56,964 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-25 16:14:56,964 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-25 16:14:56,964 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-25 16:14:56,965 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-25 16:14:56,965 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.display.d.ts (50,958 bytes)
2025-05-25 16:14:56,965 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-25 16:14:56,965 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-25 16:14:56,965 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.fileio.d.ts (56,377 bytes)
2025-05-25 16:14:56,965 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.geoLocationManager.d.ts (95,526 bytes)
2025-05-25 16:14:56,970 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.graphics.drawing.d.ts (149,907 bytes)
2025-05-25 16:14:56,970 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.graphics.text.d.ts (50,405 bytes)
2025-05-25 16:14:56,972 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.i18n.d.ts (111,611 bytes)
2025-05-25 16:14:56,972 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.inputMethod.d.ts (75,816 bytes)
2025-05-25 16:14:56,972 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.inputMethodEngine.d.ts (82,240 bytes)
2025-05-25 16:14:56,973 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.intl.d.ts (116,417 bytes)
2025-05-25 16:14:56,974 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-25 16:14:56,975 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-25 16:14:56,976 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-25 16:14:56,976 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-25 16:14:56,977 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-25 16:14:56,977 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-25 16:14:56,978 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.multimodalInput.keyCode.d.ts (51,152 bytes)
2025-05-25 16:14:56,979 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-25 16:14:56,979 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.net.http.d.ts (123,216 bytes)
2025-05-25 16:14:56,981 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-25 16:14:56,983 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.nfc.tag.d.ts (60,146 bytes)
2025-05-25 16:14:56,983 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.notificationManager.d.ts (67,491 bytes)
2025-05-25 16:14:56,983 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.pasteboard.d.ts (57,975 bytes)
2025-05-25 16:14:56,985 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.request.d.ts (192,677 bytes)
2025-05-25 16:14:56,985 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.resourceManager.d.ts (245,843 bytes)
2025-05-25 16:14:56,985 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.router.d.ts (50,688 bytes)
2025-05-25 16:14:56,985 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.rpc.d.ts (170,228 bytes)
2025-05-25 16:14:56,987 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.security.cert.d.ts (214,222 bytes)
2025-05-25 16:14:56,987 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts (336,615 bytes)
2025-05-25 16:14:56,988 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.security.huks.d.ts (186,266 bytes)
2025-05-25 16:14:56,989 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.sensor.d.ts (160,773 bytes)
2025-05-25 16:14:56,991 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.UiTest.d.ts (158,743 bytes)
2025-05-25 16:14:56,992 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.userIAM.userAuth.d.ts (55,963 bytes)
2025-05-25 16:14:56,992 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.util.d.ts (198,770 bytes)
2025-05-25 16:14:56,994 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.web.netErrorList.d.ts (61,804 bytes)
2025-05-25 16:14:56,995 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.web.webview.d.ts (267,704 bytes)
2025-05-25 16:14:56,995 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-25 16:14:56,995 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.window.d.ts (286,762 bytes)
2025-05-25 16:14:56,997 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.worker.d.ts (86,613 bytes)
2025-05-25 16:14:56,997 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\@ohos.zlib.d.ts (98,263 bytes)
2025-05-25 16:14:57,004 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\application\AbilityDelegator.d.ts (66,895 bytes)
2025-05-25 16:14:57,010 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\application\UIAbilityContext.d.ts (109,096 bytes)
2025-05-25 16:14:57,031 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/api\tag\nfctech.d.ts (69,043 bytes)
2025-05-25 16:14:57,031 - ComprehensiveRealSystemTest - INFO - Searching in: ../Information/default/openharmony/ets/component
2025-05-25 16:14:57,039 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\canvas.d.ts (209,139 bytes)
2025-05-25 16:14:57,040 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\common.d.ts (566,343 bytes)
2025-05-25 16:14:57,041 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-25 16:14:57,042 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\enums.d.ts (205,914 bytes)
2025-05-25 16:14:57,044 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\gesture.d.ts (97,133 bytes)
2025-05-25 16:14:57,047 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\navigation.d.ts (83,995 bytes)
2025-05-25 16:14:57,049 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\rich_editor.d.ts (94,325 bytes)
2025-05-25 16:14:57,053 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\swiper.d.ts (55,612 bytes)
2025-05-25 16:14:57,054 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\text_input.d.ts (62,924 bytes)
2025-05-25 16:14:57,056 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\units.d.ts (68,870 bytes)
2025-05-25 16:14:57,056 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/component\web.d.ts (231,955 bytes)
2025-05-25 16:14:57,057 - ComprehensiveRealSystemTest - INFO - Searching in: ../Information/default/openharmony/ets/build-tools
2025-05-25 16:14:57,058 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\aot\src\lib_ark_builtins.d.ts (54,703 bytes)
2025-05-25 16:14:57,062 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts (842,681 bytes)
2025-05-25 16:14:57,064 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.es5.d.ts (212,312 bytes)
2025-05-25 16:14:57,068 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.webworker.d.ts (259,617 bytes)
2025-05-25 16:14:57,068 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\protocol.d.ts (98,216 bytes)
2025-05-25 16:14:57,069 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts (587,234 bytes)
2025-05-25 16:14:57,069 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts (447,512 bytes)
2025-05-25 16:14:57,069 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts (447,499 bytes)
2025-05-25 16:14:57,096 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\canvas.d.ts (209,151 bytes)
2025-05-25 16:14:57,097 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts (566,649 bytes)
2025-05-25 16:14:57,097 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\common_ts_ets_api.d.ts (71,067 bytes)
2025-05-25 16:14:57,099 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\enums.d.ts (205,914 bytes)
2025-05-25 16:14:57,100 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\gesture.d.ts (97,133 bytes)
2025-05-25 16:14:57,104 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\navigation.d.ts (84,079 bytes)
2025-05-25 16:14:57,108 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\rich_editor.d.ts (94,325 bytes)
2025-05-25 16:14:57,109 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\swiper.d.ts (55,612 bytes)
2025-05-25 16:14:57,113 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\text_input.d.ts (62,924 bytes)
2025-05-25 16:14:57,114 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\units.d.ts (68,936 bytes)
2025-05-25 16:14:57,115 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\web.d.ts (231,967 bytes)
2025-05-25 16:14:57,173 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index-legacy.d.ts (168,429 bytes)
2025-05-25 16:14:57,173 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts (611,184 bytes)
2025-05-25 16:14:57,227 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\index.d.ts (52,155 bytes)
2025-05-25 16:14:57,228 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\eslint\rules\stylistic-issues.d.ts (52,150 bytes)
2025-05-25 16:14:57,229 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\buffer.d.ts (107,224 bytes)
2025-05-25 16:14:57,229 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\child_process.d.ts (67,998 bytes)
2025-05-25 16:14:57,229 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\crypto.d.ts (183,978 bytes)
2025-05-25 16:14:57,232 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs.d.ts (182,973 bytes)
2025-05-25 16:14:57,232 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http.d.ts (79,056 bytes)
2025-05-25 16:14:57,233 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\http2.d.ts (113,815 bytes)
2025-05-25 16:14:57,233 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\inspector.d.ts (125,369 bytes)
2025-05-25 16:14:57,234 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\process.d.ts (73,684 bytes)
2025-05-25 16:14:57,234 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\stream.d.ts (70,767 bytes)
2025-05-25 16:14:57,234 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\tls.d.ts (54,923 bytes)
2025-05-25 16:14:57,235 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\util.d.ts (82,275 bytes)
2025-05-25 16:14:57,236 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\fs\promises.d.ts (53,450 bytes)
2025-05-25 16:14:57,239 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\buffer.d.ts (107,224 bytes)
2025-05-25 16:14:57,239 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\child_process.d.ts (67,998 bytes)
2025-05-25 16:14:57,240 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\crypto.d.ts (183,913 bytes)
2025-05-25 16:14:57,241 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs.d.ts (182,973 bytes)
2025-05-25 16:14:57,241 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http.d.ts (79,056 bytes)
2025-05-25 16:14:57,241 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\http2.d.ts (113,815 bytes)
2025-05-25 16:14:57,242 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\inspector.d.ts (125,369 bytes)
2025-05-25 16:14:57,243 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\process.d.ts (73,684 bytes)
2025-05-25 16:14:57,243 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\stream.d.ts (72,452 bytes)
2025-05-25 16:14:57,243 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\tls.d.ts (54,923 bytes)
2025-05-25 16:14:57,243 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\util.d.ts (82,262 bytes)
2025-05-25 16:14:57,245 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@types\node\ts4.8\fs\promises.d.ts (53,380 bytes)
2025-05-25 16:14:57,280 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\dist\generated\ast-spec.d.ts (68,378 bytes)
2025-05-25 16:14:57,281 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@typescript-eslint\types\_ts3.4\dist\generated\ast-spec.d.ts (70,137 bytes)
2025-05-25 16:14:57,577 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts (816,786 bytes)
2025-05-25 16:14:57,577 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.es5.d.ts (213,196 bytes)
2025-05-25 16:14:57,587 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts (272,256 bytes)
2025-05-25 16:14:57,588 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\protocol.d.ts (107,934 bytes)
2025-05-25 16:14:57,588 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts (747,710 bytes)
2025-05-25 16:14:57,589 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts (598,789 bytes)
2025-05-25 16:14:57,589 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts (598,776 bytes)
2025-05-25 16:14:57,592 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts (335,559 bytes)
2025-05-25 16:14:57,656 - ComprehensiveRealSystemTest - INFO - Searching in: ../Information/default/hms/ets/api
2025-05-25 16:14:57,656 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-25 16:14:57,656 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-25 16:14:57,669 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-25 16:14:57,669 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-25 16:14:57,671 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.iap.d.ts (88,545 bytes)
2025-05-25 16:14:57,671 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-25 16:14:57,672 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-25 16:14:57,672 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-25 16:14:57,674 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-25 16:14:57,675 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.core.weather.d.ts (79,418 bytes)
2025-05-25 16:14:57,676 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.health.store.d.ts (346,063 bytes)
2025-05-25 16:14:57,676 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-25 16:14:57,678 - ComprehensiveRealSystemTest - INFO - Found large file: ../Information/default/hms/ets/api\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-25 16:14:57,679 - ComprehensiveRealSystemTest - INFO - Searching in: ../comprehensive_dataset
2025-05-25 16:14:57,683 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\basic_ui\text_input.d.ts (62,924 bytes)
2025-05-25 16:14:57,685 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\container\swiper.d.ts (55,612 bytes)
2025-05-25 16:14:57,687 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\device\@ohos.bluetooth.ble.d.ts (167,753 bytes)
2025-05-25 16:14:57,687 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\device\@ohos.bluetooth.d.ts (116,249 bytes)
2025-05-25 16:14:57,689 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\device\@ohos.bluetoothManager.d.ts (255,785 bytes)
2025-05-25 16:14:57,690 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\device\@ohos.sensor.d.ts (160,773 bytes)
2025-05-25 16:14:57,690 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\device\@ohos.wifiManager.d.ts (103,735 bytes)
2025-05-25 16:14:57,693 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\form\text_input.d.ts (62,924 bytes)
2025-05-25 16:14:57,700 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.collaboration.rcp.d.ts (137,929 bytes)
2025-05-25 16:14:57,700 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.account.LoginComponent.d.ets (58,250 bytes)
2025-05-25 16:14:57,704 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.atomicserviceComponent.atomicserviceUi.d.ets (52,440 bytes)
2025-05-25 16:14:57,704 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.authentication.d.ts (54,789 bytes)
2025-05-25 16:14:57,705 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.iap.d.ts (88,545 bytes)
2025-05-25 16:14:57,706 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.map.map.d.ts (158,612 bytes)
2025-05-25 16:14:57,706 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.map.mapCommon.d.ts (74,305 bytes)
2025-05-25 16:14:57,706 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.map.navi.d.ts (65,365 bytes)
2025-05-25 16:14:57,707 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.map.site.d.ts (55,511 bytes)
2025-05-25 16:14:57,710 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.core.weather.d.ts (79,418 bytes)
2025-05-25 16:14:57,710 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.health.store.d.ts (346,063 bytes)
2025-05-25 16:14:57,711 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.health.wearEngine.d.ts (77,380 bytes)
2025-05-25 16:14:57,711 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\hms\@hms.officeservice.pdfservice.d.ts (96,589 bytes)
2025-05-25 16:14:57,716 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts (325,027 bytes)
2025-05-25 16:14:57,716 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts (276,572 bytes)
2025-05-25 16:14:57,717 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\multimedia\@ohos.multimedia.camera.d.ts (142,490 bytes)
2025-05-25 16:14:57,717 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\multimedia\@ohos.multimedia.drm.d.ts (62,549 bytes)
2025-05-25 16:14:57,717 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts (296,616 bytes)
2025-05-25 16:14:57,718 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts (270,515 bytes)
2025-05-25 16:14:57,718 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\navigation\navigation.d.ts (83,995 bytes)
2025-05-25 16:14:57,719 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\network\@ohos.net.connection.d.ts (76,049 bytes)
2025-05-25 16:14:57,719 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\network\@ohos.net.http.d.ts (123,216 bytes)
2025-05-25 16:14:57,721 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\network\@ohos.net.socket.d.ts (199,887 bytes)
2025-05-25 16:14:57,721 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\network\@ohos.request.d.ts (192,677 bytes)
2025-05-25 16:14:57,723 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.data.distributedData.d.ts (114,680 bytes)
2025-05-25 16:14:57,724 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.data.distributedKVStore.d.ts (143,208 bytes)
2025-05-25 16:14:57,724 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.data.preferences.d.ts (106,030 bytes)
2025-05-25 16:14:57,724 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts (363,266 bytes)
2025-05-25 16:14:57,726 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.file.fs.d.ts (372,692 bytes)
2025-05-25 16:14:57,727 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.file.photoAccessHelper.d.ts (143,404 bytes)
2025-05-25 16:14:57,728 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\storage\@ohos.fileio.d.ts (56,377 bytes)
2025-05-25 16:14:57,734 - ComprehensiveRealSystemTest - INFO - Found large file: ../comprehensive_dataset\system\@ohos.bundle.bundleManager.d.ts (60,656 bytes)
2025-05-25 16:14:57,734 - ComprehensiveRealSystemTest - INFO - Found 30 large ArkTS files for testing
2025-05-25 16:14:57,734 - ComprehensiveRealSystemTest - INFO - Found 30 large files for testing
2025-05-25 16:14:57,734 - ComprehensiveRealSystemTest - INFO - Step 2: Testing parsing capabilities...
2025-05-25 16:14:57,738 - ComprehensiveRealSystemTest - INFO - === Testing Parsing Capabilities ===
2025-05-25 16:14:57,741 - ComprehensiveRealSystemTest - INFO - Parsing file 1/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-25 16:14:57,780 - ComprehensiveRealSystemTest - INFO - Parsed 681 symbols in 0.039s
2025-05-25 16:14:57,781 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'type': 1, 'namespace': 2, 'interface': 14, 'decorator': 486, 'callback_type': 1, 'union_type': 176, 'intersection_type': 1}
2025-05-25 16:14:57,781 - ComprehensiveRealSystemTest - INFO - Parsing file 2/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-25 16:14:57,810 - ComprehensiveRealSystemTest - INFO - Parsed 777 symbols in 0.029s
2025-05-25 16:14:57,810 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'interface': 14, 'decorator': 568, 'callback_type': 2, 'union_type': 190, 'intersection_type': 1}
2025-05-25 16:14:57,810 - ComprehensiveRealSystemTest - INFO - Parsing file 3/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-25 16:14:57,959 - ComprehensiveRealSystemTest - INFO - Parsed 2489 symbols in 0.149s
2025-05-25 16:14:57,959 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 45, 'interface': 1244, 'type': 185, 'function': 50, 'namespace': 110, 'enum': 41, 'const': 9, 'reexport_all': 1, 'decorator': 632, 'callback_type': 12, 'union_type': 145, 'intersection_type': 12, 'export_assignment': 3}
2025-05-25 16:14:57,959 - ComprehensiveRealSystemTest - INFO - Parsing file 4/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-25 16:14:57,980 - ComprehensiveRealSystemTest - INFO - Parsed 75 symbols in 0.021s
2025-05-25 16:14:57,980 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 2, 'decorator': 16, 'union_type': 57}
2025-05-25 16:14:57,980 - ComprehensiveRealSystemTest - INFO - Parsing file 5/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-25 16:14:58,107 - ComprehensiveRealSystemTest - INFO - Parsed 2174 symbols in 0.118s
2025-05-25 16:14:58,107 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'reexport_all': 1, 'decorator': 594, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 3}
2025-05-25 16:14:58,107 - ComprehensiveRealSystemTest - INFO - Parsing file 6/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-25 16:14:58,241 - ComprehensiveRealSystemTest - INFO - Parsed 2173 symbols in 0.134s
2025-05-25 16:14:58,241 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 35, 'interface': 1022, 'type': 183, 'function': 44, 'namespace': 100, 'enum': 41, 'reexport_all': 1, 'decorator': 594, 'callback_type': 10, 'union_type': 134, 'intersection_type': 7, 'export_assignment': 2}
2025-05-25 16:14:58,241 - ComprehensiveRealSystemTest - INFO - Parsing file 7/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-25 16:14:58,350 - ComprehensiveRealSystemTest - INFO - Parsed 2105 symbols in 0.109s
2025-05-25 16:14:58,350 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 27, 'interface': 1106, 'type': 169, 'function': 36, 'namespace': 41, 'enum': 39, 'const': 9, 'reexport_all': 1, 'decorator': 519, 'callback_type': 12, 'union_type': 133, 'intersection_type': 11, 'export_assignment': 2}
2025-05-25 16:14:58,350 - ComprehensiveRealSystemTest - INFO - Parsing file 8/30: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-25 16:14:58,426 - ComprehensiveRealSystemTest - INFO - Parsed 11561 symbols in 0.059s
2025-05-25 16:14:58,427 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'decorator': 11358, 'callback_type': 12, 'union_type': 2}
2025-05-25 16:14:58,428 - ComprehensiveRealSystemTest - INFO - Parsing file 9/30: ../Information/default/openharmony/ets/component\common.d.ts
2025-05-25 16:14:58,493 - ComprehensiveRealSystemTest - INFO - Parsed 11561 symbols in 0.065s
2025-05-25 16:14:58,493 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 14, 'interface': 103, 'type': 38, 'namespace': 2, 'enum': 32, 'decorator': 11358, 'callback_type': 12, 'union_type': 2}
2025-05-25 16:14:58,493 - ComprehensiveRealSystemTest - INFO - Parsing file 10/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-25 16:14:58,595 - ComprehensiveRealSystemTest - INFO - Parsed 1806 symbols in 0.099s
2025-05-25 16:14:58,595 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'reexport_all': 1, 'decorator': 493, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 2}
2025-05-25 16:14:58,595 - ComprehensiveRealSystemTest - INFO - Parsing file 11/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-25 16:14:58,684 - ComprehensiveRealSystemTest - INFO - Parsed 1805 symbols in 0.088s
2025-05-25 16:14:58,684 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 17, 'interface': 887, 'type': 167, 'function': 30, 'namespace': 31, 'enum': 39, 'reexport_all': 1, 'decorator': 493, 'callback_type': 10, 'union_type': 123, 'intersection_type': 6, 'export_assignment': 1}
2025-05-25 16:14:58,684 - ComprehensiveRealSystemTest - INFO - Parsing file 12/30: ../Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-25 16:14:58,713 - ComprehensiveRealSystemTest - INFO - Parsed 6311 symbols in 0.025s
2025-05-25 16:14:58,715 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'decorator': 6281, 'callback_type': 1, 'export_assignment': 1}
2025-05-25 16:14:58,716 - ComprehensiveRealSystemTest - INFO - Parsing file 13/30: ../comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-25 16:14:58,737 - ComprehensiveRealSystemTest - INFO - Parsed 6311 symbols in 0.021s
2025-05-25 16:14:58,738 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 3, 'interface': 19, 'namespace': 2, 'enum': 4, 'decorator': 6281, 'callback_type': 1, 'export_assignment': 1}
2025-05-25 16:14:58,738 - ComprehensiveRealSystemTest - INFO - Parsing file 14/30: ../Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-25 16:14:58,793 - ComprehensiveRealSystemTest - INFO - Parsed 3946 symbols in 0.039s
2025-05-25 16:14:58,794 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'decorator': 3899, 'union_type': 2, 'export_assignment': 1}
2025-05-25 16:14:58,794 - ComprehensiveRealSystemTest - INFO - Parsing file 15/30: ../comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-25 16:14:58,831 - ComprehensiveRealSystemTest - INFO - Parsed 3946 symbols in 0.037s
2025-05-25 16:14:58,831 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 20, 'interface': 23, 'decorator': 3899, 'union_type': 2, 'export_assignment': 1}
2025-05-25 16:14:58,841 - ComprehensiveRealSystemTest - INFO - Parsing file 16/30: ../Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-25 16:14:58,920 - ComprehensiveRealSystemTest - INFO - Parsed 3639 symbols in 0.077s
2025-05-25 16:14:58,920 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'decorator': 3512, 'union_type': 10, 'export_assignment': 1}
2025-05-25 16:14:58,921 - ComprehensiveRealSystemTest - INFO - Parsing file 17/30: ../comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-25 16:14:58,994 - ComprehensiveRealSystemTest - INFO - Parsed 3639 symbols in 0.073s
2025-05-25 16:14:58,994 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 49, 'interface': 67, 'decorator': 3512, 'union_type': 10, 'export_assignment': 1}
2025-05-25 16:14:58,994 - ComprehensiveRealSystemTest - INFO - Parsing file 18/30: ../Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-25 16:14:59,042 - ComprehensiveRealSystemTest - INFO - Parsed 4570 symbols in 0.038s
2025-05-25 16:14:59,043 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 6, 'interface': 59, 'decorator': 4503, 'export_assignment': 1}
2025-05-25 16:14:59,043 - ComprehensiveRealSystemTest - INFO - Parsing file 19/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-25 16:14:59,095 - ComprehensiveRealSystemTest - INFO - Parsed 562 symbols in 0.052s
2025-05-25 16:14:59,095 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'class': 122, 'interface': 263, 'type': 1, 'namespace': 65, 'const': 16, 'decorator': 2, 'callback_type': 1, 'union_type': 55, 'intersection_type': 36, 'export_assignment': 1}
2025-05-25 16:14:59,095 - ComprehensiveRealSystemTest - INFO - Parsing file 20/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-25 16:14:59,135 - ComprehensiveRealSystemTest - INFO - Parsed 3746 symbols in 0.040s
2025-05-25 16:14:59,135 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'decorator': 3715, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-25 16:14:59,135 - ComprehensiveRealSystemTest - INFO - Parsing file 21/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-25 16:14:59,177 - ComprehensiveRealSystemTest - INFO - Parsed 3746 symbols in 0.040s
2025-05-25 16:14:59,177 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'decorator': 3715, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-25 16:14:59,177 - ComprehensiveRealSystemTest - INFO - Parsing file 22/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-25 16:14:59,217 - ComprehensiveRealSystemTest - INFO - Parsed 3818 symbols in 0.039s
2025-05-25 16:14:59,217 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'decorator': 3794, 'union_type': 1, 'export_assignment': 1}
2025-05-25 16:14:59,217 - ComprehensiveRealSystemTest - INFO - Parsing file 23/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-25 16:14:59,254 - ComprehensiveRealSystemTest - INFO - Parsed 3818 symbols in 0.036s
2025-05-25 16:14:59,254 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 1, 'interface': 20, 'decorator': 3794, 'union_type': 1, 'export_assignment': 1}
2025-05-25 16:14:59,254 - ComprehensiveRealSystemTest - INFO - Parsing file 24/30: ../Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-25 16:14:59,297 - ComprehensiveRealSystemTest - INFO - Parsed 3336 symbols in 0.041s
2025-05-25 16:14:59,298 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 44, 'decorator': 3289, 'union_type': 1, 'export_assignment': 1}
2025-05-25 16:14:59,298 - ComprehensiveRealSystemTest - INFO - Parsing file 25/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-25 16:14:59,334 - ComprehensiveRealSystemTest - INFO - Parsed 3258 symbols in 0.035s
2025-05-25 16:14:59,334 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'decorator': 3213, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-25 16:14:59,335 - ComprehensiveRealSystemTest - INFO - Parsing file 26/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-25 16:14:59,369 - ComprehensiveRealSystemTest - INFO - Parsed 3258 symbols in 0.033s
2025-05-25 16:14:59,369 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 39, 'decorator': 3213, 'callback_type': 1, 'union_type': 3, 'export_assignment': 1}
2025-05-25 16:14:59,369 - ComprehensiveRealSystemTest - INFO - Parsing file 27/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-25 16:14:59,383 - ComprehensiveRealSystemTest - INFO - Parsed 119 symbols in 0.014s
2025-05-25 16:14:59,383 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 14, 'decorator': 14, 'union_type': 90}
2025-05-25 16:14:59,384 - ComprehensiveRealSystemTest - INFO - Parsing file 28/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-25 16:14:59,421 - ComprehensiveRealSystemTest - INFO - Parsed 3457 symbols in 0.036s
2025-05-25 16:14:59,421 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'decorator': 3419, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-25 16:14:59,421 - ComprehensiveRealSystemTest - INFO - Parsing file 29/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-25 16:14:59,451 - ComprehensiveRealSystemTest - INFO - Parsed 3457 symbols in 0.030s
2025-05-25 16:14:59,451 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'interface': 27, 'decorator': 3419, 'callback_type': 5, 'union_type': 4, 'export_assignment': 1}
2025-05-25 16:14:59,451 - ComprehensiveRealSystemTest - INFO - Parsing file 30/30: ../Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-25 16:14:59,495 - ComprehensiveRealSystemTest - INFO - Parsed 3160 symbols in 0.044s
2025-05-25 16:14:59,495 - ComprehensiveRealSystemTest - INFO -    Symbol types: {'namespace': 1, 'class': 25, 'interface': 31, 'decorator': 3100, 'callback_type': 1, 'union_type': 1, 'export_assignment': 1}
2025-05-25 16:14:59,498 - ComprehensiveRealSystemTest - INFO - Parsing completed: 30/30 files successful
2025-05-25 16:14:59,498 - ComprehensiveRealSystemTest - INFO - Total symbols parsed: 105304
2025-05-25 16:14:59,498 - ComprehensiveRealSystemTest - INFO - Symbol types found: {'type': 1132, 'namespace': 604, 'interface': 7257, 'decorator': 93685, 'callback_type': 109, 'union_type': 1408, 'intersection_type': 87, 'class': 405, 'function': 234, 'enum': 312, 'const': 34, 'reexport_all': 6, 'export_assignment': 31}
2025-05-25 16:14:59,498 - ComprehensiveRealSystemTest - INFO - Step 3: Testing indexing capabilities...
2025-05-25 16:14:59,498 - ComprehensiveRealSystemTest - INFO - === Testing Indexing Capabilities ===
2025-05-25 16:15:00,034 - ComprehensiveRealSystemTest - INFO - Qdrant collection created successfully
2025-05-25 16:15:00,034 - ComprehensiveRealSystemTest - INFO - Indexing file 1/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\lib.dom.d.ts
2025-05-25 16:15:28,697 - ComprehensiveRealSystemTest - INFO - Indexed 681 symbols in 28.663s
2025-05-25 16:15:28,697 - ComprehensiveRealSystemTest - INFO - Indexing file 2/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.dom.d.ts
2025-05-25 16:16:04,194 - ComprehensiveRealSystemTest - INFO - Indexed 777 symbols in 35.497s
2025-05-25 16:16:04,194 - ComprehensiveRealSystemTest - INFO - Indexing file 3/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-25 16:17:53,844 - ComprehensiveRealSystemTest - INFO - Indexed 2489 symbols in 109.650s
2025-05-25 16:17:53,844 - ComprehensiveRealSystemTest - INFO - Indexing file 4/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\@babel\types\lib\index.d.ts
2025-05-25 16:17:57,394 - ComprehensiveRealSystemTest - INFO - Indexed 75 symbols in 3.548s
2025-05-25 16:17:57,394 - ComprehensiveRealSystemTest - INFO - Indexing file 5/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescript.d.ts
2025-05-25 16:19:44,034 - ComprehensiveRealSystemTest - INFO - Indexed 2174 symbols in 106.640s
2025-05-25 16:19:44,034 - ComprehensiveRealSystemTest - INFO - Indexing file 6/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-25 16:21:24,377 - ComprehensiveRealSystemTest - INFO - Indexed 2173 symbols in 100.343s
2025-05-25 16:21:24,378 - ComprehensiveRealSystemTest - INFO - Indexing file 7/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\tsserverlibrary.d.ts
2025-05-25 16:23:01,609 - ComprehensiveRealSystemTest - INFO - Indexed 2105 symbols in 97.231s
2025-05-25 16:23:01,609 - ComprehensiveRealSystemTest - INFO - Indexing file 8/30: ../Information/default/openharmony/ets/build-tools\ets-loader\declarations\common.d.ts
2025-05-25 16:31:42,219 - ComprehensiveRealSystemTest - INFO - Indexed 11561 symbols in 520.609s
2025-05-25 16:31:42,220 - ComprehensiveRealSystemTest - INFO - Indexing file 9/30: ../Information/default/openharmony/ets/component\common.d.ts
2025-05-25 16:40:29,438 - ComprehensiveRealSystemTest - INFO - Indexed 11561 symbols in 527.219s
2025-05-25 16:40:29,438 - ComprehensiveRealSystemTest - INFO - Indexing file 10/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescript.d.ts
2025-05-25 16:41:51,326 - ComprehensiveRealSystemTest - INFO - Indexed 1806 symbols in 81.887s
2025-05-25 16:41:51,326 - ComprehensiveRealSystemTest - INFO - Indexing file 11/30: ../Information/default/openharmony/ets/build-tools\ets-loader\bin\ark\build-win\legacy_api8\node_modules\typescript\lib\typescriptServices.d.ts
2025-05-25 16:43:20,467 - ComprehensiveRealSystemTest - INFO - Indexed 1805 symbols in 89.141s
2025-05-25 16:43:20,469 - ComprehensiveRealSystemTest - INFO - Indexing file 12/30: ../Information/default/openharmony/ets/api\@ohos.file.fs.d.ts
2025-05-25 16:48:00,648 - ComprehensiveRealSystemTest - INFO - Indexed 6311 symbols in 280.178s
2025-05-25 16:48:00,648 - ComprehensiveRealSystemTest - INFO - Indexing file 13/30: ../comprehensive_dataset\storage\@ohos.file.fs.d.ts
2025-05-25 16:52:42,442 - ComprehensiveRealSystemTest - INFO - Indexed 6311 symbols in 281.793s
2025-05-25 16:52:42,442 - ComprehensiveRealSystemTest - INFO - Indexing file 14/30: ../Information/default/openharmony/ets/api\@ohos.data.relationalStore.d.ts
2025-05-25 16:55:51,255 - ComprehensiveRealSystemTest - INFO - Indexed 3945 symbols in 188.813s
2025-05-25 16:55:51,256 - ComprehensiveRealSystemTest - INFO - Indexing file 15/30: ../comprehensive_dataset\storage\@ohos.data.relationalStore.d.ts
2025-05-25 16:58:49,725 - ComprehensiveRealSystemTest - INFO - Indexed 3946 symbols in 178.469s
2025-05-25 16:58:49,725 - ComprehensiveRealSystemTest - INFO - Indexing file 16/30: ../Information/default/hms/ets/api\@hms.health.store.d.ts
2025-05-25 17:01:25,902 - ComprehensiveRealSystemTest - INFO - Indexed 3639 symbols in 156.176s
2025-05-25 17:01:25,903 - ComprehensiveRealSystemTest - INFO - Indexing file 17/30: ../comprehensive_dataset\hms\@hms.health.store.d.ts
2025-05-25 17:04:11,516 - ComprehensiveRealSystemTest - INFO - Indexed 3639 symbols in 165.614s
2025-05-25 17:04:11,516 - ComprehensiveRealSystemTest - INFO - Indexing file 18/30: ../Information/default/openharmony/ets/api\@ohos.security.cryptoFramework.d.ts
2025-05-25 17:07:35,942 - ComprehensiveRealSystemTest - INFO - Indexed 4570 symbols in 204.425s
2025-05-25 17:07:35,942 - ComprehensiveRealSystemTest - INFO - Indexing file 19/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\webpack\types.d.ts
2025-05-25 17:08:02,989 - ComprehensiveRealSystemTest - INFO - Indexed 562 symbols in 27.047s
2025-05-25 17:08:02,989 - ComprehensiveRealSystemTest - INFO - Indexing file 20/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.audio.d.ts
2025-05-25 17:10:47,754 - ComprehensiveRealSystemTest - INFO - Indexed 3746 symbols in 164.765s
2025-05-25 17:10:47,755 - ComprehensiveRealSystemTest - INFO - Indexing file 21/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.audio.d.ts
2025-05-25 17:13:37,588 - ComprehensiveRealSystemTest - INFO - Indexed 3746 symbols in 169.831s
2025-05-25 17:13:37,588 - ComprehensiveRealSystemTest - INFO - Indexing file 22/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.image.d.ts
2025-05-25 17:16:29,262 - ComprehensiveRealSystemTest - INFO - Indexed 3818 symbols in 171.674s
2025-05-25 17:16:29,262 - ComprehensiveRealSystemTest - INFO - Indexing file 23/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.image.d.ts
2025-05-25 17:19:19,181 - ComprehensiveRealSystemTest - INFO - Indexed 3818 symbols in 169.918s
2025-05-25 17:19:19,181 - ComprehensiveRealSystemTest - INFO - Indexing file 24/30: ../Information/default/openharmony/ets/api\@ohos.window.d.ts
2025-05-25 17:21:53,850 - ComprehensiveRealSystemTest - INFO - Indexed 3336 symbols in 154.669s
2025-05-25 17:21:53,850 - ComprehensiveRealSystemTest - INFO - Indexing file 25/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.avsession.d.ts
2025-05-25 17:24:06,169 - ComprehensiveRealSystemTest - INFO - Indexed 3258 symbols in 132.319s
2025-05-25 17:24:06,169 - ComprehensiveRealSystemTest - INFO - Indexing file 26/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.avsession.d.ts
2025-05-25 17:26:33,974 - ComprehensiveRealSystemTest - INFO - Indexed 3258 symbols in 147.805s
2025-05-25 17:26:33,975 - ComprehensiveRealSystemTest - INFO - Indexing file 27/30: ../Information/default/openharmony/ets/build-tools\ets-loader\node_modules\typescript\lib\lib.webworker.d.ts
2025-05-25 17:26:39,747 - ComprehensiveRealSystemTest - INFO - Indexed 119 symbols in 5.772s
2025-05-25 17:26:39,747 - ComprehensiveRealSystemTest - INFO - Indexing file 28/30: ../Information/default/openharmony/ets/api\@ohos.multimedia.media.d.ts
2025-05-25 17:29:21,792 - ComprehensiveRealSystemTest - INFO - Indexed 3457 symbols in 162.045s
2025-05-25 17:29:21,793 - ComprehensiveRealSystemTest - INFO - Indexing file 29/30: ../comprehensive_dataset\multimedia\@ohos.multimedia.media.d.ts
2025-05-25 17:32:02,212 - ComprehensiveRealSystemTest - INFO - Indexed 3457 symbols in 160.418s
2025-05-25 17:32:02,212 - ComprehensiveRealSystemTest - INFO - Indexing file 30/30: ../Information/default/openharmony/ets/api\@ohos.web.webview.d.ts
2025-05-25 17:34:29,946 - ComprehensiveRealSystemTest - INFO - Indexed 3160 symbols in 147.733s
2025-05-25 17:34:29,949 - ComprehensiveRealSystemTest - INFO - Indexing completed: 30/30 files successful
2025-05-25 17:34:29,949 - ComprehensiveRealSystemTest - INFO - Total symbols indexed: 105303
2025-05-25 17:34:29,949 - ComprehensiveRealSystemTest - INFO - Step 4: Testing agent capabilities...
2025-05-25 17:34:29,950 - ComprehensiveRealSystemTest - INFO - === Testing Agent Capabilities ===
2025-05-25 17:34:29,985 - ComprehensiveRealSystemTest - INFO - Testing query: 'Button' (type: component)
2025-05-25 17:35:30,092 - ComprehensiveRealSystemTest - WARNING - Query failed: Button
2025-05-25 17:35:30,093 - ComprehensiveRealSystemTest - INFO - Testing query: 'Dialog' (type: component)
2025-05-25 17:36:30,150 - ComprehensiveRealSystemTest - WARNING - Query failed: Dialog
2025-05-25 17:36:30,150 - ComprehensiveRealSystemTest - INFO - Testing query: 'Text' (type: component)
2025-05-25 17:37:30,210 - ComprehensiveRealSystemTest - WARNING - Query failed: Text
2025-05-25 17:37:30,210 - ComprehensiveRealSystemTest - INFO - Testing query: 'Image' (type: component)
2025-05-25 17:38:30,252 - ComprehensiveRealSystemTest - WARNING - Query failed: Image
2025-05-25 17:38:30,252 - ComprehensiveRealSystemTest - INFO - Testing query: 'List' (type: component)
2025-05-25 17:39:30,311 - ComprehensiveRealSystemTest - WARNING - Query failed: List
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - INFO - Testing query: 'UIAbility' (type: api)
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - ERROR - Error testing query 'UIAbility': 'Function' object is not callable
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - INFO - Testing query: 'router' (type: api)
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - ERROR - Error testing query 'router': 'Function' object is not callable
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - INFO - Testing query: 'preferences' (type: api)
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - ERROR - Error testing query 'preferences': 'Function' object is not callable
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - INFO - Testing query: 'http' (type: api)
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - ERROR - Error testing query 'http': 'Function' object is not callable
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - INFO - Testing query: 'bluetooth' (type: api)
2025-05-25 17:39:30,312 - ComprehensiveRealSystemTest - ERROR - Error testing query 'bluetooth': 'Function' object is not callable
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.app.ability.UIAbility' (type: import_path)
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.app.ability.UIAbility': 'Function' object is not callable
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.router' (type: import_path)
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.router': 'Function' object is not callable
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.data.preferences' (type: import_path)
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.data.preferences': 'Function' object is not callable
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.net.http' (type: import_path)
2025-05-25 17:39:30,313 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.net.http': 'Function' object is not callable
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - INFO - Testing query: '@ohos.bluetooth' (type: import_path)
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - ERROR - Error testing query '@ohos.bluetooth': 'Function' object is not callable
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - INFO - Testing query: 'file system' (type: api)
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - ERROR - Error testing query 'file system': 'Function' object is not callable
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - INFO - Testing query: 'data storage' (type: api)
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - ERROR - Error testing query 'data storage': 'Function' object is not callable
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - INFO - Testing query: 'network request' (type: api)
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - ERROR - Error testing query 'network request': 'Function' object is not callable
2025-05-25 17:39:30,314 - ComprehensiveRealSystemTest - INFO - Testing query: 'device sensor' (type: api)
2025-05-25 17:39:30,315 - ComprehensiveRealSystemTest - ERROR - Error testing query 'device sensor': 'Function' object is not callable
2025-05-25 17:39:30,315 - ComprehensiveRealSystemTest - INFO - Testing query: 'multimedia audio' (type: api)
2025-05-25 17:39:30,315 - ComprehensiveRealSystemTest - ERROR - Error testing query 'multimedia audio': 'Function' object is not callable
2025-05-25 17:39:30,316 - ComprehensiveRealSystemTest - INFO - Agent testing completed: 0/20 queries successful
2025-05-25 17:39:30,317 - ComprehensiveRealSystemTest - INFO - === Generating Comprehensive Report ===
2025-05-25 17:39:30,320 - ComprehensiveRealSystemTest - ERROR - Critical error in comprehensive test: 'charmap' codec can't encode character '\U0001f3af' in position 2: character maps to <undefined>
2025-05-25 17:39:30,326 - ComprehensiveRealSystemTest - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\TEMP\ImportEstimator\test_codes\test_comprehensive_real_system.py", line 501, in run_comprehensive_test
    self.generate_comprehensive_report()
  File "C:\Users\<USER>\TEMP\ImportEstimator\test_codes\test_comprehensive_real_system.py", line 599, in generate_comprehensive_report
    f.write("\n".join(report_lines))
  File "C:\Python3_10\lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f3af' in position 2: character maps to <undefined>

