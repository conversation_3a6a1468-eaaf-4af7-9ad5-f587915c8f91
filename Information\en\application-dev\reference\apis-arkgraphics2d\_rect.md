# Rect


## Overview

The **Rect** struct describes a rectangle.

**Since**: 8

**Related module**: [NativeWindow](_native_window.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| [x](#x) | Start X coordinate of the rectangle.| 
| [y](#y) | Start Y coordinate of the rectangle.| 
| [w](#w) | Width of the rectangle.| 
| [h](#h) | Height of the rectangle.| 


## x

```
int32_t Rect::x
```

**Description**

Start X coordinate of the rectangle.


## y

```
int32_t Rect::y
```

**Description**

Start Y coordinate of the rectangle.


## w

```
uint32_t Rext::w
```

**Description**

Width of the rectangle.


## h

```
uint32_t Rext::h
```

**Description**

Height of the rectangle.
