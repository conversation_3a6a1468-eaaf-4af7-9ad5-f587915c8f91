# Media Library Kit (Media File Management Service)

- [Introduction to Media Library Kit](photoAccessHelper-overview.md)
- [Selecting Media Assets Using Picker](photoAccessHelper-photoviewpicker.md)
- [Creating a Media Asset Using SaveButton](photoAccessHelper-savebutton.md)
- Moving Photos
  - [Accessing and Managing Moving Photo Assets](photoAccessHelper-movingphoto.md)
  - [Playing Moving Photos with MovingPhotoView](movingphotoview-guidelines.md)
- Restricted Open Capabilities
  - [Before You Start](photoAccessHelper-preparation.md)
  - [Managing Media Assets](photoAccessHelper-resource-guidelines.md)
  - [Managing User Albums](photoAccessHelper-userAlbum-guidelines.md)
  - [Managing System Albums](photoAccessHelper-systemAlbum-guidelines.md)
  - [Observing Media Assets](photoAccessHelper-notify-guidelines.md)
  - [Requesting Media Assets Using MediaAssetManager (C/C++)](using-ndk-mediaassetmanager-for-request-resource.md)
