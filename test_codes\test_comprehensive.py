"""
Comprehensive Test for ArkTS Import Suggestion System

This module tests all aspects of the ArkTS Import Suggestion System:
1. Basic queries
2. Component searches
3. Import path searches
4. Symbol type searches
5. Nested symbol searches
6. Hybrid searches
7. Caching
8. Enhanced ranking
9. Async queries
10. Formatting
11. Agno Tools integration
"""

import unittest
import os
import sys
import time
import json
import asyncio
from unittest.mock import patch, MagicMock

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the modules to test
from arkts_query import ArkTSQuery
from arkts_query_cached import ArkTSQueryCached
from arkts_query_enhanced import ArkTSQueryEnhanced
from arkts_query_async import ArkTSQueryAsync
from arkts_formatter import ArkTSFormatter
from arkts_agno_tools import ArkTSImportTools

# Import configuration
import config


class TestComprehensive(unittest.TestCase):
    """Comprehensive test case for ArkTS Import Suggestion System."""

    @classmethod
    def setUpClass(cls):
        """Set up test fixtures that are reused across all tests."""
        # Initialize the query engines
        cls.query = ArkTSQuery()
        cls.query_cached = ArkTSQueryCached()
        cls.query_enhanced = ArkTSQueryEnhanced()
        cls.query_async = ArkTSQueryAsync()
        cls.formatter = ArkTSFormatter()
        cls.tools = ArkTSImportTools()

        # Test queries
        cls.test_queries = [
            "audio",
            "button",
            "http",
            "dialog",
            "bluetooth"
        ]

        # Test symbol types
        cls.test_symbol_types = [
            "class",
            "interface",
            "enum",
            "namespace",
            "function"
        ]

        # Test parent symbols
        cls.test_parent_symbols = [
            "audio",
            "http",
            "bluetooth"
        ]

        # Create test results directory
        os.makedirs("test_results", exist_ok=True)

    def test_01_basic_queries(self):
        """Test basic queries."""
        print("\n=== Testing Basic Queries ===")

        for query in self.test_queries:
            print(f"\nQuery: {query}")
            results = self.query.suggest_imports(query, limit=3)

            # Check that results are returned
            self.assertIsInstance(results, list)

            # Print results
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Import: {result.get('import_statement', 'N/A')}")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Save results to file
            with open(f"test_results/basic_query_{query}.json", "w") as f:
                json.dump(results, f, indent=2)

    def test_02_component_searches(self):
        """Test component searches."""
        print("\n=== Testing Component Searches ===")

        for query in ["Chip", "Dialog", "Button"]:
            print(f"\nComponent: {query}")
            results = self.query.search_component(query, limit=3)

            # Check that results are returned
            self.assertIsInstance(results, list)

            # Print results
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Import: {result.get('import_statement', 'N/A')}")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Save results to file
            with open(f"test_results/component_search_{query}.json", "w") as f:
                json.dump(results, f, indent=2)

    def test_03_import_path_searches(self):
        """Test import path searches."""
        print("\n=== Testing Import Path Searches ===")

        for query in self.test_queries:
            print(f"\nImport Path: {query}")
            results = self.query.search_import_path(query, limit=3)

            # Check that results are returned
            self.assertIsInstance(results, list)

            # Print results
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Import: {result.get('import_statement', 'N/A')}")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Save results to file
            with open(f"test_results/import_path_{query}.json", "w") as f:
                json.dump(results, f, indent=2)

    def test_04_symbol_type_searches(self):
        """Test symbol type searches."""
        print("\n=== Testing Symbol Type Searches ===")

        for query in self.test_queries:
            for symbol_type in self.test_symbol_types:
                print(f"\nQuery: {query}, Type: {symbol_type}")
                results = self.query.search_by_symbol_type(query, symbol_type, limit=3)

                # Check that results are returned
                self.assertIsInstance(results, list)

                # Print results
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                    print(f"   Import: {result.get('import_statement', 'N/A')}")
                    print(f"   Score: {result.get('score', 0):.4f}")

                # Save results to file
                with open(f"test_results/symbol_type_{query}_{symbol_type}.json", "w") as f:
                    json.dump(results, f, indent=2)

    def test_05_nested_symbol_searches(self):
        """Test nested symbol searches."""
        print("\n=== Testing Nested Symbol Searches ===")

        for parent in self.test_parent_symbols:
            print(f"\nParent: {parent}")
            results = self.query.search_nested_symbols(parent, limit=3)

            # Check that results are returned
            self.assertIsInstance(results, list)

            # Print results
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Import: {result.get('import_statement', 'N/A')}")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Save results to file
            with open(f"test_results/nested_symbols_{parent}.json", "w") as f:
                json.dump(results, f, indent=2)

            # Test nested symbols by type
            for symbol_type in self.test_symbol_types:
                print(f"\nParent: {parent}, Type: {symbol_type}")
                results = self.query.search_nested_by_type(parent, symbol_type, limit=3)

                # Check that results are returned
                self.assertIsInstance(results, list)

                # Print results
                for i, result in enumerate(results, 1):
                    print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                    print(f"   Import: {result.get('import_statement', 'N/A')}")
                    print(f"   Score: {result.get('score', 0):.4f}")

                # Save results to file
                with open(f"test_results/nested_symbols_{parent}_{symbol_type}.json", "w") as f:
                    json.dump(results, f, indent=2)

    def test_06_hybrid_searches(self):
        """Test hybrid searches."""
        print("\n=== Testing Hybrid Searches ===")

        for query in self.test_queries:
            print(f"\nHybrid Query: {query}")

            # Standard search
            standard_results = self.query.suggest_imports(query, limit=3, use_hybrid=False)

            # Hybrid search
            hybrid_results = self.query.suggest_imports(query, limit=3, use_hybrid=True)

            # Check that results are returned
            self.assertIsInstance(standard_results, list)
            self.assertIsInstance(hybrid_results, list)

            # Print standard results
            print("Standard Results:")
            for i, result in enumerate(standard_results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Print hybrid results
            print("\nHybrid Results:")
            for i, result in enumerate(hybrid_results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Save results to file
            with open(f"test_results/hybrid_search_{query}.json", "w") as f:
                json.dump({
                    "standard": standard_results,
                    "hybrid": hybrid_results
                }, f, indent=2)

    def test_07_caching(self):
        """Test caching."""
        print("\n=== Testing Caching ===")

        # Clear cache
        self.query_cached.clear_cache()

        for query in self.test_queries[:2]:  # Use only a few queries to save time
            print(f"\nQuery: {query}")

            # First query (cache miss)
            start_time = time.time()
            results1 = self.query_cached.suggest_imports(query, limit=3)
            first_query_time = time.time() - start_time

            # Second query (cache hit)
            start_time = time.time()
            results2 = self.query_cached.suggest_imports(query, limit=3)
            second_query_time = time.time() - start_time

            # Check that results are the same
            self.assertEqual(len(results1), len(results2))

            # Check that second query is faster
            print(f"First query time: {first_query_time:.4f}s")
            print(f"Second query time: {second_query_time:.4f}s")
            print(f"Speedup: {first_query_time / max(second_query_time, 0.0001):.2f}x")

            # Save results to file
            with open(f"test_results/caching_{query}.json", "w") as f:
                json.dump({
                    "first_query_time": first_query_time,
                    "second_query_time": second_query_time,
                    "speedup": first_query_time / max(second_query_time, 0.0001),
                    "results": results1
                }, f, indent=2)

    def test_08_enhanced_ranking(self):
        """Test enhanced ranking."""
        print("\n=== Testing Enhanced Ranking ===")

        for query in self.test_queries[:2]:  # Use only a few queries to save time
            print(f"\nQuery: {query}")

            # Standard search
            standard_results = self.query.suggest_imports(query, limit=3)

            # Enhanced search
            enhanced_results = self.query_enhanced.suggest_imports_hybrid(query, limit=3)

            # Check that results are returned
            self.assertIsInstance(standard_results, list)
            self.assertIsInstance(enhanced_results, list)

            # Print standard results
            print("Standard Results:")
            for i, result in enumerate(standard_results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Score: {result.get('score', 0):.4f}")

            # Print enhanced results
            print("\nEnhanced Results:")
            for i, result in enumerate(enhanced_results, 1):
                print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                print(f"   Score: {result.get('score', 0):.4f}")
                if 'vector_score' in result and 'text_score' in result:
                    print(f"   Vector Score: {result.get('vector_score', 0):.4f}, Text Score: {result.get('text_score', 0):.4f}")

            # Save results to file
            with open(f"test_results/enhanced_ranking_{query}.json", "w") as f:
                json.dump({
                    "standard": standard_results,
                    "enhanced": enhanced_results
                }, f, indent=2)

    def test_09_async_queries(self):
        """Test async queries."""
        print("\n=== Testing Async Queries ===")

        async def run_async_tests():
            for query in self.test_queries[:2]:  # Use only a few queries to save time
                print(f"\nQuery: {query}")

                # Sync search
                start_time = time.time()
                sync_results = self.query.suggest_imports(query, limit=3)
                sync_time = time.time() - start_time

                # Async search
                start_time = time.time()
                async_results = await self.query_async.suggest_imports_async(query, limit=3)
                async_time = time.time() - start_time

                # Check that results are returned
                self.assertIsInstance(sync_results, list)
                self.assertIsInstance(async_results, list)

                # Print sync results
                print("Sync Results:")
                for i, result in enumerate(sync_results, 1):
                    print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                    print(f"   Score: {result.get('score', 0):.4f}")

                # Print async results
                print("\nAsync Results:")
                for i, result in enumerate(async_results, 1):
                    print(f"{i}. {result.get('symbol_name')} ({result.get('symbol_type')})")
                    print(f"   Score: {result.get('score', 0):.4f}")

                # Print timing
                print(f"Sync time: {sync_time:.4f}s")
                print(f"Async time: {async_time:.4f}s")

                # Save results to file
                with open(f"test_results/async_queries_{query}.json", "w") as f:
                    json.dump({
                        "sync_time": sync_time,
                        "async_time": async_time,
                        "sync_results": sync_results,
                        "async_results": async_results
                    }, f, indent=2)

        # Run async tests
        try:
            # Create a new event loop and run the async tests
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(run_async_tests())
        finally:
            loop.close()

    def test_10_formatting(self):
        """Test formatting."""
        print("\n=== Testing Formatting ===")

        # Get some results to format
        results = self.query.suggest_imports("audio", limit=5)

        # Format as general results
        general_formatted = self.formatter.format_results_for_agent(results, "general")

        # Format as component results
        component_formatted = self.formatter.format_results_for_agent(results, "component")

        # Format as import path results
        import_path_formatted = self.formatter.format_results_for_agent(results, "import_path")

        # Format as nested results
        nested_formatted = self.formatter.format_results_for_agent(results, "nested")

        # Format as JSON
        json_formatted = self.formatter.format_as_json(results)

        # Format as markdown table
        table_formatted = self.formatter.format_as_markdown_table(results)

        # Check that results are formatted
        self.assertIsInstance(general_formatted, str)
        self.assertIsInstance(component_formatted, str)
        self.assertIsInstance(import_path_formatted, str)
        self.assertIsInstance(nested_formatted, str)
        self.assertIsInstance(json_formatted, str)
        self.assertIsInstance(table_formatted, str)

        # Print formatted results
        print("General Formatted (excerpt):")
        print(general_formatted[:200] + "...\n")

        print("Component Formatted (excerpt):")
        print(component_formatted[:200] + "...\n")

        print("Import Path Formatted (excerpt):")
        print(import_path_formatted[:200] + "...\n")

        print("Nested Formatted (excerpt):")
        print(nested_formatted[:200] + "...\n")

        print("JSON Formatted (excerpt):")
        print(json_formatted[:200] + "...\n")

        print("Table Formatted (excerpt):")
        print(table_formatted[:200] + "...\n")

        # Save results to file
        with open("test_results/formatting.json", "w") as f:
            json.dump({
                "general": general_formatted,
                "component": component_formatted,
                "import_path": import_path_formatted,
                "nested": nested_formatted,
                "json": json_formatted,
                "table": table_formatted
            }, f, indent=2)

    def test_11_agno_tools(self):
        """Test Agno Tools integration."""
        print("\n=== Testing Agno Tools Integration ===")

        # Test search_component
        component_result = self.tools.search_component("Button", limit=3)

        # Test search_import_path
        import_path_result = self.tools.search_import_path("audio", limit=3)

        # Test search_arkts_api
        api_result = self.tools.search_arkts_api("bluetooth", limit=3)

        # Test handle_agent_query
        agent_result = self.tools.handle_agent_query("ArkTS search: 'audio interface'", limit=3)

        # Check that results are returned
        self.assertIsInstance(component_result, str)
        self.assertIsInstance(import_path_result, str)
        self.assertIsInstance(api_result, str)
        self.assertIsInstance(agent_result, str)

        # Print results
        print("Component Search Result (excerpt):")
        print(component_result[:200] + "...\n")

        print("Import Path Search Result (excerpt):")
        print(import_path_result[:200] + "...\n")

        print("API Search Result (excerpt):")
        print(api_result[:200] + "...\n")

        print("Agent Query Result (excerpt):")
        print(agent_result[:200] + "...\n")

        # Save results to file
        with open("test_results/agno_tools.json", "w") as f:
            json.dump({
                "component": component_result,
                "import_path": import_path_result,
                "api": api_result,
                "agent": agent_result
            }, f, indent=2)


if __name__ == "__main__":
    unittest.main()
