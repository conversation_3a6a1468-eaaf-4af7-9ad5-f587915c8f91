# This file was autogenerated by uv via the following command:
#    ./scripts/generate_requirements.sh
annotated-types==0.7.0
    # via pydantic
anyio==4.7.0
    # via httpx
certifi==2024.12.14
    # via
    #   httpcore
    #   httpx
click==8.1.8
    # via typer
docstring-parser==0.16
    # via agno (libs/agno/pyproject.toml)
exceptiongroup==1.2.2
    # via anyio
gitdb==4.0.11
    # via gitpython
gitpython==3.1.43
    # via agno (libs/agno/pyproject.toml)
h11==0.14.0
    # via httpcore
httpcore==1.0.7
    # via httpx
httpx==0.28.1
    # via agno (libs/agno/pyproject.toml)
idna==3.10
    # via
    #   anyio
    #   httpx
markdown-it-py==3.0.0
    # via rich
mdurl==0.1.2
    # via markdown-it-py
pydantic==2.10.4
    # via
    #   agno (libs/agno/pyproject.toml)
    #   pydantic-settings
pydantic-core==2.27.2
    # via pydantic
pydantic-settings==2.7.1
    # via agno (libs/agno/pyproject.toml)
pygments==2.18.0
    # via rich
python-dotenv==1.0.1
    # via
    #   agno (libs/agno/pyproject.toml)
    #   pydantic-settings
python-multipart==0.0.20
    # via agno (libs/agno/pyproject.toml)
pyyaml==6.0.2
    # via agno (libs/agno/pyproject.toml)
rich==13.9.4
    # via
    #   agno (libs/agno/pyproject.toml)
    #   typer
shellingham==1.5.4
    # via typer
smmap==5.0.1
    # via gitdb
sniffio==1.3.1
    # via anyio
tomli==2.2.1
    # via agno (libs/agno/pyproject.toml)
typer==0.15.1
    # via agno (libs/agno/pyproject.toml)
typing-extensions==4.12.2
    # via
    #   agno (libs/agno/pyproject.toml)
    #   anyio
    #   pydantic
    #   pydantic-core
    #   rich
    #   typer
