#!/usr/bin/env python3
"""
Script to clear the Qdrant database and re-index all files with maximum speed.
"""

import os
import sys
import time
import argparse
import logging
from qdrant_client import QdrantClient
from qdrant_client.http import models

# Import local modules
import config
from arkts_indexer import ArkTSIndexer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format=config.LOG_FORMAT,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("clear_and_index.log")
    ]
)
logger = logging.getLogger("ClearAndIndex")

def clear_database(qdrant_url: str, collection_name: str) -> bool:
    """
    Clear the Qdrant database by recreating the collection.
    
    Args:
        qdrant_url: URL of the Qdrant server
        collection_name: Name of the collection to clear
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Connecting to Qdrant at {qdrant_url}")
        client = QdrantClient(url=qdrant_url)
        
        # Check if collection exists
        collections = client.get_collections().collections
        collection_exists = any(collection.name == collection_name for collection in collections)
        
        if collection_exists:
            logger.info(f"Deleting existing collection: {collection_name}")
            client.delete_collection(collection_name=collection_name)
            logger.info(f"Collection {collection_name} deleted successfully")
        else:
            logger.info(f"Collection {collection_name} does not exist, nothing to delete")
        
        # Get vector size for the embedding model
        vector_size = config.VECTOR_SIZES.get(config.EMBEDDING_MODEL, config.DEFAULT_VECTOR_SIZE)
        logger.info(f"Creating new collection {collection_name} with vector size {vector_size}")
        
        # Create new collection with optimized settings for maximum performance
        client.create_collection(
            collection_name=collection_name,
            vectors_config=models.VectorParams(
                size=vector_size,
                distance=models.Distance.COSINE
            ),
            optimizers_config=models.OptimizersConfigDiff(
                indexing_threshold=20000,  # Higher threshold for faster initial indexing
                memmap_threshold=20000     # Higher threshold for better memory usage
            ),
            hnsw_config=models.HnswConfigDiff(
                m=16,                      # Higher M for better recall
                ef_construct=200,          # Higher ef_construct for better index quality
                full_scan_threshold=10000  # Higher threshold for faster initial indexing
            )
        )
        
        logger.info(f"Collection {collection_name} created successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        return False

def index_directories(directories: list, qdrant_url: str, collection_name: str, ollama_url: str, embedding_model: str) -> int:
    """
    Index all files in the specified directories with maximum speed.
    
    Args:
        directories: List of directories to index
        qdrant_url: URL of the Qdrant server
        collection_name: Name of the collection to index into
        ollama_url: URL of the Ollama server
        embedding_model: Name of the embedding model to use
        
    Returns:
        Total number of symbols indexed
    """
    try:
        # Initialize indexer
        indexer = ArkTSIndexer(
            qdrant_url=qdrant_url,
            collection_name=collection_name,
            ollama_url=ollama_url,
            embedding_model=embedding_model
        )
        
        total_symbols = 0
        total_start_time = time.time()
        
        # Index each directory
        for directory in directories:
            logger.info(f"Indexing directory: {directory}")
            print(f"\n\n{'='*80}")
            print(f"Indexing directory: {directory}")
            print(f"{'='*80}")
            
            dir_start_time = time.time()
            symbols_indexed = indexer.index_directory(directory)
            dir_time = time.time() - dir_start_time
            
            total_symbols += symbols_indexed
            
            print(f"\nDirectory {directory} completed:")
            print(f"  - Symbols indexed: {symbols_indexed}")
            print(f"  - Time taken: {dir_time:.2f} seconds")
            if symbols_indexed > 0:
                print(f"  - Speed: {symbols_indexed / dir_time:.2f} symbols/second")
            
        total_time = time.time() - total_start_time
        
        print(f"\n\n{'='*80}")
        print(f"Indexing complete!")
        print(f"{'='*80}")
        print(f"Total symbols indexed: {total_symbols}")
        print(f"Total time taken: {total_time:.2f} seconds")
        if total_symbols > 0:
            print(f"Overall speed: {total_symbols / total_time:.2f} symbols/second")
        
        return total_symbols
        
    except Exception as e:
        logger.error(f"Error indexing directories: {str(e)}")
        return 0

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Clear Qdrant database and re-index all files with maximum speed')
    parser.add_argument('--qdrant-url', type=str, default=config.QDRANT_URL, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, default=config.COLLECTION_NAME, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, default=config.OLLAMA_URL, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, default=config.EMBEDDING_MODEL, help='Ollama embedding model to use')
    parser.add_argument('--directories', type=str, nargs='+', required=True, help='Directories to index')
    parser.add_argument('--skip-clear', action='store_true', help='Skip clearing the database')
    
    args = parser.parse_args()
    
    print(f"\n{'='*80}")
    print(f"ArkTS Import Estimator - Clear and Index with MAXIMUM SPEED")
    print(f"{'='*80}")
    print(f"Qdrant URL: {args.qdrant_url}")
    print(f"Collection: {args.collection}")
    print(f"Ollama URL: {args.ollama_url}")
    print(f"Embedding model: {args.embedding_model}")
    print(f"Directories to index: {args.directories}")
    print(f"Skip clearing database: {args.skip_clear}")
    print(f"{'='*80}\n")
    
    # Clear database if not skipped
    if not args.skip_clear:
        print("Clearing database...")
        if not clear_database(args.qdrant_url, args.collection):
            print("Failed to clear database. Exiting.")
            return 1
        print("Database cleared successfully.")
    else:
        print("Skipping database clearing as requested.")
    
    # Index directories
    print("\nStarting indexing process with MAXIMUM SPEED...")
    total_symbols = index_directories(
        args.directories,
        args.qdrant_url,
        args.collection,
        args.ollama_url,
        args.embedding_model
    )
    
    if total_symbols > 0:
        print("\nIndexing completed successfully!")
        return 0
    else:
        print("\nIndexing failed or no symbols were indexed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
