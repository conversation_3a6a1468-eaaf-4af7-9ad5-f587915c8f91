# OH_Drawing_FontAdjustInfo


## Overview

The OH_Drawing_FontAdjustInfo struct describes the information about a font weight mapping.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| int [weight](#weight) | Original font weight. | 
| int [to](#to) | Font weight displayed in the application. | 


## Member Variable Description


### to

```
int OH_Drawing_FontAdjustInfo::to
```
**Description**

Font weight displayed in the application.


### weight

```
int OH_Drawing_FontAdjustInfo::weight
```
**Description**

Original font weight.
