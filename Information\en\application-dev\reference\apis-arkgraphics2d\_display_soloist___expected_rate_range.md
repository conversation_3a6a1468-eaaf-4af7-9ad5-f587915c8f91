# DisplaySoloist_ExpectedRateRange


## Overview

The DisplaySoloist_ExpectedRateRange struct describes the expected frame rate range.

**Since**: 12

**Related module**: [NativeDisplaySoloist](_native_display_soloist.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| int32_t [min](#min) | Minimum value of the expected frame rate range. The value range is [0,120].| 
| int32_t [max](#max) | Maximum value of the expected frame rate range. The value range is [0,120].| 
| int32_t [expected](#expected) | Expected frame rate. The value range is [0,120].| 


## Member Variable Description


### expected

```
int32_t DisplaySoloist_ExpectedRateRange::expected
```

**Description**

Expected frame rate. The value range is [0,120].


### max

```
int32_t DisplaySoloist_ExpectedRateRange::max
```

**Description**

Maximum value of the expected frame rate range. The value range is [0,120].


### min

```
int32_t DisplaySoloist_ExpectedRateRange::min
```

**Description**

Minimum value of the expected frame rate range. The value range is [0,120].
