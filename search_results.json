["Information\\default\\hms\\ets\\api\\@hms.ai.CardRecognition.d.ets", "Information\\default\\hms\\ets\\api\\@hms.collaboration.camera.d.ets", "Information\\default\\hms\\ets\\api\\@hms.collaboration.service.d.ets", "Information\\default\\hms\\ets\\api\\@hms.core.appgalleryservice.privacyManager.d.ts", "Information\\default\\hms\\ets\\api\\@hms.core.appgalleryservice.updateManager.d.ts", "Information\\default\\hms\\ets\\api\\@hms.core.gameservice.gameplayer.d.ts", "Information\\default\\hms\\ets\\api\\@hms.core.iap.d.ts", "Information\\default\\hms\\ets\\api\\@hms.core.payment.walletTransitCard.d.ts", "Information\\default\\hms\\ets\\api\\@hms.core.ringtone.d.ts", "Information\\default\\hms\\ets\\api\\@hms.filemanagement.filepreview.d.ts", "Information\\default\\hms\\ets\\kits\\@kit.ServiceCollaborationKit.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.ability.wantConstant.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.advertising.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.app.ability.dialogRequest.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.app.ability.wantConstant.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.arkui.advanced.Dialog.d.ets", "Information\\default\\openharmony\\ets\\api\\@ohos.arkui.UIContext.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.atomicservice.InterstitialDialogAction.d.ets", "Information\\default\\openharmony\\ets\\api\\@ohos.commonEvent.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.commonEventManager.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.file.photoAccessHelper.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.inputMethod.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.inputMethodList.d.ets", "Information\\default\\openharmony\\ets\\api\\@ohos.notificationManager.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.prompt.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.promptAction.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.router.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.UiTest.d.ts", "Information\\default\\openharmony\\ets\\api\\@ohos.window.d.ts", "Information\\default\\openharmony\\ets\\api\\@system.prompt.d.ts", "Information\\default\\openharmony\\ets\\api\\@system.router.d.ts", "Information\\default\\openharmony\\ets\\api\\permissions.d.ts", "Information\\default\\openharmony\\ets\\api\\application\\UIAbilityContext.d.ts", "Information\\default\\openharmony\\ets\\api\\security\\PermissionRequestResult.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\lib.dom.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\action_sheet.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\alert_dialog.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\alphabet_indexer.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\calendar_picker.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\common.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\custom_dialog_controller.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\date_picker.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\enums.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\index-full.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\nav_destination.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\text_picker.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\time_picker.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\declarations\\web.d.ts", "Information\\default\\openharmony\\ets\\build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.dom.d.ts", "Information\\default\\openharmony\\ets\\component\\action_sheet.d.ts", "Information\\default\\openharmony\\ets\\component\\alert_dialog.d.ts", "Information\\default\\openharmony\\ets\\component\\alphabet_indexer.d.ts", "Information\\default\\openharmony\\ets\\component\\calendar_picker.d.ts", "Information\\default\\openharmony\\ets\\component\\common.d.ts", "Information\\default\\openharmony\\ets\\component\\custom_dialog_controller.d.ts", "Information\\default\\openharmony\\ets\\component\\date_picker.d.ts", "Information\\default\\openharmony\\ets\\component\\enums.d.ts", "Information\\default\\openharmony\\ets\\component\\index-full.d.ts", "Information\\default\\openharmony\\ets\\component\\nav_destination.d.ts", "Information\\default\\openharmony\\ets\\component\\text_picker.d.ts", "Information\\default\\openharmony\\ets\\component\\time_picker.d.ts", "Information\\default\\openharmony\\ets\\component\\web.d.ts", "Information\\default\\openharmony\\ets\\kits\\@kit.AbilityKit.d.ts", "Information\\default\\openharmony\\ets\\kits\\@kit.ArkUI.d.ts", "Information\\default\\openharmony\\ets\\kits\\@kit.IMEKit.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.ability.wantConstant.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.advertising.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.app.ability.dialogRequest.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.app.ability.wantConstant.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.arkui.advanced.Dialog.d.ets", "Information\\default\\openharmony\\js\\api\\@ohos.arkui.UIContext.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.atomicservice.InterstitialDialogAction.d.ets", "Information\\default\\openharmony\\js\\api\\@ohos.commonEvent.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.commonEventManager.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.file.photoAccessHelper.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.inputMethod.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.inputMethodList.d.ets", "Information\\default\\openharmony\\js\\api\\@ohos.notificationManager.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.prompt.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.promptAction.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.router.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.UiTest.d.ts", "Information\\default\\openharmony\\js\\api\\@ohos.window.d.ts", "Information\\default\\openharmony\\js\\api\\@system.prompt.d.ts", "Information\\default\\openharmony\\js\\api\\@system.router.d.ts", "Information\\default\\openharmony\\js\\api\\permissions.d.ts", "Information\\default\\openharmony\\js\\api\\@internal\\full\\viewmodel.d.ts", "Information\\default\\openharmony\\js\\api\\application\\UIAbilityContext.d.ts", "Information\\default\\openharmony\\js\\api\\security\\PermissionRequestResult.d.ts", "Information\\default\\openharmony\\js\\build-tools\\ace-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\lib.dom.d.ts", "Information\\default\\openharmony\\js\\build-tools\\ace-loader\\node_modules\\parse5\\dist\\cjs\\common\\html.d.ts", "Information\\default\\openharmony\\js\\build-tools\\ace-loader\\node_modules\\parse5\\dist\\common\\html.d.ts"]