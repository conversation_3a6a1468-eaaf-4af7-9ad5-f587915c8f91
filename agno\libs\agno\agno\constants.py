PYTHONPATH_ENV_VAR: str = "PYTHONPATH"
AGNO_RUNTIME_ENV_VAR: str = "AGNO_RUNTIME"
AGNO_API_KEY_ENV_VAR: str = "AGNO_API_KEY"

WORKSPACE_ID_ENV_VAR: str = "AGNO_WORKSPACE_ID"
WORKSPACE_NAME_ENV_VAR: str = "AGNO_WORKSPACE_NAME"
WORKSPACE_ROOT_ENV_VAR: str = "AGNO_WORKSPACE_ROOT"
WORKSPACE_DIR_ENV_VAR: str = "AGNO_WORKSPACE_DIR"
REQUIREMENTS_FILE_PATH_ENV_VAR: str = "REQUIREMENTS_FILE_PATH"

AWS_REGION_ENV_VAR: str = "AWS_REGION"
AWS_DEFAULT_REGION_ENV_VAR: str = "AWS_DEFAULT_REGION"
AWS_PROFILE_ENV_VAR: str = "AWS_PROFILE"
