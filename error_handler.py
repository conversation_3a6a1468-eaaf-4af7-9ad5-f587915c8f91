"""
<PERSON>rro<PERSON>ler for ArkTS Import Suggestion System

This module provides error handling utilities for the ArkTS import suggestion system.
It includes classes and functions for handling connection errors, timeout errors,
validation errors, and other types of errors that may occur during the operation
of the system.
"""

import time
import logging
import httpx
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar, Union

# Configure logging
logger = logging.getLogger("ErrorHandler")

# Type variable for generic function return type
T = TypeVar('T')


class ErrorHandler:
    """Handles errors in the ArkTS import suggestion system."""
    
    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0):
        """Initialize the error handler.
        
        Args:
            max_retries: Maximum number of retries
            retry_delay: Initial delay between retries in seconds (will be increased exponentially)
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        
    def handle_connection_error(self, func: Callable[..., T], *args: Any, **kwargs: Any) -> T:
        """Handle connection errors with retry mechanism.
        
        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Result of the function
            
        Raises:
            ConnectionError: If all retries fail
        """
        for attempt in range(1, self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except (ConnectionError, httpx.ConnectError, httpx.ConnectTimeout) as e:
                logger.warning(f"Connection error (attempt {attempt}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries:
                    retry_delay = self.retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                    logger.info(f"Retrying in {retry_delay:.2f} seconds...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Failed after {self.max_retries} attempts")
                    raise ConnectionError(f"Failed to connect after {self.max_retries} attempts: {str(e)}")
    
    def handle_timeout_error(self, func: Callable[..., T], timeout: float, *args: Any, **kwargs: Any) -> T:
        """Handle timeout errors with retry mechanism.
        
        Args:
            func: Function to call
            timeout: Timeout in seconds
            *args: Function arguments
            **kwargs: Function keyword arguments
            
        Returns:
            Result of the function
            
        Raises:
            TimeoutError: If all retries fail
        """
        original_timeout = timeout
        for attempt in range(1, self.max_retries + 1):
            try:
                return func(*args, **kwargs, timeout=timeout)
            except (TimeoutError, httpx.ReadTimeout) as e:
                logger.warning(f"Timeout error (attempt {attempt}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries:
                    retry_delay = self.retry_delay * (2 ** (attempt - 1))  # Exponential backoff
                    timeout = original_timeout * 1.5 ** attempt  # Increase timeout for next attempt
                    logger.info(f"Retrying in {retry_delay:.2f} seconds with increased timeout of {timeout:.2f}s...")
                    time.sleep(retry_delay)
                else:
                    logger.error(f"Failed after {self.max_retries} attempts")
                    raise TimeoutError(f"Operation timed out after {self.max_retries} attempts: {str(e)}")
    
    def validate_query_params(self, 
                             query: Optional[str] = None, 
                             component: Optional[str] = None, 
                             symbol_type: Optional[str] = None, 
                             parent_symbol: Optional[str] = None, 
                             limit: Optional[int] = None) -> Dict[str, Any]:
        """Validate query parameters.
        
        Args:
            query: Query string
            component: Component name
            symbol_type: Symbol type
            parent_symbol: Parent symbol
            limit: Result limit
            
        Returns:
            Validated parameters
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Validate query
        if query is not None and not isinstance(query, str):
            raise ValueError(f"Query must be a string, got {type(query)}")
        
        # Validate component
        if component is not None and not isinstance(component, str):
            raise ValueError(f"Component must be a string, got {type(component)}")
        
        # Validate symbol_type
        valid_symbol_types = ['class', 'interface', 'enum', 'namespace', 'function', 'component', 'type']
        if symbol_type is not None and symbol_type not in valid_symbol_types:
            raise ValueError(f"Symbol type must be one of {valid_symbol_types}, got {symbol_type}")
        
        # Validate parent_symbol
        if parent_symbol is not None and not isinstance(parent_symbol, str):
            raise ValueError(f"Parent symbol must be a string, got {type(parent_symbol)}")
        
        # Validate limit
        if limit is not None:
            try:
                limit = int(limit)
                if limit <= 0:
                    raise ValueError(f"Limit must be a positive integer, got {limit}")
            except (ValueError, TypeError):
                raise ValueError(f"Limit must be a positive integer, got {limit}")
        
        return {
            'query': query,
            'component': component,
            'symbol_type': symbol_type,
            'parent_symbol': parent_symbol,
            'limit': limit
        }
    
    def validate_qdrant_params(self, 
                              url: Optional[str] = None, 
                              collection_name: Optional[str] = None) -> Dict[str, Any]:
        """Validate Qdrant parameters.
        
        Args:
            url: Qdrant server URL
            collection_name: Qdrant collection name
            
        Returns:
            Validated parameters
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Validate URL
        if url is not None and not isinstance(url, str):
            raise ValueError(f"URL must be a string, got {type(url)}")
        
        # Validate collection name
        if collection_name is not None and not isinstance(collection_name, str):
            raise ValueError(f"Collection name must be a string, got {type(collection_name)}")
        
        return {
            'url': url,
            'collection_name': collection_name
        }
    
    def validate_ollama_params(self, 
                              url: Optional[str] = None, 
                              model: Optional[str] = None) -> Dict[str, Any]:
        """Validate Ollama parameters.
        
        Args:
            url: Ollama server URL
            model: Ollama model name
            
        Returns:
            Validated parameters
            
        Raises:
            ValueError: If parameters are invalid
        """
        # Validate URL
        if url is not None and not isinstance(url, str):
            raise ValueError(f"URL must be a string, got {type(url)}")
        
        # Validate model
        if model is not None and not isinstance(model, str):
            raise ValueError(f"Model must be a string, got {type(model)}")
        
        return {
            'url': url,
            'model': model
        }
    
    def validate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean up query results.
        
        Args:
            results: Query results
            
        Returns:
            Validated and cleaned up results
        """
        validated_results = []
        
        for result in results:
            # Ensure all required fields are present
            if 'symbol_name' not in result:
                logger.warning(f"Result missing symbol_name: {result}")
                continue
                
            if 'symbol_type' not in result:
                logger.warning(f"Result missing symbol_type: {result}")
                result['symbol_type'] = 'unknown'
            
            # Ensure score is present and valid
            if 'score' not in result or not isinstance(result['score'], (int, float)):
                logger.warning(f"Result has invalid score: {result}")
                result['score'] = 0.0
            
            # Clean up description
            if 'description' in result and result['description'] is not None:
                # Truncate long descriptions
                if len(result['description']) > 1000:
                    result['description'] = result['description'][:997] + "..."
            else:
                result['description'] = ""
            
            validated_results.append(result)
        
        return validated_results


# Create a global instance for convenience
error_handler = ErrorHandler()
