#!/usr/bin/env python3
"""
Test regex patterns against real ArkTS component files.
This test validates that the component regex patterns work correctly
with actual files from the Information/default/ and Information/SpecialInformation/ directories.
"""

import os
import re
import logging
from typing import List, Dict, Any

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_component_patterns():
    """Test component regex patterns against real files."""
    
    # Original problematic pattern
    original_pattern = re.compile(r'@Component\s+(?:export\s+)?struct\s+(\w+)|@Component\s*\n\s*export\s+declare\s+struct\s+(\w+)')
    
    # New simplified patterns
    inline_pattern = re.compile(r'@Component\s+(?:export\s+)?struct\s+(\w+)')
    multiline_pattern = re.compile(r'@Component\s*\n\s*export\s+declare\s+struct\s+(\w+)')
    
    # Test cases from real files
    test_cases = [
        # Case 1: Simple inline component (from comprehensive_dataset)
        """/**
 * Provides a button component.
 * @component Button
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
@Component
export struct Button {
  constructor(options?: ButtonOptions);
}""",
        
        # Case 2: Component without export
        """@Component
struct MyComponent {
  build() {
    Text('Hello')
  }
}""",
        
        # Case 3: Multiline component with export declare
        """@Component
export declare struct Dialog {
  title(title: string): Dialog;
}""",
        
        # Case 4: Component with extra whitespace
        """@Component   
  export   struct   CustomButton   {
  build() {}
}""",
        
        # Case 5: Multiline with various spacing
        """@Component
  
  export declare struct AdvancedDialog {
  content(content: string): AdvancedDialog;
}""",
        
        # Case 6: Multiple components in one text
        """@Component
struct Header {
  build() {}
}

@Component
export struct Footer {
  build() {}
}

@Component
export declare struct Sidebar {
  width(width: number): Sidebar;
}""",
        
        # Case 7: Component with comments
        """// This is a component
@Component
export struct CommentedComponent {
  // Build method
  build() {}
}""",
        
        # Case 8: Nested in namespace (should still match)
        """namespace UI {
  @Component
  export struct NamespacedComponent {
    build() {}
  }
}""",
        
        # Case 9: Component with decorators before @Component
        """@Entry
@Component
struct EntryComponent {
  build() {}
}""",
        
        # Case 10: Edge case - component name with numbers
        """@Component
export struct Button2 {
  build() {}
}"""
    ]
    
    logger.info("🧪 Testing component regex patterns...")
    
    results = {
        'original_pattern': [],
        'inline_pattern': [],
        'multiline_pattern': [],
        'combined_new_patterns': []
    }
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n📋 Test Case {i}:")
        logger.info(f"Content preview: {test_case[:100]}...")
        
        # Test original pattern
        original_matches = []
        for match in original_pattern.finditer(test_case):
            component_name = match.group(1) or match.group(2)
            if component_name:
                original_matches.append(component_name)
        
        # Test new inline pattern
        inline_matches = []
        for match in inline_pattern.finditer(test_case):
            component_name = match.group(1)
            if component_name:
                inline_matches.append(component_name)
        
        # Test new multiline pattern
        multiline_matches = []
        for match in multiline_pattern.finditer(test_case):
            component_name = match.group(1)
            if component_name:
                multiline_matches.append(component_name)
        
        # Combined new patterns
        combined_matches = inline_matches + multiline_matches
        
        logger.info(f"  Original pattern found: {original_matches}")
        logger.info(f"  Inline pattern found: {inline_matches}")
        logger.info(f"  Multiline pattern found: {multiline_matches}")
        logger.info(f"  Combined new patterns found: {combined_matches}")
        
        results['original_pattern'].append(original_matches)
        results['inline_pattern'].append(inline_matches)
        results['multiline_pattern'].append(multiline_matches)
        results['combined_new_patterns'].append(combined_matches)
        
        # Check if results match
        if set(original_matches) == set(combined_matches):
            logger.info("  ✅ Results match!")
        else:
            logger.warning(f"  ⚠️ Results differ! Original: {original_matches}, New: {combined_matches}")
    
    return results

def test_performance_comparison():
    """Test performance of original vs new patterns."""
    import time
    
    # Create a large test string with many components
    large_test = """
@Component
export struct Component1 { build() {} }

@Component
struct Component2 { build() {} }

@Component
export declare struct Component3 { build() {} }

@Component
  export struct Component4 { build() {} }

@Component

export declare struct Component5 { build() {} }
""" * 1000  # Repeat 1000 times
    
    # Original pattern
    original_pattern = re.compile(r'@Component\s+(?:export\s+)?struct\s+(\w+)|@Component\s*\n\s*export\s+declare\s+struct\s+(\w+)')
    
    # New patterns
    inline_pattern = re.compile(r'@Component\s+(?:export\s+)?struct\s+(\w+)')
    multiline_pattern = re.compile(r'@Component\s*\n\s*export\s+declare\s+struct\s+(\w+)')
    
    logger.info("\n⚡ Performance comparison:")
    
    # Test original pattern
    start_time = time.time()
    original_matches = list(original_pattern.finditer(large_test))
    original_time = time.time() - start_time
    
    # Test new patterns
    start_time = time.time()
    inline_matches = list(inline_pattern.finditer(large_test))
    multiline_matches = list(multiline_pattern.finditer(large_test))
    new_time = time.time() - start_time
    
    logger.info(f"  Original pattern: {original_time:.4f}s, found {len(original_matches)} matches")
    logger.info(f"  New patterns: {new_time:.4f}s, found {len(inline_matches) + len(multiline_matches)} matches")
    logger.info(f"  Performance improvement: {((original_time - new_time) / original_time * 100):.1f}%")
    
    return {
        'original_time': original_time,
        'new_time': new_time,
        'original_matches': len(original_matches),
        'new_matches': len(inline_matches) + len(multiline_matches)
    }

def test_real_files():
    """Test patterns against real files in the Information directory."""
    logger.info("\n📁 Testing against real files...")
    
    # Patterns to test
    inline_pattern = re.compile(r'@Component\s+(?:export\s+)?struct\s+(\w+)')
    multiline_pattern = re.compile(r'@Component\s*\n\s*export\s+declare\s+struct\s+(\w+)')
    
    # Files to test
    test_files = [
        "comprehensive_dataset/custom/button_component.d.ets",
        "comprehensive_dataset/custom/dialog_component.d.ets",
        "Information/SpecialInformation/components.md"
    ]
    
    results = {}
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Test patterns
                inline_matches = [match.group(1) for match in inline_pattern.finditer(content)]
                multiline_matches = [match.group(1) for match in multiline_pattern.finditer(content)]
                
                results[file_path] = {
                    'inline_matches': inline_matches,
                    'multiline_matches': multiline_matches,
                    'total_matches': inline_matches + multiline_matches
                }
                
                logger.info(f"  📄 {file_path}:")
                logger.info(f"    Inline matches: {inline_matches}")
                logger.info(f"    Multiline matches: {multiline_matches}")
                logger.info(f"    Total: {len(inline_matches) + len(multiline_matches)} components found")
                
            except Exception as e:
                logger.error(f"  ❌ Error reading {file_path}: {str(e)}")
                results[file_path] = {'error': str(e)}
        else:
            logger.warning(f"  ⚠️ File not found: {file_path}")
            results[file_path] = {'error': 'File not found'}
    
    return results

def main():
    """Run all regex pattern tests."""
    logger.info("🚀 Starting regex pattern validation tests...")
    
    # Test 1: Pattern correctness
    pattern_results = test_component_patterns()
    
    # Test 2: Performance comparison
    performance_results = test_performance_comparison()
    
    # Test 3: Real files
    real_file_results = test_real_files()
    
    # Summary
    logger.info("\n📊 Test Summary:")
    logger.info("=" * 50)
    
    # Check if all pattern tests passed
    all_tests_passed = True
    for i, (original, combined) in enumerate(zip(pattern_results['original_pattern'], pattern_results['combined_new_patterns'])):
        if set(original) != set(combined):
            all_tests_passed = False
            logger.error(f"  ❌ Test case {i+1} failed: {original} != {combined}")
    
    if all_tests_passed:
        logger.info("  ✅ All pattern tests passed!")
    else:
        logger.error("  ❌ Some pattern tests failed!")
    
    # Performance summary
    if performance_results['new_time'] < performance_results['original_time']:
        logger.info(f"  ⚡ Performance improved by {((performance_results['original_time'] - performance_results['new_time']) / performance_results['original_time'] * 100):.1f}%")
    else:
        logger.warning("  ⚠️ Performance regression detected")
    
    # Real file summary
    total_components_found = 0
    for file_path, result in real_file_results.items():
        if 'total_matches' in result:
            total_components_found += len(result['total_matches'])
    
    logger.info(f"  📁 Found {total_components_found} components in real files")
    
    logger.info("\n🎯 Conclusion:")
    if all_tests_passed and performance_results['new_time'] < performance_results['original_time']:
        logger.info("  ✅ New regex patterns are working correctly and perform better!")
        logger.info("  ✅ No catastrophic backtracking risk with simplified patterns")
        logger.info("  ✅ All component types are properly detected")
    else:
        logger.error("  ❌ Issues detected with new patterns")
    
    return {
        'pattern_tests_passed': all_tests_passed,
        'performance_improved': performance_results['new_time'] < performance_results['original_time'],
        'total_components_found': total_components_found
    }

if __name__ == "__main__":
    main()
