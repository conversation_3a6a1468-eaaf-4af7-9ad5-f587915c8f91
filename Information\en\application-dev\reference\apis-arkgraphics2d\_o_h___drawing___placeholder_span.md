# OH_Drawing_PlaceholderSpan


## Overview

The **OH_Drawing_PlaceholderSpan** struct describes the placeholder that acts as a span.

**Since**: 11

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description|
| -------- | -------- |
| double [width](#width) | Width of a placeholder.|
| double [height](#height) | Height of a placeholder.|
| [OH_Drawing_PlaceholderVerticalAlignment](_drawing.md#oh_drawing_placeholderverticalalignment) [alignment](#alignment) | Alignment mode of a placeholder.|
| [OH_Drawing_TextBaseline](_drawing.md#oh_drawing_textbaseline) [baseline](#baseline) | Baseline of a placeholder.|
| double [baselineOffset](#baselineoffset) | Baseline offset of a placeholder.|


## Member Variable Description


### alignment

```
OH_Drawing_PlaceholderVerticalAlignment OH_Drawing_PlaceholderSpan::alignment
```

**Description**

Alignment mode of a placeholder.


### baseline

```
OH_Drawing_TextBaseline OH_Drawing_PlaceholderSpan::baseline
```

**Description**

Baseline of a placeholder.


### baselineOffset

```
double OH_Drawing_PlaceholderSpan::baselineOffset
```

**Description**

Baseline offset of a placeholder.


### height

```
double OH_Drawing_PlaceholderSpan::height
```

**Description**

Height of a placeholder.


### width

```
double OH_Drawing_PlaceholderSpan::width
```

**Description**

Width of a placeholder.
