"""
Performance Optimizer for ArkTS Import Suggestion System

This module provides utilities for optimizing the performance of the ArkTS import
suggestion system. It includes classes and functions for caching, batch processing,
and other performance optimizations.
"""

import time
import logging
import hashlib
import functools
import concurrent.futures
from typing import Any, Callable, Dict, List, Optional, Tuple, TypeVar, Union
from collections import OrderedDict

# Configure logging
logger = logging.getLogger("PerformanceOptimizer")

# Type variable for generic function return type
T = TypeVar('T')
F = TypeVar('F', bound=Callable[..., Any])


class LRUCache:
    """LRU (Least Recently Used) cache implementation."""

    def __init__(self, maxsize: int = 128, ttl: float = 3600.0):
        """Initialize the LRU cache.

        Args:
            maxsize: Maximum size of the cache
            ttl: Time-to-live for cache entries in seconds
        """
        self.maxsize = maxsize
        self.ttl = ttl
        self.cache: OrderedDict[str, Tuple[float, Any]] = OrderedDict()

    def __contains__(self, key: str) -> bool:
        """Check if key is in cache and not expired.

        Args:
            key: Cache key

        Returns:
            True if key is in cache and not expired, False otherwise
        """
        if key not in self.cache:
            return False

        timestamp, _ = self.cache[key]
        if time.time() - timestamp > self.ttl:
            # Entry has expired
            del self.cache[key]
            return False

        # Move to end (most recently used)
        self.cache.move_to_end(key)
        return True

    def __getitem__(self, key: str) -> Any:
        """Get value for key.

        Args:
            key: Cache key

        Returns:
            Cached value

        Raises:
            KeyError: If key is not in cache or has expired
        """
        if key not in self:
            raise KeyError(key)

        _, value = self.cache[key]
        return value

    def __setitem__(self, key: str, value: Any) -> None:
        """Set value for key.

        Args:
            key: Cache key
            value: Value to cache
        """
        # Add/update entry
        self.cache[key] = (time.time(), value)

        # Move to end (most recently used)
        self.cache.move_to_end(key)

        # Remove oldest entries if cache is too large
        while len(self.cache) > self.maxsize:
            self.cache.popitem(last=False)

    def clear(self) -> None:
        """Clear the cache."""
        self.cache.clear()

    def get(self, key: str, default: Any = None) -> Any:
        """Get value for key, or default if key is not in cache or has expired.

        Args:
            key: Cache key
            default: Default value to return if key is not in cache

        Returns:
            Cached value or default
        """
        try:
            return self[key]
        except KeyError:
            return default


class PerformanceOptimizer:
    """Optimizes performance of the ArkTS import suggestion system."""

    def __init__(self, cache_size: int = 1000, cache_ttl: float = 3600.0,
                 batch_size: int = 10, num_threads: int = 4):
        """Initialize the performance optimizer.

        Args:
            cache_size: Cache size
            cache_ttl: Cache TTL in seconds
            batch_size: Batch size for batch processing
            num_threads: Number of threads for parallel processing
        """
        self.cache_size = cache_size
        self.cache_ttl = cache_ttl
        self.batch_size = batch_size
        self.num_threads = num_threads

        # Initialize cache
        self.memory_cache = LRUCache(maxsize=cache_size, ttl=cache_ttl)

        # Initialize thread pool
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=num_threads)

    def cached(self, func: F) -> F:
        """Decorator to cache function results.

        Args:
            func: Function to cache

        Returns:
            Cached function
        """
        @functools.wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            # Create cache key
            cache_key = self._create_cache_key(func.__name__, args, kwargs)

            # Check cache
            if cache_key in self.memory_cache:
                logger.debug(f"Cache hit for {cache_key}")
                return self.memory_cache[cache_key]

            # Execute function
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()

            # Log execution time
            logger.debug(f"Function {func.__name__} took {end_time - start_time:.4f}s")

            # Update cache
            self.memory_cache[cache_key] = result

            return result

        return wrapper  # type: ignore

    def batch_process(self, func: Callable[[List[Any]], List[Any]], items: List[Any],
                     adaptive_sizing: bool = True) -> List[Any]:
        """Process items in batches with optional adaptive batch sizing.

        Args:
            func: Function to process batches
            items: Items to process
            adaptive_sizing: Whether to use adaptive batch sizing

        Returns:
            Processed items
        """
        if not items:
            return []

        # Use adaptive batch sizing if requested
        if adaptive_sizing and len(items) > self.batch_size * 2:
            return self._adaptive_batch_process(func, items)

        # Split items into batches
        batches = [items[i:i + self.batch_size] for i in range(0, len(items), self.batch_size)]

        results = []
        for i, batch in enumerate(batches):
            logger.debug(f"Processing batch {i+1}/{len(batches)} with {len(batch)} items")

            # Process batch
            start_time = time.time()
            batch_results = func(batch)
            end_time = time.time()

            # Log batch processing time
            logger.debug(f"Batch {i+1}/{len(batches)} took {end_time - start_time:.4f}s")

            # Add batch results to overall results
            results.extend(batch_results)

        return results

    def _adaptive_batch_process(self, func: Callable[[List[Any]], List[Any]], items: List[Any]) -> List[Any]:
        """Process items in batches with adaptive batch sizing.

        This method adjusts the batch size based on processing time to optimize throughput.

        Args:
            func: Function to process batches
            items: Items to process

        Returns:
            Processed items
        """
        results = []
        remaining_items = items.copy()

        # Start with the configured batch size
        current_batch_size = self.batch_size
        min_batch_size = max(1, self.batch_size // 4)
        max_batch_size = min(len(items), self.batch_size * 4)

        batch_num = 0
        target_time_per_batch = 1.0  # Target 1 second per batch

        while remaining_items:
            # Get the next batch
            batch = remaining_items[:current_batch_size]
            remaining_items = remaining_items[current_batch_size:]

            batch_num += 1
            logger.debug(f"Processing adaptive batch {batch_num} with {len(batch)} items")

            # Process batch
            start_time = time.time()
            batch_results = func(batch)
            end_time = time.time()

            batch_time = end_time - start_time

            # Log batch processing time
            logger.debug(f"Adaptive batch {batch_num} took {batch_time:.4f}s")

            # Add batch results to overall results
            results.extend(batch_results)

            # Adjust batch size based on processing time
            if batch_time > 0:
                # Adjust batch size to target processing time
                ideal_batch_size = int(current_batch_size * (target_time_per_batch / batch_time))

                # Clamp to min/max batch size
                current_batch_size = max(min_batch_size, min(max_batch_size, ideal_batch_size))

                logger.debug(f"Adjusted batch size to {current_batch_size}")

        return results

    def parallel_process(self, func: Callable[[Any], Any], items: List[Any],
                       chunk_size: Optional[int] = None,
                       preserve_order: bool = True) -> List[Any]:
        """Process items in parallel with improved performance.

        Args:
            func: Function to process items
            items: Items to process
            chunk_size: Size of chunks for processing (None for auto-sizing)
            preserve_order: Whether to preserve the order of results

        Returns:
            Processed items
        """
        if not items:
            return []

        # Determine optimal chunk size if not specified
        if chunk_size is None:
            # Use a chunk size that balances overhead and parallelism
            chunk_size = max(1, min(100, len(items) // (self.num_threads * 2)))

        logger.debug(f"Processing {len(items)} items in parallel with {self.num_threads} threads and chunk size {chunk_size}")

        # Group items into chunks for better performance
        chunks = [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]

        # Define a function to process a chunk of items
        def process_chunk(chunk_items):
            return [func(item) for item in chunk_items]

        # Submit chunks for processing
        start_time = time.time()

        if preserve_order:
            # Use map to preserve order
            results = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.num_threads) as executor:
                for chunk_results in executor.map(process_chunk, chunks):
                    results.extend(chunk_results)
        else:
            # Use submit/as_completed for potentially better performance
            futures = []
            with concurrent.futures.ThreadPoolExecutor(max_workers=self.num_threads) as executor:
                # Submit all chunks for processing
                futures = [executor.submit(process_chunk, chunk) for chunk in chunks]

                # Collect results as they complete
                results = []
                for future in concurrent.futures.as_completed(futures):
                    try:
                        chunk_results = future.result()
                        results.extend(chunk_results)
                    except Exception as e:
                        logger.error(f"Error in parallel processing: {str(e)}")

        end_time = time.time()

        # Log parallel processing time
        logger.debug(f"Parallel processing of {len(items)} items took {end_time - start_time:.4f}s")

        return results

    def parallel_batch_process(self, func: Callable[[List[Any]], List[Any]], items: List[Any],
                            batch_size: Optional[int] = None) -> List[Any]:
        """Process items in parallel batches for maximum performance.

        This method combines the benefits of batch processing and parallel processing.

        Args:
            func: Function to process batches
            items: Items to process
            batch_size: Size of batches (None for auto-sizing)

        Returns:
            Processed items
        """
        if not items:
            return []

        # Determine optimal batch size if not specified
        if batch_size is None:
            # Calculate a batch size that balances overhead and parallelism
            # Each thread should process at least one batch
            items_per_thread = max(1, len(items) // self.num_threads)
            batch_size = max(1, min(self.batch_size, items_per_thread))

        logger.debug(f"Processing {len(items)} items in parallel batches with {self.num_threads} threads and batch size {batch_size}")

        # Split items into batches
        batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]

        # Process batches in parallel
        start_time = time.time()
        batch_results = self.parallel_process(func, batches, preserve_order=False)
        end_time = time.time()

        # Flatten results
        results = []
        for batch_result in batch_results:
            results.extend(batch_result)

        # Log processing time
        logger.debug(f"Parallel batch processing of {len(items)} items took {end_time - start_time:.4f}s")

        return results

    def _create_cache_key(self, func_name: str, args: Tuple[Any, ...], kwargs: Dict[str, Any]) -> str:
        """Create a cache key from function name, args and kwargs.

        Args:
            func_name: Function name
            args: Function args
            kwargs: Function kwargs

        Returns:
            Cache key
        """
        # Convert args and kwargs to a string
        args_str = str(args)
        kwargs_str = str(sorted(kwargs.items()))

        # Create cache key
        cache_key = f"{func_name}:{args_str}:{kwargs_str}"

        # Hash the cache key
        return hashlib.md5(cache_key.encode()).hexdigest()


# Create a global instance for convenience
performance_optimizer = PerformanceOptimizer()
