# resourceManager Switching


  | API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding Field in the Stage Model| 
| -------- | -------- | -------- |
| [getResourceManager(callback: AsyncCallback&lt;ResourceManager&gt;): void;](../reference/apis-localization-kit/js-apis-resource-manager.md#resourcemanagergetresourcemanager)<br>[getResourceManager(bundleName: string, callback: AsyncCallback&lt;ResourceManager&gt;): void;](../reference/apis-localization-kit/js-apis-resource-manager.md#resourcemanagergetresourcemanager-1)<br>[getResourceManager(): Promise&lt;ResourceManager&gt;;](../reference/apis-localization-kit/js-apis-resource-manager.md#resourcemanagergetresourcemanager-2)<br>[getResourceManager(bundleName: string): Promise&lt;ResourceManager&gt;;](../reference/apis-localization-kit/js-apis-resource-manager.md#resourcemanagergetresourcemanager-3) | application\Context.d.ts | [resourceManager: resmgr.ResourceManager;](../reference/apis-ability-kit/js-apis-inner-application-context.md#attributes)|
