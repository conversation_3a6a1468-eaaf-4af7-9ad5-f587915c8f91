# @ohos.app.ability.ChildProcessOptions

The ChildProcessOptions module describes the startup configuration of a child process. When starting a child process through [childProcessManager](js-apis-app-ability-childProcessManager.md), you can configure the startup configuration of the child process through **ChildProcessOptions**.

> **NOTE**
> 
> The initial APIs of this module are supported since API version 12. Newly added APIs will be marked with a superscript to indicate their earliest API version.
> 
> The APIs of this module can be used only in the stage model.

## Modules to Import

```ts
import { ChildProcessOptions } from '@kit.AbilityKit';
```

## Properties

**System capability**: SystemCapability.Ability.AbilityRuntime.Core

| Name       | Type     | Mandatory| Description                                                              |
| ----------- | --------------------   | ---- | ---------------------------------------------------- |
| isolationMode | boolean | No| Whether the child process runs in an independent sandbox environment. The default value is **false**, indicating that the child process runs in a non-independent sandbox environment.<br>This parameter is reserved. Currently, the child process can run only in a non-independent sandbox environment.|

**Example**

```ts
import { ChildProcessArgs, ChildProcessOptions, childProcessManager } from '@kit.AbilityKit';

let args: ChildProcessArgs = {};
let options: ChildProcessOptions = {
  isolationMode: false
};
childProcessManager.startArkChildProcess("entry/./ets/process/DemoProcess.ets", args, options);
```
