# OH_Drawing_FontStyleStruct


## Overview

The OH_Drawing_FontStyleStruct struct describes a font style.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| [OH_Drawing_FontWeight](_drawing.md#oh_drawing_fontweight) [weight](#weight) | Font weight. | 
| [OH_Drawing_FontWidth](_drawing.md#oh_drawing_fontwidth) [width](#width) | Font width. | 
| [OH_Drawing_FontStyle](_drawing.md#oh_drawing_fontstyle) [slant](#slant) | Font slant. | 


## Member Variable Description


### slant

```
OH_Drawing_FontStyle OH_Drawing_FontStyleStruct::slant
```
**Description**

Font slant.


### weight

```
OH_Drawing_FontWeight OH_Drawing_FontStyleStruct::weight
```
**Description**

Font weight.


### width

```
OH_Drawing_FontWidth OH_Drawing_FontStyleStruct::width
```
**Description**

Font width.
