{"parsing_results": {"total_files": 30, "successful_files": 30, "failed_files": 0, "total_symbols": 105304, "symbol_types": {"type": 1132, "namespace": 604, "interface": 7257, "decorator": 93685, "callback_type": 109, "union_type": 1408, "intersection_type": 87, "class": 405, "function": 234, "enum": 312, "const": 34, "reexport_all": 6, "export_assignment": 31}, "file_results": [{"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\lib.dom.d.ts", "file_size": 842681, "success": true, "symbols_count": 681, "parse_time": 0.03919720649719238, "symbol_types": {"type": 1, "namespace": 2, "interface": 14, "decorator": 486, "callback_type": 1, "union_type": 176, "intersection_type": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.dom.d.ts", "file_size": 816786, "success": true, "symbols_count": 777, "parse_time": 0.029356718063354492, "symbol_types": {"namespace": 2, "interface": 14, "decorator": 568, "callback_type": 2, "union_type": 190, "intersection_type": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\tsserverlibrary.d.ts", "file_size": 747710, "success": true, "symbols_count": 2489, "parse_time": 0.14863061904907227, "symbol_types": {"class": 45, "interface": 1244, "type": 185, "function": 50, "namespace": 110, "enum": 41, "const": 9, "reexport_all": 1, "decorator": 632, "callback_type": 12, "union_type": 145, "intersection_type": 12, "export_assignment": 3}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\@babel\\types\\lib\\index.d.ts", "file_size": 611184, "success": true, "symbols_count": 75, "parse_time": 0.02127385139465332, "symbol_types": {"namespace": 2, "decorator": 16, "union_type": 57}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\typescript.d.ts", "file_size": 598789, "success": true, "symbols_count": 2174, "parse_time": 0.11792683601379395, "symbol_types": {"class": 35, "interface": 1022, "type": 183, "function": 44, "namespace": 100, "enum": 41, "reexport_all": 1, "decorator": 594, "callback_type": 10, "union_type": 134, "intersection_type": 7, "export_assignment": 3}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\typescriptServices.d.ts", "file_size": 598776, "success": true, "symbols_count": 2173, "parse_time": 0.13370275497436523, "symbol_types": {"class": 35, "interface": 1022, "type": 183, "function": 44, "namespace": 100, "enum": 41, "reexport_all": 1, "decorator": 594, "callback_type": 10, "union_type": 134, "intersection_type": 7, "export_assignment": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\tsserverlibrary.d.ts", "file_size": 587234, "success": true, "symbols_count": 2105, "parse_time": 0.1090993881225586, "symbol_types": {"class": 27, "interface": 1106, "type": 169, "function": 36, "namespace": 41, "enum": 39, "const": 9, "reexport_all": 1, "decorator": 519, "callback_type": 12, "union_type": 133, "intersection_type": 11, "export_assignment": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\declarations\\common.d.ts", "file_size": 566649, "success": true, "symbols_count": 11561, "parse_time": 0.059435367584228516, "symbol_types": {"class": 14, "interface": 103, "type": 38, "namespace": 2, "enum": 32, "decorator": 11358, "callback_type": 12, "union_type": 2}}, {"file_path": "../Information/default/openharmony/ets/component\\common.d.ts", "file_size": 566343, "success": true, "symbols_count": 11561, "parse_time": 0.0651395320892334, "symbol_types": {"class": 14, "interface": 103, "type": 38, "namespace": 2, "enum": 32, "decorator": 11358, "callback_type": 12, "union_type": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\typescript.d.ts", "file_size": 447512, "success": true, "symbols_count": 1806, "parse_time": 0.09899306297302246, "symbol_types": {"class": 17, "interface": 887, "type": 167, "function": 30, "namespace": 31, "enum": 39, "reexport_all": 1, "decorator": 493, "callback_type": 10, "union_type": 123, "intersection_type": 6, "export_assignment": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\typescriptServices.d.ts", "file_size": 447499, "success": true, "symbols_count": 1805, "parse_time": 0.0881037712097168, "symbol_types": {"class": 17, "interface": 887, "type": 167, "function": 30, "namespace": 31, "enum": 39, "reexport_all": 1, "decorator": 493, "callback_type": 10, "union_type": 123, "intersection_type": 6, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.file.fs.d.ts", "file_size": 372692, "success": true, "symbols_count": 6311, "parse_time": 0.025374889373779297, "symbol_types": {"class": 3, "interface": 19, "namespace": 2, "enum": 4, "decorator": 6281, "callback_type": 1, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\storage\\@ohos.file.fs.d.ts", "file_size": 372692, "success": true, "symbols_count": 6311, "parse_time": 0.020591259002685547, "symbol_types": {"class": 3, "interface": 19, "namespace": 2, "enum": 4, "decorator": 6281, "callback_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.data.relationalStore.d.ts", "file_size": 363266, "success": true, "symbols_count": 3946, "parse_time": 0.03936648368835449, "symbol_types": {"namespace": 1, "class": 20, "interface": 23, "decorator": 3899, "union_type": 2, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\storage\\@ohos.data.relationalStore.d.ts", "file_size": 363266, "success": true, "symbols_count": 3946, "parse_time": 0.037062883377075195, "symbol_types": {"namespace": 1, "class": 20, "interface": 23, "decorator": 3899, "union_type": 2, "export_assignment": 1}}, {"file_path": "../Information/default/hms/ets/api\\@hms.health.store.d.ts", "file_size": 346063, "success": true, "symbols_count": 3639, "parse_time": 0.07672739028930664, "symbol_types": {"namespace": 49, "interface": 67, "decorator": 3512, "union_type": 10, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\hms\\@hms.health.store.d.ts", "file_size": 346063, "success": true, "symbols_count": 3639, "parse_time": 0.0731658935546875, "symbol_types": {"namespace": 49, "interface": 67, "decorator": 3512, "union_type": 10, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.security.cryptoFramework.d.ts", "file_size": 336615, "success": true, "symbols_count": 4570, "parse_time": 0.03809857368469238, "symbol_types": {"namespace": 1, "class": 6, "interface": 59, "decorator": 4503, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\webpack\\types.d.ts", "file_size": 335559, "success": true, "symbols_count": 562, "parse_time": 0.052435874938964844, "symbol_types": {"class": 122, "interface": 263, "type": 1, "namespace": 65, "const": 16, "decorator": 2, "callback_type": 1, "union_type": 55, "intersection_type": 36, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.audio.d.ts", "file_size": 325027, "success": true, "symbols_count": 3746, "parse_time": 0.03981733322143555, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3715, "callback_type": 1, "union_type": 1, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.audio.d.ts", "file_size": 325027, "success": true, "symbols_count": 3746, "parse_time": 0.03992652893066406, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3715, "callback_type": 1, "union_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.image.d.ts", "file_size": 296616, "success": true, "symbols_count": 3818, "parse_time": 0.03935098648071289, "symbol_types": {"namespace": 1, "class": 1, "interface": 20, "decorator": 3794, "union_type": 1, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.image.d.ts", "file_size": 296616, "success": true, "symbols_count": 3818, "parse_time": 0.03618025779724121, "symbol_types": {"namespace": 1, "class": 1, "interface": 20, "decorator": 3794, "union_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.window.d.ts", "file_size": 286762, "success": true, "symbols_count": 3336, "parse_time": 0.04143357276916504, "symbol_types": {"namespace": 1, "interface": 44, "decorator": 3289, "union_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.avsession.d.ts", "file_size": 276572, "success": true, "symbols_count": 3258, "parse_time": 0.03531599044799805, "symbol_types": {"namespace": 1, "interface": 39, "decorator": 3213, "callback_type": 1, "union_type": 3, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.avsession.d.ts", "file_size": 276572, "success": true, "symbols_count": 3258, "parse_time": 0.03265547752380371, "symbol_types": {"namespace": 1, "interface": 39, "decorator": 3213, "callback_type": 1, "union_type": 3, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.webworker.d.ts", "file_size": 272256, "success": true, "symbols_count": 119, "parse_time": 0.014339447021484375, "symbol_types": {"namespace": 1, "interface": 14, "decorator": 14, "union_type": 90}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.media.d.ts", "file_size": 270515, "success": true, "symbols_count": 3457, "parse_time": 0.03581523895263672, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3419, "callback_type": 5, "union_type": 4, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.media.d.ts", "file_size": 270515, "success": true, "symbols_count": 3457, "parse_time": 0.030193328857421875, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3419, "callback_type": 5, "union_type": 4, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.web.webview.d.ts", "file_size": 267704, "success": true, "symbols_count": 3160, "parse_time": 0.04367494583129883, "symbol_types": {"namespace": 1, "class": 25, "interface": 31, "decorator": 3100, "callback_type": 1, "union_type": 1, "export_assignment": 1}}], "errors": []}, "indexing_results": {"total_files": 30, "successful_files": 30, "failed_files": 0, "total_symbols_indexed": 105303, "file_results": [{"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\lib.dom.d.ts", "file_size": 842681, "success": true, "symbols_indexed": 681, "index_time": 28.66329526901245}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.dom.d.ts", "file_size": 816786, "success": true, "symbols_indexed": 777, "index_time": 35.49708580970764}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\tsserverlibrary.d.ts", "file_size": 747710, "success": true, "symbols_indexed": 2489, "index_time": 109.65002369880676}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\@babel\\types\\lib\\index.d.ts", "file_size": 611184, "success": true, "symbols_indexed": 75, "index_time": 3.5481388568878174}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\typescript.d.ts", "file_size": 598789, "success": true, "symbols_indexed": 2174, "index_time": 106.63971877098083}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\typescriptServices.d.ts", "file_size": 598776, "success": true, "symbols_indexed": 2173, "index_time": 100.34253668785095}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\tsserverlibrary.d.ts", "file_size": 587234, "success": true, "symbols_indexed": 2105, "index_time": 97.23059320449829}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\declarations\\common.d.ts", "file_size": 566649, "success": true, "symbols_indexed": 11561, "index_time": 520.6094706058502}, {"file_path": "../Information/default/openharmony/ets/component\\common.d.ts", "file_size": 566343, "success": true, "symbols_indexed": 11561, "index_time": 527.2185859680176}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\typescript.d.ts", "file_size": 447512, "success": true, "symbols_indexed": 1806, "index_time": 81.88684320449829}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\typescriptServices.d.ts", "file_size": 447499, "success": true, "symbols_indexed": 1805, "index_time": 89.14055967330933}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.file.fs.d.ts", "file_size": 372692, "success": true, "symbols_indexed": 6311, "index_time": 280.1784682273865}, {"file_path": "../comprehensive_dataset\\storage\\@ohos.file.fs.d.ts", "file_size": 372692, "success": true, "symbols_indexed": 6311, "index_time": 281.7930681705475}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.data.relationalStore.d.ts", "file_size": 363266, "success": true, "symbols_indexed": 3945, "index_time": 188.81295561790466}, {"file_path": "../comprehensive_dataset\\storage\\@ohos.data.relationalStore.d.ts", "file_size": 363266, "success": true, "symbols_indexed": 3946, "index_time": 178.46932458877563}, {"file_path": "../Information/default/hms/ets/api\\@hms.health.store.d.ts", "file_size": 346063, "success": true, "symbols_indexed": 3639, "index_time": 156.17643475532532}, {"file_path": "../comprehensive_dataset\\hms\\@hms.health.store.d.ts", "file_size": 346063, "success": true, "symbols_indexed": 3639, "index_time": 165.61374378204346}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.security.cryptoFramework.d.ts", "file_size": 336615, "success": true, "symbols_indexed": 4570, "index_time": 204.42526030540466}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\webpack\\types.d.ts", "file_size": 335559, "success": true, "symbols_indexed": 562, "index_time": 27.046911478042603}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.audio.d.ts", "file_size": 325027, "success": true, "symbols_indexed": 3746, "index_time": 164.76535749435425}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.audio.d.ts", "file_size": 325027, "success": true, "symbols_indexed": 3746, "index_time": 169.8306052684784}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.image.d.ts", "file_size": 296616, "success": true, "symbols_indexed": 3818, "index_time": 171.67441391944885}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.image.d.ts", "file_size": 296616, "success": true, "symbols_indexed": 3818, "index_time": 169.91753578186035}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.window.d.ts", "file_size": 286762, "success": true, "symbols_indexed": 3336, "index_time": 154.66897249221802}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.avsession.d.ts", "file_size": 276572, "success": true, "symbols_indexed": 3258, "index_time": 132.31874918937683}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.avsession.d.ts", "file_size": 276572, "success": true, "symbols_indexed": 3258, "index_time": 147.80460500717163}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.webworker.d.ts", "file_size": 272256, "success": true, "symbols_indexed": 119, "index_time": 5.772449731826782}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.media.d.ts", "file_size": 270515, "success": true, "symbols_indexed": 3457, "index_time": 162.04493188858032}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.media.d.ts", "file_size": 270515, "success": true, "symbols_indexed": 3457, "index_time": 160.41827297210693}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.web.webview.d.ts", "file_size": 267704, "success": true, "symbols_indexed": 3160, "index_time": 147.73267817497253}], "errors": []}, "agent_test_results": {"total_queries": 20, "successful_queries": 0, "failed_queries": 20, "query_results": [{"query": "<PERSON><PERSON>", "query_type": "component", "success": false, "query_time": 60.10667848587036, "results_length": 45, "error": "No meaningful results or error in response"}, {"query": "Dialog", "query_type": "component", "success": false, "query_time": 60.057395935058594, "results_length": 45, "error": "No meaningful results or error in response"}, {"query": "Text", "query_type": "component", "success": false, "query_time": 60.060176610946655, "results_length": 43, "error": "No meaningful results or error in response"}, {"query": "Image", "query_type": "component", "success": false, "query_time": 60.041367292404175, "results_length": 44, "error": "No meaningful results or error in response"}, {"query": "List", "query_type": "component", "success": false, "query_time": 60.059547901153564, "results_length": 43, "error": "No meaningful results or error in response"}, {"query": "UIAbility", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "router", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "preferences", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "http", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "bluetooth", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.app.ability.UIAbility", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.router", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.data.preferences", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.net.http", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.bluetooth", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "file system", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "data storage", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "network request", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "device sensor", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "multimedia audio", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}], "errors": ["Error testing query 'UIAbility': 'Function' object is not callable", "Error testing query 'router': 'Function' object is not callable", "Error testing query 'preferences': 'Function' object is not callable", "Error testing query 'http': 'Function' object is not callable", "Error testing query 'bluetooth': 'Function' object is not callable", "Error testing query '@ohos.app.ability.UIAbility': 'Function' object is not callable", "Error testing query '@ohos.router': 'Function' object is not callable", "Error testing query '@ohos.data.preferences': 'Function' object is not callable", "Error testing query '@ohos.net.http': 'Function' object is not callable", "Error testing query '@ohos.bluetooth': 'Function' object is not callable", "Error testing query 'file system': 'Function' object is not callable", "Error testing query 'data storage': 'Function' object is not callable", "Error testing query 'network request': 'Function' object is not callable", "Error testing query 'device sensor': 'Function' object is not callable", "Error testing query 'multimedia audio': 'Function' object is not callable"]}, "search_test_results": [], "performance_metrics": {"total_test_time": 5073.379744052887, "files_processed": 30, "symbols_indexed": 105303, "files_per_second": 0.00591321791655091, "symbols_per_second": 20.75598620888535}, "errors": [], "warnings": []}