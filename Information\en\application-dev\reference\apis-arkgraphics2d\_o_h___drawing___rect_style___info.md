# OH_Drawing_RectStyle_Info


## Overview

The OH_Drawing_RectStyle_Info struct describes the style of a rectangle.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| uint32_t [color](#color) | Color of the rectangle. | 
| double [leftTopRadius](#lefttopradius) | Left top radius of the rectangle. | 
| double [rightTopRadius](#righttopradius) | Right top radius of the rectangle. | 
| double [rightBottomRadius](#rightbottomradius) | Right bottom radius of the rectangle. | 
| double [leftBottomRadius](#leftbottomradius) | Left bottom radius of the rectangle. | 


## Member Variable Description


### color

```
uint32_t OH_Drawing_RectStyle_Info::color
```
**Description**

Color of the rectangle.


### leftBottomRadius

```
double OH_Drawing_RectStyle_Info::leftBottomRadius
```
**Description**

Left bottom radius of the rectangle.


### leftTopRadius

```
double OH_Drawing_RectStyle_Info::leftTopRadius
```
**Description**

Left top radius of the rectangle.


### rightBottomRadius

```
double OH_Drawing_RectStyle_Info::rightBottomRadius
```
**Description**

Right bottom radius of the rectangle.


### rightTopRadius

```
double OH_Drawing_RectStyle_Info::rightTopRadius
```
**Description**

Right top radius of the rectangle.
