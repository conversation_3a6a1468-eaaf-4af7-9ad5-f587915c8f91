/*
 * Copyright (c) 2022-2023 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @file
 * @kit ArkUI
 */
/**
 * Defines the option in length unit of grid-row component.
 *
 * @interface GridRowSizeOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the option in length unit of grid-row component.
 *
 * @interface GridRowSizeOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the option in length unit of grid-row component.
 *
 * @interface GridRowSizeOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare interface GridRowSizeOption {
    /**
     * Grid Row Size Option xs, select xs gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Size Option xs, select xs gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Size Option xs, select xs gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    xs?: Length;
    /**
     * Grid Row Size Option sm, select sm gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Size Option sm, select sm gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Size Option sm, select sm gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    sm?: Length;
    /**
     * Grid Row Size Option md, select md gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Size Option md, select md gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Size Option md, select md gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    md?: Length;
    /**
     * Grid Row Size Option lg, select lg gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Size Option lg, select lg gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Size Option lg, select lg gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    lg?: Length;
    /**
     * Grid Row Size Option xl, select xl gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Size Option xl, select xl gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Size Option xl, select xl gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    xl?: Length;
    /**
     * Grid Row Size Option xxl, select xxl gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Size Option xxl, select xxl gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Size Option xxl, select xxl gutter size based on gridRow current breakpoint
     *
     * @type { ?Length }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    xxl?: Length;
}
/**
 * Defines the option in number unit of grid-container component.
 *
 * @interface GridRowColumnOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the option in number unit of grid-container component.
 *
 * @interface GridRowColumnOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the option in number unit of grid-container component.
 *
 * @interface GridRowColumnOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare interface GridRowColumnOption {
    /**
     * Grid Row Column Option xs, select xs column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Column Option xs, select xs column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Column Option xs, select xs column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    xs?: number;
    /**
     * Grid Row Column Option sm, select sm column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Column Option sm, select sm column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Column Option sm, select sm column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    sm?: number;
    /**
     * Grid Row Column Option md, select md column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Column Option md, select md column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Column Option md
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    md?: number;
    /**
     * Grid Row Column Option lg, select lg column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Column Option lg, select lg column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Column Option lg, select lg column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    lg?: number;
    /**
     * Grid Row Column Option xl, select xl column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Column Option xl, select xl column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Column Option xl, select xl column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    xl?: number;
    /**
     * Grid Row Column Option xxl, select xxl column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Grid Row Column Option xxl, select xxl column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Grid Row Column Option xxl, select xxl column num based on gridRow current breakpoint
     *
     * @type { ?number }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    xxl?: number;
}
/**
 * Defines the gutter of grid-row component.
 *
 * @interface GutterOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the gutter of grid-row component.
 *
 * @interface GutterOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the gutter of grid-row component.
 *
 * @interface GutterOption
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare interface GutterOption {
    /**
     * Define x in GutterOption
     *
     * @type { ?(Length | GridRowSizeOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Define x in GutterOption
     *
     * @type { ?(Length | GridRowSizeOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Define x in GutterOption
     *
     * @type { ?(Length | GridRowSizeOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    x?: Length | GridRowSizeOption;
    /**
     * Define y in GutterOption
     *
     * @type { ?(Length | GridRowSizeOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Define y in GutterOption
     *
     * @type { ?(Length | GridRowSizeOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Define y in GutterOption
     *
     * @type { ?(Length | GridRowSizeOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    y?: Length | GridRowSizeOption;
}
/**
 * Defines the breakpoint reference of grid-container component.
 *
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the breakpoint reference of grid-container component.
 *
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the breakpoint reference of grid-container component.
 *
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare enum BreakpointsReference {
    /**
     * Respond to breakpoint changes according to window width
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Respond to breakpoint changes according to window width
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Respond to breakpoint changes according to window width
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    WindowSize,
    /**
     * Respond to breakpoint changes according to component width
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Respond to breakpoint changes according to component width
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Respond to breakpoint changes according to component width
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    ComponentSize
}
/**
 * Defines the direction of grid-container component.
 *
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the direction of grid-container component.
 *
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the direction of grid-container component.
 *
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare enum GridRowDirection {
    /**
     * The elements in the grid component are arranged in rows
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * The elements in the grid component are arranged in rows
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * The elements in the grid component are arranged in rows
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    Row,
    /**
     * The elements in the grid component are arranged in reverse order of rows
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * The elements in the grid component are arranged in reverse order of rows
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * The elements in the grid component are arranged in reverse order of rows
     *
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    RowReverse
}
/**
 * Defines the breakpoints of grid-row component.
 *
 * @interface BreakPoints
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the breakpoints of grid-row component.
 *
 * @interface BreakPoints
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the breakpoints of grid-row component.
 *
 * @interface BreakPoints
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare interface BreakPoints {
    /**
     * Breakpoint array
     *
     * @type { ?Array<string> }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Breakpoint array
     *
     * @type { ?Array<string> }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Breakpoint array
     *
     * @type { ?Array<string> }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    value?: Array<string>;
    /**
     * Set breakpoint reference
     *
     * @type { ?BreakpointsReference }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Set breakpoint reference
     *
     * @type { ?BreakpointsReference }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Set breakpoint reference
     *
     * @type { ?BreakpointsReference }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    reference?: BreakpointsReference;
}
/**
 * Defines the options of grid-row component.
 *
 * @interface GridRowOptions
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the options of grid-row component.
 *
 * @interface GridRowOptions
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the options of grid-row component.
 *
 * @interface GridRowOptions
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare interface GridRowOptions {
    /**
     * layout spacing between sub-components
     *
     * @type { ?(Length | GutterOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * layout spacing between sub-components
     *
     * @type { ?(Length | GutterOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * layout spacing between sub-components
     *
     * @type { ?(Length | GutterOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    gutter?: Length | GutterOption;
    /**
     * Sets the total number of columns in the current layout.
     *
     * @type { ?(number | GridRowColumnOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Sets the total number of columns in the current layout.
     *
     * @type { ?(number | GridRowColumnOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Sets the total number of columns in the current layout.
     *
     * @type { ?(number | GridRowColumnOption) }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    columns?: number | GridRowColumnOption;
    /**
     * grid-row layout breakpoints.
     *
     * @type { ?BreakPoints }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * grid-row layout breakpoints.
     *
     * @type { ?BreakPoints }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * grid-row layout breakpoints.
     *
     * @type { ?BreakPoints }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    breakpoints?: BreakPoints;
    /**
     * grid-row layout direction.
     *
     * @type { ?GridRowDirection }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * grid-row layout direction.
     *
     * @type { ?GridRowDirection }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * grid-row layout direction.
     *
     * @type { ?GridRowDirection }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    direction?: GridRowDirection;
}
/**
 * Defines the the new version of grid-container component.
 *
 * @interface GridRowInterface
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the the new version of grid-container component.
 *
 * @interface GridRowInterface
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the the new version of grid-container component.
 *
 * @interface GridRowInterface
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
interface GridRowInterface {
    /**
     * Defines the constructor of GridRow.
     *
     * @param { GridRowOptions } option
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Defines the constructor of GridRow.
     *
     * @param { GridRowOptions } option
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Defines the constructor of GridRow.
     *
     * @param { GridRowOptions } option
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    (option?: GridRowOptions): GridRowAttribute;
}
/**
 * Defines the GridRow attribute functions.
 *
 * @extends CommonMethod<GridRowAttribute>
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines the GridRow attribute functions.
 *
 * @extends CommonMethod<GridRowAttribute>
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines the GridRow attribute functions.
 *
 * @extends CommonMethod<GridRowAttribute>
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare class GridRowAttribute extends CommonMethod<GridRowAttribute> {
    /**
     * Callback triggered when the breakpoint changes, breakpoints value can be xs, sm, md, lg, xl, xxl
     *
     * @param { function } callback
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @form
     * @since 9
     */
    /**
     * Callback triggered when the breakpoint changes, breakpoints value can be xs, sm, md, lg, xl, xxl
     *
     * @param { function } callback
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Callback triggered when the breakpoint changes, breakpoints value can be xs, sm, md, lg, xl, xxl
     *
     * @param { function } callback
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    onBreakpointChange(callback: (breakpoints: string) => void): GridRowAttribute;
    /**
     * Cross axis alignment of each line in GridRow.
     *
     * @param { ItemAlign } value - element alignment
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @since 10
     */
    /**
     * Cross axis alignment of each line in GridRow.
     *
     * @param { ItemAlign } value - element alignment
     * @returns { GridRowAttribute }
     * @syscap SystemCapability.ArkUI.ArkUI.Full
     * @crossplatform
     * @form
     * @atomicservice
     * @since 11
     */
    alignItems(value: ItemAlign): GridRowAttribute;
}
/**
 * Defines GridRow Component.
 *
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines GridRow Component.
 *
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines GridRow Component.
 *
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare const GridRow: GridRowInterface;
/**
 * Defines GridRow Component instance.
 *
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @form
 * @since 9
 */
/**
 * Defines GridRow Component instance.
 *
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @since 10
 */
/**
 * Defines GridRow Component instance.
 *
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @form
 * @atomicservice
 * @since 11
 */
declare const GridRowInstance: GridRowAttribute;
