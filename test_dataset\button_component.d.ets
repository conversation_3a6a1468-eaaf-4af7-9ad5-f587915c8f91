/**
 * Provides a button component.
 * @component Button
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
@Component
export struct Button {
  /**
   * Creates a button component.
   * @param options Button options.
   */
  constructor(options?: ButtonOptions);

  /**
   * Sets the button type.
   * @param type Button type.
   * @returns This Button component.
   */
  type(type: ButtonType): Button;

  /**
   * Sets the button label.
   * @param label Button label.
   * @returns This Button component.
   */
  label(label: string): Button;

  /**
   * Sets the button icon.
   * @param icon Button icon.
   * @returns This Button component.
   */
  icon(icon: string): But<PERSON>;

  /**
   * Sets whether the button is disabled.
   * @param disabled Whether the button is disabled.
   * @returns This Button component.
   */
  disabled(disabled: boolean): Button;

  /**
   * Sets the button click event handler.
   * @param callback Button click event handler.
   * @returns This Button component.
   */
  onClick(callback: () => void): But<PERSON>;
}

/**
 * Defines the button options.
 * @interface ButtonOptions
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
interface ButtonOptions {
  /**
   * Button type.
   */
  type?: ButtonType;

  /**
   * Button label.
   */
  label?: string;

  /**
   * Button icon.
   */
  icon?: string;

  /**
   * Whether the button is disabled.
   */
  disabled?: boolean;

  /**
   * Button click event handler.
   */
  onClick?: () => void;
}

/**
 * Enum for button type.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum ButtonType {
  /**
   * Normal button.
   */
  Normal,

  /**
   * Capsule button.
   */
  Capsule,

  /**
   * Circle button.
   */
  Circle,

  /**
   * Text button.
   */
  Text
}

/**
 * Enum for button role.
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum ButtonRole {
  /**
   * Primary button.
   */
  Primary,

  /**
   * Secondary button.
   */
  Secondary,

  /**
   * Tertiary button.
   */
  Tertiary
}

export { Button, ButtonOptions, ButtonType, ButtonRole };
