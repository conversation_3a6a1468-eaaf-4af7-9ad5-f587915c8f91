# LLDB Debugger


## Introduction

Low Level Debugger (LLDB) is a next-generation, high-performance debugger. For details, visit the [LLDB official website](https://lldb.llvm.org/).

LLDB used in OpenHarmony is developed on [LLVM 15.0.4](https://github.com/llvm/llvm-project/releases/tag/llvmorg-15.0.4). It is the default debugger in DevEco Studio and supports debugging of C and C++ applications.


## How to Obtain

Obtain the OpenHarmony SDK. Then find LLDB in the **\ohos-sdk\[system]\native\llvm** directory of the SDK, where **system** can be **windows**, **linux**, or **darwin**.

For example, for Windows, **lldb.exe** is stored in **\ohos-sdk\windows\native\llvm\bin** after the SDK is decompressed.


## Functions

The following lists some functions supported by LLDB. For more functions and related commands, see [LLDB Usage Guide](https://gitee.com/openharmony/third_party_llvm-project/blob/master/lldb/README.md) and [LLDB official manual](https://lldb.llvm.org).

- Logging

- Breakpoint management

- Watchpoint management

- Expression processing

- Viewing variables

- Process/Thread management

- Assembly processing

- Obtaining source code information

- Signal processing

- Launching a process

- Attaching to a process


## When to Use

- Local debugging
  - Local debugging in the Linux x86_64 environment

     LLDB supports debugging of C and C++ applications in the Linux x86_64 environment.
  - Local debugging on the macOS desktop

     LLDB supports debugging of C and C++ applications on the macOS desktop (including macOS x86_64 and M1).

- Remote debugging
  - Remote debugging based on DevEco Studio

     LLDB supports remote debugging of native C++ applications by connecting to OpenHarmony devices or emulators from the Windows and macOS desktops based on DevEco Studio.
  - Remote debugging through direct connection

     LLDB supports remote debugging of C and C++ applications by directly connecting to OpenHarmony devices from Windows, macOS desktop, and Linux x86_64 environment.


## Local Debugging

> **NOTE**
>
> The local debugging procedure for Linux x86_64 is the same as that for macOS.

**Procedure**

- Using LLDB to start and debug an application

  The following walks you through on how to debug an executable file named **a.out** in the Linux x86_64 environment. The file contains debugging information and is generated by the Clang compiler, which is of the same version as LLDB.

  1. Obtain the executable file **a.out**.
  2. Run LLDB and specify the file to debug as **a.out**.

      ```lldb
      ./lldb a.out
      ```
  3. Set breakpoints at the **main** function in the code.

      ```lldb
      (lldb) b main
      ```
  4. Run the application, and it stops at the first breakpoint.

      ```lldb
      (lldb) run
      ```
  5. Continue to run the application.

      ```lldb
      (lldb) continue
      ```
  6. List all the breakpoints.

      ```lldb
      (lldb) breakpoint list
      ```
  7. Show the arguments and local variables of the current frame.

      ```lldb
      (lldb) frame variable
      ```
  8. Run debugging commands as required to continue debugging.
  9. Exit debugging.

      ```lldb
      (lldb) quit
      ```

- Using LLDB to debug a started application

  The following walks you through on how to debug an executable file named **a.out** in the macOS environment. The file contains user input and debugging information and is generated by the Clang compiler. 

  1. Start the application on Command Line Interface (CLI) 1. (The message "Please input a number of type int" is displayed.)

      ```shell
      ./a.out
      ```
  2. Run LLDB on CLI 2.

      ```shell
      ./lldb
      ```
  3. Attach to the application.

      ```lldb
      (lldb) process attach --name a.out
      ```
  4. Set a breakpoint in line 10 of **hello.cpp**.

      ```lldb
      (lldb) breakpoint set --file hello.cpp --line 10
      ```
  5. On CLI 1, enter a number of the int type.

      ```shell
      88
      ```
  6. Continue to run the application on CLI 2. The application stops at the breakpoint.

      ```lldb
      (lldb) continue
      ```
  7. Run debugging commands as required to continue debugging.
  8. Detach from the application.

      ```lldb
      (lldb) detach
      ```
  9. Exit debugging.

      ```lldb
      (lldb) quit
      ```

  > **NOTE**
  >
  > You can also perform step 4 in prior to step 3.


## Remote Debugging

> **NOTE**
> - During remote debugging, **lldb-server** and **lldb** must be used together.
> 
> - The remote debugging procedures for Windows, Linux x86_64, and macOS are the same.

**Procedure**

The following walks you through on how to remotely debug an executable file named **a.out** by connecting to an Arm-based OpenHarmony device (for example, RK3568 development board) from the Windows platform.

> **NOTE**
>
> In the command below, **/data/local/tmp** indicates the specified directory on the device.
>
> **8080** is the listening port, which can be customized.
>
> You must have the execute permission on the **lldb-server** and **a.out** files of the device.

1. Open CLI 1 and push **lldb-server** and **a.out** to the device. (**a.out** is generated when you compile **hello.cpp** using the Clang compiler.)

   ```shell
   hdc file send lldb-server /data/local/tmp
   hdc file send a.out /data/local/tmp
   ```

2. Run **lldb-server**.

   ```shell
   hdc shell ./data/local/tmp/lldb-server p --server --listen "*:8080"
   ```

3. Open CLI 2 and run the binary file **lldb**.

   ```shell
   ./lldb
   ```

4. Select and connect to the remote device on the LLDB CLI.

   ```lldb
   (lldb) platform select remote-ohos
   (lldb) platform connect connect://localhost:8080 
   ```

5. Specify the binary file **a.out** on the device to be debugged.

   ```lldb
   (lldb) target create /data/local/tmp/a.out
   ```

6. Set breakpoints at the **main** function in the code.

   ```lldb
   (lldb) b main
   ```

7. Start the application.

   ```lldb
   (lldb) run
   ```

8. Display source code for the current target process.

   ```lldb
   (lldb) source list
   ```

9. Run debugging commands as required to continue debugging.

10. Exit debugging.

    ```lldb
    (lldb) quit
    ```
