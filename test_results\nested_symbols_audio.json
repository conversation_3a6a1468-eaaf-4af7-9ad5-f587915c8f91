[{"symbol_name": "AudioR<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12", "import_statement": "import { audio.AudioRenderer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioRenderer", "score": 0.0}, {"symbol_name": "DeviceChangeAction", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes the device change type and device information. @typedef DeviceChangeAction @syscap SystemCapability.Multimedia.Audio.Device @crossplatform @since 12", "import_statement": "import { audio.DeviceChangeAction } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.DeviceChangeAction", "score": 0.0}, {"symbol_name": "CaptureFilterOptions", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describe playback capture filtering options @typedef CaptureFilterOptions @syscap SystemCapability.Multimedia.Audio.PlaybackCapture @since 10 @deprecated since 12 @useinstead OH_AVScreenCapture in native interface.", "import_statement": "import { audio.CaptureFilterOptions } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.CaptureFilterOptions", "score": 0.0}]