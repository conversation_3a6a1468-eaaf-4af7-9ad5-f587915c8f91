"""
Test Qdrant Compatibility Layer

This module tests the Qdrant compatibility layer to ensure it works correctly
with both the deprecated `search` method and the newer `query_points` method.
"""

import unittest
import os
import logging
from typing import List, Dict, Any
from qdrant_client import QdrantClient, AsyncQdrantClient
from qdrant_client.http import models

# Import the compatibility layer
from qdrant_compatibility import qdrant_compatibility, QdrantCompatibility

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TestQdrantCompatibility")


class TestQdrantCompatibility(unittest.TestCase):
    """Test the Qdrant compatibility layer."""

    def setUp(self):
        """Set up the test environment."""
        # Initialize Qdrant client
        self.qdrant_url = config.QDRANT_URL
        self.collection_name = config.COLLECTION_NAME
        self.client = QdrantClient(
            url=self.qdrant_url,
            prefer_grpc=False,
            timeout=60.0,
            check_compatibility=False
        )
        
        # Initialize async client
        self.async_client = AsyncQdrantClient(
            url=self.qdrant_url,
            prefer_grpc=False,
            timeout=60.0,
            check_compatibility=False
        )
        
        # Create a dummy vector for testing
        self.vector_size = config.VECTOR_SIZES.get(
            config.EMBEDDING_MODEL, 
            config.DEFAULT_VECTOR_SIZE
        )
        self.dummy_vector = [0.0] * self.vector_size

    def test_search_with_deprecated_api(self):
        """Test search with deprecated API."""
        # Set environment variable to use deprecated API
        os.environ["USE_QDRANT_NEW_API"] = "0"
        
        # Perform search
        results = qdrant_compatibility.search(
            client=self.client,
            collection_name=self.collection_name,
            query_vector=self.dummy_vector,
            limit=5
        )
        
        # Check results
        self.assertIsNotNone(results)
        self.assertIsInstance(results, list)
        logger.info(f"Found {len(results)} results with deprecated API")

    def test_search_with_new_api(self):
        """Test search with new API."""
        # Set environment variable to use new API
        os.environ["USE_QDRANT_NEW_API"] = "1"
        
        try:
            # Perform search
            results = qdrant_compatibility.search(
                client=self.client,
                collection_name=self.collection_name,
                query_vector=self.dummy_vector,
                limit=5
            )
            
            # Check results
            self.assertIsNotNone(results)
            self.assertIsInstance(results, list)
            logger.info(f"Found {len(results)} results with new API")
        except Exception as e:
            logger.warning(f"New API test failed: {str(e)}")
            logger.warning("This is expected if the server version doesn't support query_points")
            # Reset environment variable
            os.environ["USE_QDRANT_NEW_API"] = "0"
            
    def test_search_with_filter(self):
        """Test search with filter."""
        # Set environment variable to use deprecated API
        os.environ["USE_QDRANT_NEW_API"] = "0"
        
        # Create filter
        filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="symbol_type",
                    match=models.MatchValue(value="component")
                )
            ]
        )
        
        # Perform search
        results = qdrant_compatibility.search(
            client=self.client,
            collection_name=self.collection_name,
            query_vector=self.dummy_vector,
            query_filter=filter,
            limit=5
        )
        
        # Check results
        self.assertIsNotNone(results)
        self.assertIsInstance(results, list)
        logger.info(f"Found {len(results)} results with filter")
        
        # Check that all results are components
        for result in results:
            self.assertEqual(result.payload.get("symbol_type"), "component")
            
    async def async_test_search(self):
        """Test async search."""
        # Set environment variable to use deprecated API
        os.environ["USE_QDRANT_NEW_API"] = "0"
        
        # Perform async search
        results = await qdrant_compatibility.search_async(
            client=self.async_client,
            collection_name=self.collection_name,
            query_vector=self.dummy_vector,
            limit=5
        )
        
        # Check results
        self.assertIsNotNone(results)
        self.assertIsInstance(results, list)
        logger.info(f"Found {len(results)} results with async search")
        
        return results
        
    def test_async_search(self):
        """Test async search using asyncio."""
        import asyncio
        
        # Run async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            results = loop.run_until_complete(self.async_test_search())
            self.assertIsNotNone(results)
            self.assertIsInstance(results, list)
        finally:
            loop.close()


if __name__ == "__main__":
    unittest.main()
