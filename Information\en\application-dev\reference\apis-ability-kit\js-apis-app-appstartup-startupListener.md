# @ohos.app.appstartup.StartupListener


The StartupListener module provides APIs to listen for startup tasks.

> **NOTE**
>
> The initial APIs of this module are supported since API version 12. Newly added APIs will be marked with a superscript to indicate their earliest API version.
>
> The APIs of this module can be used only in the stage model.

## Modules to Import

```ts
import { StartupListener } from '@kit.AbilityKit';
```

## StartupListener.onCompleted

onCompleted?(error: BusinessError\<void\>): void

Called when all startup tasks are complete.

**System capability**: SystemCapability.Ability.AppStartup

**Parameters**

| Name | Type | Mandatory | Description |
| -------- | -------- | -------- | -------- |
| error | [BusinessError](../apis-basic-services-kit/js-apis-base.md#businesserror) | Yes | Error message. |

**Example**

```ts
import { StartupConfig, StartupConfigEntry, StartupListener } from '@kit.AbilityKit';
import { BusinessError } from '@kit.BasicServicesKit';
import { hilog } from '@kit.PerformanceAnalysisKit';

export default class MyStartupConfigEntry extends StartupConfigEntry {
  onConfig() {
    hilog.info(0x0000, 'testTag', `onConfig`);
    let onCompletedCallback = (error: BusinessError<void>) => {
      hilog.info(0x0000, 'testTag', `onCompletedCallback`);
      if (error) {
        hilog.info(0x0000, 'testTag', 'onCompletedCallback: %{public}d, message: %{public}s', error.code, error.message);
      } else {
        hilog.info(0x0000, 'testTag', `onCompletedCallback: success.`);
      }
    }
    let startupListener: StartupListener = {
      'onCompleted': onCompletedCallback
    }
    let config: StartupConfig = {
      'timeoutMs': 10000,
      'startupListener': startupListener
    }
    return config;
  }
}
```
