#!/usr/bin/env python3
"""
Real World ArkTS Files Comprehensive Test
This test validates parsing capabilities against 20+ large, complex real-world ArkTS files.
"""

import os
import sys
import logging
import time
from typing import List, Dict, Any

# Add current directory to path to import component_cards
sys.path.insert(0, os.getcwd())

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_large_test_files() -> List[str]:
    """Get list of large, complex ArkTS files for testing."""
    
    # Large and complex files from Information/default/
    test_files = [
        # TypeScript library files (very large and complex)
        "Information/default/openharmony/ets/build-tools/ets-loader/node_modules/typescript/lib/lib.dom.d.ts",
        "Information/default/openharmony/ets/build-tools/ets-loader/node_modules/typescript/lib/tsserverlibrary.d.ts",
        "Information/default/openharmony/ets/build-tools/ets-loader/node_modules/typescript/lib/typescript.d.ts",
        "Information/default/openharmony/ets/build-tools/ets-loader/node_modules/typescript/lib/typescriptServices.d.ts",
        
        # Babel types (complex type definitions)
        "Information/default/openharmony/ets/build-tools/ets-loader/node_modules/@babel/types/lib/index.d.ts",
        
        # ArkTS common declarations (large component files)
        "Information/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts",
        "Information/default/openharmony/ets/component/common.d.ts",
        
        # OpenHarmony API files (complex system APIs)
        "Information/default/openharmony/ets/api/@ohos.file.fs.d.ts",
        "Information/default/openharmony/ets/api/@ohos.data.relationalStore.d.ts",
        "Information/default/openharmony/ets/api/@ohos.security.cryptoFramework.d.ts",
        "Information/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts",
        "Information/default/openharmony/ets/api/@system.app.d.ts",
        "Information/default/openharmony/ets/api/@system.file.d.ts",
        
        # HMS API files
        "Information/default/hms/ets/api/@hms.health.store.d.ts",
        
        # Webpack types (build tool types)
        "Information/default/openharmony/ets/build-tools/ets-loader/node_modules/webpack/types.d.ts",
        
        # Component files
        "Information/default/openharmony/ets/component/button.d.ts",
        "Information/default/openharmony/ets/component/text.d.ts",
        "Information/default/openharmony/ets/component/image.d.ts",
        "Information/default/openharmony/ets/component/list.d.ts",
        "Information/default/openharmony/ets/component/grid.d.ts",
        
        # Advanced ArkUI components (.d.ets files)
        "Information/default/openharmony/ets/api/@ohos.arkui.advanced.Dialog.d.ets",
        
        # Index files (reference many other files)
        "Information/default/openharmony/ets/component/index-full.d.ts",
        
        # Additional complex files
        "comprehensive_dataset/custom/button_component.d.ets",
        "comprehensive_dataset/custom/dialog_component.d.ets"
    ]
    
    # Filter to only existing files
    existing_files = []
    for file_path in test_files:
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            existing_files.append((file_path, file_size))
            logger.info(f"✅ Found: {file_path} ({file_size:,} bytes)")
        else:
            logger.warning(f"⚠️ Not found: {file_path}")
    
    # Sort by size (largest first) and return paths
    existing_files.sort(key=lambda x: x[1], reverse=True)
    return [file_path for file_path, _ in existing_files]

def test_file_parsing_performance(parser, file_path: str) -> Dict[str, Any]:
    """Test parsing performance and results for a single file."""
    
    try:
        file_size = os.path.getsize(file_path)
        logger.info(f"\n📁 Testing: {os.path.basename(file_path)} ({file_size:,} bytes)")
        
        # Measure parsing time
        start_time = time.time()
        symbols = parser.parse_file(file_path)
        parse_time = time.time() - start_time
        
        # Categorize symbols
        categories = {}
        for symbol in symbols:
            symbol_type = symbol.get('symbol_type', 'unknown')
            if symbol_type not in categories:
                categories[symbol_type] = []
            categories[symbol_type].append(symbol)
        
        # Calculate statistics
        total_symbols = len(symbols)
        symbols_per_second = total_symbols / parse_time if parse_time > 0 else 0
        bytes_per_second = file_size / parse_time if parse_time > 0 else 0
        
        result = {
            'file_path': file_path,
            'file_size': file_size,
            'total_symbols': total_symbols,
            'categories': {cat: len(items) for cat, items in categories.items()},
            'parse_time': parse_time,
            'symbols_per_second': symbols_per_second,
            'bytes_per_second': bytes_per_second,
            'success': True,
            'symbols': symbols
        }
        
        # Log results
        logger.info(f"  ✅ Parsed {total_symbols:,} symbols in {parse_time:.3f}s")
        logger.info(f"  📊 Performance: {symbols_per_second:.1f} symbols/s, {bytes_per_second/1024:.1f} KB/s")
        logger.info(f"  📋 Categories: {dict(list(result['categories'].items())[:5])}")
        if len(result['categories']) > 5:
            logger.info(f"     ... and {len(result['categories']) - 5} more categories")
        
        return result
        
    except Exception as e:
        logger.error(f"  ❌ Error parsing {file_path}: {str(e)}")
        return {
            'file_path': file_path,
            'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            'total_symbols': 0,
            'categories': {},
            'parse_time': 0,
            'symbols_per_second': 0,
            'bytes_per_second': 0,
            'success': False,
            'error': str(e)
        }

def analyze_symbol_distribution(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze symbol distribution across all files."""
    
    total_symbols = 0
    total_files = 0
    total_size = 0
    total_time = 0
    
    all_categories = {}
    file_type_stats = {}
    
    for result in results:
        if result['success']:
            total_symbols += result['total_symbols']
            total_files += 1
            total_size += result['file_size']
            total_time += result['parse_time']
            
            # Aggregate categories
            for category, count in result['categories'].items():
                if category not in all_categories:
                    all_categories[category] = 0
                all_categories[category] += count
            
            # File type statistics
            file_ext = '.d.ets' if result['file_path'].endswith('.d.ets') else '.d.ts'
            if file_ext not in file_type_stats:
                file_type_stats[file_ext] = {'files': 0, 'symbols': 0, 'size': 0}
            file_type_stats[file_ext]['files'] += 1
            file_type_stats[file_ext]['symbols'] += result['total_symbols']
            file_type_stats[file_ext]['size'] += result['file_size']
    
    return {
        'total_symbols': total_symbols,
        'total_files': total_files,
        'total_size': total_size,
        'total_time': total_time,
        'avg_symbols_per_file': total_symbols / total_files if total_files > 0 else 0,
        'avg_parse_time': total_time / total_files if total_files > 0 else 0,
        'overall_performance': total_symbols / total_time if total_time > 0 else 0,
        'overall_throughput': total_size / total_time if total_time > 0 else 0,
        'categories': all_categories,
        'file_type_stats': file_type_stats
    }

def test_specific_features(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Test specific ArkTS features across all files."""
    
    feature_stats = {
        'components_found': 0,
        'decorators_found': 0,
        'union_types_found': 0,
        'intersection_types_found': 0,
        'callback_types_found': 0,
        'nested_symbols_found': 0,
        'generic_types_found': 0,
        'reexports_found': 0,
        'files_with_components': 0,
        'files_with_decorators': 0,
        'largest_file_symbols': 0,
        'most_complex_file': '',
        'fastest_parse_rate': 0,
        'slowest_parse_rate': float('inf')
    }
    
    for result in results:
        if not result['success']:
            continue
            
        categories = result['categories']
        
        # Count specific features
        feature_stats['components_found'] += categories.get('component', 0)
        feature_stats['decorators_found'] += categories.get('decorator', 0)
        feature_stats['union_types_found'] += categories.get('union_type', 0)
        feature_stats['intersection_types_found'] += categories.get('intersection_type', 0)
        feature_stats['callback_types_found'] += categories.get('callback_type', 0)
        feature_stats['reexports_found'] += categories.get('reexport', 0) + categories.get('reexport_all', 0)
        
        # Count nested symbols
        nested_count = sum(1 for symbol in result['symbols'] if symbol.get('is_nested', False))
        feature_stats['nested_symbols_found'] += nested_count
        
        # Files with specific features
        if categories.get('component', 0) > 0:
            feature_stats['files_with_components'] += 1
        if categories.get('decorator', 0) > 0:
            feature_stats['files_with_decorators'] += 1
        
        # Performance metrics
        if result['total_symbols'] > feature_stats['largest_file_symbols']:
            feature_stats['largest_file_symbols'] = result['total_symbols']
            feature_stats['most_complex_file'] = os.path.basename(result['file_path'])
        
        if result['symbols_per_second'] > feature_stats['fastest_parse_rate']:
            feature_stats['fastest_parse_rate'] = result['symbols_per_second']
        
        if result['symbols_per_second'] < feature_stats['slowest_parse_rate']:
            feature_stats['slowest_parse_rate'] = result['symbols_per_second']
    
    return feature_stats

def main():
    """Run comprehensive real-world file testing."""
    logger.info("🚀 Starting Real-World ArkTS Files Comprehensive Test...")
    
    try:
        from component_cards import ArkTSSymbolParser
        
        # Initialize parser
        parser = ArkTSSymbolParser()
        
        # Get test files
        test_files = get_large_test_files()
        logger.info(f"\n📊 Found {len(test_files)} files to test")
        
        if len(test_files) < 20:
            logger.warning(f"⚠️ Only found {len(test_files)} files, expected at least 20")
        
        # Test each file
        results = []
        for i, file_path in enumerate(test_files, 1):
            logger.info(f"\n[{i}/{len(test_files)}] Testing file...")
            result = test_file_parsing_performance(parser, file_path)
            results.append(result)
        
        # Analyze results
        logger.info("\n" + "="*80)
        logger.info("📊 COMPREHENSIVE ANALYSIS RESULTS")
        logger.info("="*80)
        
        # Overall statistics
        analysis = analyze_symbol_distribution(results)
        logger.info(f"✅ Successfully parsed: {analysis['total_files']}/{len(test_files)} files")
        logger.info(f"📁 Total file size: {analysis['total_size']/1024/1024:.1f} MB")
        logger.info(f"🔍 Total symbols extracted: {analysis['total_symbols']:,}")
        logger.info(f"⏱️ Total parse time: {analysis['total_time']:.2f}s")
        logger.info(f"📈 Average symbols per file: {analysis['avg_symbols_per_file']:.0f}")
        logger.info(f"⚡ Overall performance: {analysis['overall_performance']:.0f} symbols/s")
        logger.info(f"💾 Overall throughput: {analysis['overall_throughput']/1024/1024:.1f} MB/s")
        
        # Feature analysis
        features = test_specific_features(results)
        logger.info(f"\n🎯 ArkTS Feature Detection:")
        logger.info(f"  🧩 Components found: {features['components_found']} (in {features['files_with_components']} files)")
        logger.info(f"  🏷️ Decorators found: {features['decorators_found']} (in {features['files_with_decorators']} files)")
        logger.info(f"  🔗 Union types found: {features['union_types_found']}")
        logger.info(f"  ⚡ Intersection types found: {features['intersection_types_found']}")
        logger.info(f"  📞 Callback types found: {features['callback_types_found']}")
        logger.info(f"  🏗️ Nested symbols found: {features['nested_symbols_found']}")
        logger.info(f"  📤 Reexports found: {features['reexports_found']}")
        
        # Performance analysis
        logger.info(f"\n⚡ Performance Analysis:")
        logger.info(f"  🏆 Most complex file: {features['most_complex_file']} ({features['largest_file_symbols']:,} symbols)")
        logger.info(f"  🚀 Fastest parse rate: {features['fastest_parse_rate']:.0f} symbols/s")
        logger.info(f"  🐌 Slowest parse rate: {features['slowest_parse_rate']:.0f} symbols/s")
        
        # Category breakdown
        logger.info(f"\n📋 Symbol Categories (Top 10):")
        sorted_categories = sorted(analysis['categories'].items(), key=lambda x: x[1], reverse=True)
        for i, (category, count) in enumerate(sorted_categories[:10], 1):
            percentage = (count / analysis['total_symbols']) * 100
            logger.info(f"  {i:2d}. {category}: {count:,} ({percentage:.1f}%)")
        
        # File type comparison
        logger.info(f"\n📄 File Type Comparison:")
        for file_type, stats in analysis['file_type_stats'].items():
            avg_symbols = stats['symbols'] / stats['files'] if stats['files'] > 0 else 0
            avg_size = stats['size'] / stats['files'] / 1024 if stats['files'] > 0 else 0
            logger.info(f"  {file_type}: {stats['files']} files, {stats['symbols']:,} symbols, avg {avg_symbols:.0f} symbols/file, avg {avg_size:.0f} KB/file")
        
        # Success rate
        successful_files = sum(1 for r in results if r['success'])
        success_rate = (successful_files / len(results)) * 100 if results else 0
        
        logger.info(f"\n🎉 Final Assessment:")
        logger.info(f"  ✅ Success rate: {success_rate:.1f}% ({successful_files}/{len(results)} files)")
        
        if success_rate >= 95:
            logger.info("  🏆 EXCELLENT: System handles real-world files exceptionally well!")
        elif success_rate >= 85:
            logger.info("  ✅ GOOD: System handles most real-world files successfully")
        elif success_rate >= 70:
            logger.info("  ⚠️ MODERATE: System needs improvements for better real-world compatibility")
        else:
            logger.error("  ❌ POOR: System struggles with real-world files")
        
        # Save detailed results
        results_file = "test_results/Test_RealWorld_Results/detailed_results.txt"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            f.write("Real-World ArkTS Files Test Results\n")
            f.write("="*50 + "\n\n")
            
            for result in results:
                f.write(f"File: {result['file_path']}\n")
                f.write(f"Size: {result['file_size']:,} bytes\n")
                f.write(f"Success: {result['success']}\n")
                if result['success']:
                    f.write(f"Symbols: {result['total_symbols']:,}\n")
                    f.write(f"Parse time: {result['parse_time']:.3f}s\n")
                    f.write(f"Categories: {result['categories']}\n")
                else:
                    f.write(f"Error: {result.get('error', 'Unknown error')}\n")
                f.write("\n" + "-"*50 + "\n\n")
        
        logger.info(f"\n📄 Detailed results saved to: {results_file}")
        
        return {
            'success_rate': success_rate,
            'total_symbols': analysis['total_symbols'],
            'total_files': len(results),
            'performance': analysis['overall_performance'],
            'features': features
        }
        
    except Exception as e:
        logger.error(f"❌ Critical error in real-world testing: {str(e)}")
        return {'success_rate': 0, 'error': str(e)}

if __name__ == "__main__":
    main()
