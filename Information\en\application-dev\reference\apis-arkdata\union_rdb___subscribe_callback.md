# Rdb_SubscribeCallback


## Overview

Defines the callback used to return the subscribed event.

**Since**: 11

**Related module**: [RDB](_r_d_b.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| [detailsObserver](_r_d_b.md#detailsobserver) | Callback used to return the details about the device-cloud data change.| 
| [briefObserver](_r_d_b.md#briefobserver) | Callback used to return the device-cloud data change event.| 
