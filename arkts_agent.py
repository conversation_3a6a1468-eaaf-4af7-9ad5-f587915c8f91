#!/usr/bin/env python3
"""
ArkTS Import Agent
A simple Agno agent that uses ArkTS import tools to help developers find import statements.
"""

import logging
from typing import Optional

# Import configuration
import config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from agno.agent import Agent
    from agno.models.ollama import Ollama
    from agno.tools import tool
    AGNO_AVAILABLE = True
    logger.info("✅ Agno framework loaded successfully")
except ImportError as e:
    logger.error(f"❌ Agno framework not available: {e}")
    AGNO_AVAILABLE = False

# Import our ArkTS query engine (using sync version)
try:
    from arkts_query import ArkTSQuery
    from arkts_formatter import ArkTSFormatter
    logger.info("✅ ArkTS query engine imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import ArkTS query engine: {e}")
    ArkTSQuery = None
    ArkTSFormatter = None

# Global instances for tools
_query_engine = None
_formatter = None

def _initialize_global_tools():
    """Initialize global query engine and formatter."""
    global _query_engine, _formatter
    if _query_engine is None:
        try:
            _query_engine = ArkTSQuery(
                qdrant_url=config.QDRANT_URL,
                collection_name=config.COLLECTION_NAME,
                ollama_url=config.OLLAMA_URL,
                embedding_model=config.EMBEDDING_MODEL
            )
            _formatter = ArkTSFormatter()
            logger.info("✅ Global ArkTS tools initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize global tools: {e}")
            _query_engine = None
            _formatter = None


# ArkTS Tools using @tool decorator

@tool(
    name="search_arkts_component",
    description="Search for ArkTS UI components like Button, Text, Image, List, Grid, etc. Use this when user asks about UI components.",
    show_result=True
)
def search_arkts_component(component_name: str) -> str:
    """Search for ArkTS UI components.

    Args:
        component_name: Name of the component to search for (e.g., "Button", "Text", "Image")

    Returns:
        Formatted search results with import statements
    """
    _initialize_global_tools()

    if not _query_engine:
        return "❌ Component search engine not available"

    try:
        logger.info(f"🔍 Searching for component: {component_name}")

        # Use sync search method
        results = _query_engine.search_component(component_name, limit=5)

        if not results:
            return f"No components found for '{component_name}'"

        # Format results
        formatted = _formatter.format_results_for_agent(results, "component")
        logger.info(f"✅ Found {len(results)} components for '{component_name}'")
        return formatted

    except Exception as e:
        error_msg = f"❌ Error searching for component '{component_name}': {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool(
    name="search_import_path",
    description="Find import paths for ArkTS symbols and modules. Use this when user asks about import statements or module paths.",
    show_result=True
)
def search_import_path(symbol_name: str) -> str:
    """Search for import paths for ArkTS symbols.

    Args:
        symbol_name: Name of the symbol to find import paths for (e.g., "router", "http", "camera")

    Returns:
        Formatted search results with import paths
    """
    _initialize_global_tools()

    if not _query_engine:
        return "❌ Import path search engine not available"

    try:
        logger.info(f"🔍 Searching for import path: {symbol_name}")

        # Use sync search method
        results = _query_engine.search_import_path(symbol_name, limit=5)

        if not results:
            return f"No import paths found for '{symbol_name}'"

        # Format results
        formatted = _formatter.format_results_for_agent(results, "import_path")
        logger.info(f"✅ Found {len(results)} import paths for '{symbol_name}'")
        return formatted

    except Exception as e:
        error_msg = f"❌ Error searching for import path '{symbol_name}': {str(e)}"
        logger.error(error_msg)
        return error_msg


@tool(
    name="search_arkts_api",
    description="Search for ArkTS system APIs like Router, HTTP, Camera, Bluetooth, etc. Use this when user asks about system APIs or services.",
    show_result=True
)
def search_arkts_api(api_name: str) -> str:
    """Search for ArkTS system APIs.

    Args:
        api_name: Name of the API to search for (e.g., "router", "http", "camera", "bluetooth")

    Returns:
        Formatted search results with API information and import statements
    """
    _initialize_global_tools()

    if not _query_engine:
        return "❌ API search engine not available"

    try:
        logger.info(f"🔍 Searching for API: {api_name}")

        # Use sync search method
        results = _query_engine.suggest_imports(api_name, limit=5)

        if not results:
            return f"No APIs found for '{api_name}'"

        # Format results
        formatted = _formatter.format_results_for_agent(results, "general")
        logger.info(f"✅ Found {len(results)} APIs for '{api_name}'")
        return formatted

    except Exception as e:
        error_msg = f"❌ Error searching for API '{api_name}': {str(e)}"
        logger.error(error_msg)
        return error_msg


class ArkTSImportAgent:
    """ArkTS Import Agent using Agno framework."""

    def __init__(self, model_name: Optional[str] = None):
        """Initialize the ArkTS Import Agent.

        Args:
            model_name: Name of the Ollama model to use (defaults to config)
        """
        self.model_name = model_name or config.AGENT_MODEL
        self.ollama_url = config.OLLAMA_URL
        self.agent = None

        # Initialize ArkTS tools first (this sets the global instance)
        self._initialize_tools()

        # Initialize the agent
        self._initialize_agent()

    def _initialize_tools(self):
        """Initialize ArkTS tools."""
        try:
            # Initialize global tools
            _initialize_global_tools()
            logger.info("✅ ArkTS tools initialized successfully")

        except Exception as e:
            logger.error(f"❌ Failed to initialize ArkTS tools: {e}")

    def _initialize_agent(self):
        """Initialize the Agno agent."""
        if not AGNO_AVAILABLE:
            logger.error("❌ Cannot initialize agent: Agno framework not available")
            return

        try:
            # Create Ollama model
            model = Ollama(
                id=self.model_name,
                host=self.ollama_url,
                options={
                    "num_predict": config.AGENT_MAX_TOKENS,
                    "temperature": config.AGENT_TEMPERATURE
                }
            )

            # Create agent with tools (using function references)
            tools = [
                search_arkts_component,
                search_import_path,
                search_arkts_api
            ]

            self.agent = Agent(
                name=config.AGENT_NAME,
                model=model,
                tools=tools,
                description=config.AGENT_DESCRIPTION,
                instructions=[
                    "You are an expert ArkTS developer assistant.",
                    "ALWAYS use the available tools to search for accurate import information.",
                    "Do NOT rely on your training data - use tools to get current information.",
                    "Use search_arkts_component for UI components like Button, Text, Image.",
                    "Use search_import_path for finding import paths for symbols.",
                    "Use search_arkts_api for system APIs like Router, HTTP, Camera.",
                    "Always provide clear, accurate import statements based on tool results.",
                    "Format your responses in a helpful and readable way.",
                    "If tools don't find results, suggest alternatives or ask for clarification."
                ],
                show_tool_calls=True,
                markdown=True
            )

            logger.info(f"✅ Agent initialized with model {self.model_name}")
            logger.info(f"🔧 Available tools: {len(tools)}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize agent: {e}")
            self.agent = None

    def chat(self, message: str) -> str:
        """Chat with the agent.

        Args:
            message: User message

        Returns:
            Agent response
        """
        if not self.agent:
            return "❌ Agent not available. Please check the logs for initialization errors."

        try:
            logger.info(f"🗣️ User: {message}")
            response = self.agent.run(message)
            logger.info(f"🤖 Agent: {response.content}")
            return response.content

        except Exception as e:
            error_msg = f"❌ Error during chat: {e}"
            logger.error(error_msg)
            return error_msg

    def test_tools(self):
        """Test all available tools."""
        logger.info("🧪 Testing ArkTS tools...")

        test_queries = [
            "Find the Button component",
            "How do I import Text component?",
            "Search for router API",
            "What's the import for http module?"
        ]

        for query in test_queries:
            logger.info(f"\n📝 Testing: {query}")
            response = self.chat(query)
            print(f"\nQuery: {query}")
            print(f"Response: {response}")
            print("-" * 80)


def main():
    """Main function to run the agent."""
    print("🚀 Starting ArkTS Import Agent...")

    # Initialize agent
    agent = ArkTSImportAgent()

    if not agent.agent:
        print("❌ Failed to initialize agent. Check logs for details.")
        return

    print(f"✅ Agent ready! Using model: {agent.model_name}")
    print("💡 Type 'test' to run tool tests, 'quit' to exit, or ask about ArkTS imports.")

    while True:
        try:
            user_input = input("\n🗣️ You: ").strip()

            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            elif user_input.lower() == 'test':
                agent.test_tools()
            elif user_input:
                response = agent.chat(user_input)
                print(f"\n🤖 Agent: {response}")

        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


if __name__ == "__main__":
    main()
