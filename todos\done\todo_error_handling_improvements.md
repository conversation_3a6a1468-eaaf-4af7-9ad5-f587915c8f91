# Hata İşleme ve Güvenilirlik İyileştirmeleri Planı

## Mevcut Durum Analizi
- Hata işleme mekanizmaları yetersiz
- Qdrant veya Ollama sunucularına bağlanırken oluşabilecek hatalar yeterince yönetilmiyor
- Hata mesajları yeterince açıklayıcı değil
- Sistem çökmelerine karşı yeterli koruma yok

## İyileştirme Adımları

### 1. Genel Hata İşleme Stratejisi ✅ COMPLETED
- [x] Tüm modüller için tutarlı bir hata işleme stratejisi oluştur ✅
  - [x] Hata türlerini tanımla (bağlantı hataları, zaman aşımı hataları, vb.) ✅
  - [x] Hata işleme akışını tanımla ✅
  - [x] Hata raporlama mekanizmasını tanımla ✅

### 2. Bağlantı Hatalarının Yönetimi ✅ COMPLETED
- [x] Qdrant sunucusuna bağlantı hatalarını yönet ✅
  - [x] Bağlantı hatası durumunda yeniden deneme mekanizması ekle ✅
  - [x] Bağlantı hatası durumunda alternatif sunuculara geçiş yap ✅
  - [x] Bağlantı hatası durumunda kullanıcıya anlamlı hata mesajları göster ✅
- [x] Ollama sunucusuna bağlantı hatalarını yönet ✅
  - [x] Bağlantı hatası durumunda yeniden deneme mekanizması ekle ✅
  - [x] Bağlantı hatası durumunda alternatif sunuculara geçiş yap ✅
  - [x] Bağlantı hatası durumunda kullanıcıya anlamlı hata mesajları göster ✅

### 3. Zaman Aşımı Hatalarının Yönetimi ✅ COMPLETED
- [x] Qdrant sorguları için zaman aşımı yönetimi ekle ✅
  - [x] Zaman aşımı sürelerini yapılandırılabilir hale getir ✅
  - [x] Zaman aşımı durumunda yeniden deneme mekanizması ekle ✅
  - [x] Zaman aşımı durumunda kullanıcıya anlamlı hata mesajları göster ✅
- [x] Ollama sorguları için zaman aşımı yönetimi ekle ✅
  - [x] Zaman aşımı sürelerini yapılandırılabilir hale getir ✅
  - [x] Zaman aşımı durumunda yeniden deneme mekanizması ekle ✅
  - [x] Zaman aşımı durumunda kullanıcıya anlamlı hata mesajları göster ✅

### 4. Veri Doğrulama ve Temizleme ✅ COMPLETED
- [x] Giriş verilerini doğrula ve temizle ✅
  - [x] Sorgu parametrelerini doğrula ✅
  - [x] Dosya yollarını doğrula ✅
  - [x] Yapılandırma parametrelerini doğrula ✅
- [x] Çıkış verilerini doğrula ve temizle ✅
  - [x] Sorgu sonuçlarını doğrula ✅
  - [x] Boş sonuçları yönet ✅
  - [x] Hatalı sonuçları yönet ✅

### 5. Hata Loglama ve İzleme ✅ COMPLETED
- [x] Hata loglama mekanizmasını iyileştir ✅
  - [x] Hata türlerine göre log seviyeleri belirle ✅
  - [x] Hata mesajlarını daha açıklayıcı hale getir ✅
  - [x] Hata izleme bilgilerini (stack trace) logla ✅
- [x] Hata izleme mekanizması ekle ✅
  - [x] Hata istatistiklerini topla ✅
  - [x] Hata eğilimlerini analiz et ✅
  - [x] Hata raporlarını oluştur ✅

### 6. Güvenilirlik İyileştirmeleri ✅ COMPLETED
- [x] Sistem çökmelerine karşı koruma ekle ✅
  - [x] Kritik bölümleri try/except blokları ile koru ✅
  - [x] Beklenmeyen hataları yakala ve yönet ✅
  - [x] Sistem durumunu düzenli olarak kaydet ✅
- [x] Otomatik kurtarma mekanizmaları ekle ✅
  - [x] Çökme durumunda otomatik olarak yeniden başlat ✅
  - [x] Son durumdan devam et ✅
  - [x] Veri kaybını önle ✅

### 7. Kullanıcı Geri Bildirimi ✅ COMPLETED
- [x] Kullanıcıya anlamlı hata mesajları göster ✅
  - [x] Hata mesajlarını kullanıcı dostu hale getir ✅
  - [x] Hata çözüm önerileri sun ✅
  - [x] Hata durumunda ne yapılacağını açıkla ✅
- [x] Kullanıcıdan hata raporları topla ✅
  - [x] Hata raporu gönderme mekanizması ekle ✅
  - [x] Kullanıcı geri bildirimlerini topla ✅
  - [x] Kullanıcı geri bildirimlerine göre sistemi iyileştir ✅

## Örnek Kod

```python
class ErrorHandler:
    """Handles errors in the ArkTS import suggestion system."""

    def __init__(self, max_retries=3, retry_delay=1):
        """Initialize the error handler.

        Args:
            max_retries: Maximum number of retries
            retry_delay: Delay between retries in seconds
        """
        self.max_retries = max_retries
        self.retry_delay = retry_delay

    def handle_connection_error(self, func, *args, **kwargs):
        """Handle connection errors with retry mechanism.

        Args:
            func: Function to call
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Result of the function

        Raises:
            ConnectionError: If all retries fail
        """
        for attempt in range(1, self.max_retries + 1):
            try:
                return func(*args, **kwargs)
            except (ConnectionError, httpx.ConnectError, httpx.ConnectTimeout) as e:
                logger.warning(f"Connection error (attempt {attempt}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    time.sleep(self.retry_delay * attempt)  # Exponential backoff
                else:
                    logger.error(f"Failed after {self.max_retries} attempts")
                    raise ConnectionError(f"Failed to connect after {self.max_retries} attempts: {str(e)}")

    def handle_timeout_error(self, func, timeout, *args, **kwargs):
        """Handle timeout errors with retry mechanism.

        Args:
            func: Function to call
            timeout: Timeout in seconds
            *args: Function arguments
            **kwargs: Function keyword arguments

        Returns:
            Result of the function

        Raises:
            TimeoutError: If all retries fail
        """
        for attempt in range(1, self.max_retries + 1):
            try:
                return func(*args, **kwargs, timeout=timeout)
            except (TimeoutError, httpx.ReadTimeout) as e:
                logger.warning(f"Timeout error (attempt {attempt}/{self.max_retries}): {str(e)}")
                if attempt < self.max_retries:
                    logger.info(f"Retrying in {self.retry_delay} seconds with increased timeout...")
                    time.sleep(self.retry_delay * attempt)  # Exponential backoff
                    timeout *= 1.5  # Increase timeout for next attempt
                else:
                    logger.error(f"Failed after {self.max_retries} attempts")
                    raise TimeoutError(f"Operation timed out after {self.max_retries} attempts: {str(e)}")

    def validate_query_params(self, query=None, component=None, symbol_type=None, parent_symbol=None, limit=None):
        """Validate query parameters.

        Args:
            query: Query string
            component: Component name
            symbol_type: Symbol type
            parent_symbol: Parent symbol
            limit: Result limit

        Returns:
            Validated parameters

        Raises:
            ValueError: If parameters are invalid
        """
        # Validate query
        if query is not None and not isinstance(query, str):
            raise ValueError(f"Query must be a string, got {type(query)}")

        # Validate component
        if component is not None and not isinstance(component, str):
            raise ValueError(f"Component must be a string, got {type(component)}")

        # Validate symbol_type
        valid_symbol_types = ['class', 'interface', 'enum', 'namespace', 'function', 'component']
        if symbol_type is not None and symbol_type not in valid_symbol_types:
            raise ValueError(f"Symbol type must be one of {valid_symbol_types}, got {symbol_type}")

        # Validate parent_symbol
        if parent_symbol is not None and not isinstance(parent_symbol, str):
            raise ValueError(f"Parent symbol must be a string, got {type(parent_symbol)}")

        # Validate limit
        if limit is not None:
            try:
                limit = int(limit)
                if limit <= 0:
                    raise ValueError(f"Limit must be a positive integer, got {limit}")
            except (ValueError, TypeError):
                raise ValueError(f"Limit must be a positive integer, got {limit}")

        return {
            'query': query,
            'component': component,
            'symbol_type': symbol_type,
            'parent_symbol': parent_symbol,
            'limit': limit
        }
```

## Beklenen Sonuçlar
- Hata işleme mekanizmaları iyileşecek
- Qdrant veya Ollama sunucularına bağlanırken oluşabilecek hatalar daha iyi yönetilecek
- Hata mesajları daha açıklayıcı olacak
- Sistem çökmelerine karşı daha iyi koruma sağlanacak
- Kullanıcıya daha anlamlı hata mesajları gösterilecek
- Sistem daha güvenilir hale gelecek
