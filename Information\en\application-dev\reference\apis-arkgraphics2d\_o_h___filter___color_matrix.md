# OH_Filter_ColorMatrix


## Overview

The OH_Filter_ColorMatrix struct describes a matrix used to create an effect filter.

**Since**: 12

**Related module**: [EffectKit](effect_kit.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| float [val](#val) [20] | Custom color matrix. The value is a 5 x 4 array. | 


## Member Variable Description


### val

```
float OH_Filter_ColorMatrix::val[20]
```

**Description**

Custom color matrix. The value is a 5 x 4 array.
