"""
Test Improvements

This module tests the improvements made to the ArkTS import suggestion system.
It includes tests for error handling, async operations, and performance optimization.
"""

import os
import sys
import time
import logging
import unittest
import asyncio
from typing import List, Dict, Any

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
from arkts_query import ArkTSQuery
from arkts_query_enhanced import ArkTSQueryEnhanced
from arkts_query_async import ArkTSQueryAsync
from arkts_agno_tools import ArkTSImportTools
from async_manager import AsyncManager
from error_handler import <PERSON>rror<PERSON>andler
from performance_optimizer import PerformanceOptimizer

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TestImprovements")


class TestErrorHandling(unittest.TestCase):
    """Test error handling improvements."""

    def setUp(self):
        """Set up test environment."""
        self.error_handler = ErrorHandler(max_retries=3, retry_delay=0.1)

    def test_validate_query_params(self):
        """Test query parameter validation."""
        # Valid parameters
        params = self.error_handler.validate_query_params(
            query="button",
            component=None,
            symbol_type="component",
            parent_symbol=None,
            limit=5
        )
        self.assertEqual(params['query'], "button")
        self.assertEqual(params['symbol_type'], "component")
        self.assertEqual(params['limit'], 5)

        # Invalid symbol_type
        with self.assertRaises(ValueError):
            self.error_handler.validate_query_params(
                query="button",
                symbol_type="invalid_type"
            )

        # Invalid limit
        with self.assertRaises(ValueError):
            self.error_handler.validate_query_params(
                query="button",
                limit=-1
            )

    def test_validate_results(self):
        """Test result validation."""
        # Valid results
        results = [
            {
                'symbol_name': 'Button',
                'symbol_type': 'component',
                'score': 0.8,
                'description': 'A button component'
            }
        ]
        validated = self.error_handler.validate_results(results)
        self.assertEqual(validated[0]['symbol_name'], 'Button')

        # Missing symbol_type
        results = [
            {
                'symbol_name': 'Button',
                'score': 0.8,
                'description': 'A button component'
            }
        ]
        validated = self.error_handler.validate_results(results)
        self.assertEqual(validated[0]['symbol_type'], 'unknown')

        # Missing score
        results = [
            {
                'symbol_name': 'Button',
                'symbol_type': 'component',
                'description': 'A button component'
            }
        ]
        validated = self.error_handler.validate_results(results)
        self.assertEqual(validated[0]['score'], 0.0)

        # Long description
        results = [
            {
                'symbol_name': 'Button',
                'symbol_type': 'component',
                'score': 0.8,
                'description': 'A' * 1500
            }
        ]
        validated = self.error_handler.validate_results(results)
        self.assertEqual(len(validated[0]['description']), 1000)


class TestAsyncOperations(unittest.TestCase):
    """Test async operations improvements."""

    def setUp(self):
        """Set up test environment."""
        self.async_manager = AsyncManager(default_timeout=5.0)

    def test_get_or_create_event_loop(self):
        """Test getting or creating an event loop."""
        loop = self.async_manager.get_or_create_event_loop()
        self.assertIsInstance(loop, asyncio.AbstractEventLoop)
        self.assertFalse(loop.is_closed())

    def test_run_coroutine(self):
        """Test running a coroutine."""
        async def test_coro():
            return "test"

        result = self.async_manager.run_coroutine(test_coro())
        self.assertEqual(result, "test")

    def test_run_in_new_loop(self):
        """Test running a coroutine in a new loop."""
        async def test_coro():
            return "test"

        result = self.async_manager.run_in_new_loop(test_coro())
        self.assertEqual(result, "test")

    def test_with_retry(self):
        """Test running a coroutine with retry logic."""
        # Define a coroutine that fails twice then succeeds
        attempt = [0]

        async def failing_coro():
            attempt[0] += 1
            if attempt[0] < 3:
                raise ValueError(f"Attempt {attempt[0]} failed")
            return "success"

        # Run with retry
        async def run_with_retry():
            return await AsyncManager.with_retry(
                failing_coro,
                max_retries=3,
                retry_delay=0.1
            )

        result = self.async_manager.run_coroutine(run_with_retry())
        self.assertEqual(result, "success")
        self.assertEqual(attempt[0], 3)


class TestPerformanceOptimization(unittest.TestCase):
    """Test performance optimization improvements."""

    def setUp(self):
        """Set up test environment."""
        self.performance_optimizer = PerformanceOptimizer(
            cache_size=100,
            cache_ttl=3600,
            batch_size=10,
            num_threads=4
        )

    def test_cached_decorator(self):
        """Test cached decorator."""
        # Define a function to cache
        call_count = [0]

        @self.performance_optimizer.cached
        def expensive_function(arg):
            call_count[0] += 1
            return f"result_{arg}"

        # Call the function multiple times with the same argument
        result1 = expensive_function("test")
        result2 = expensive_function("test")
        result3 = expensive_function("test")

        # Check that the function was only called once
        self.assertEqual(call_count[0], 1)
        self.assertEqual(result1, "result_test")
        self.assertEqual(result2, "result_test")
        self.assertEqual(result3, "result_test")

        # Call with a different argument
        result4 = expensive_function("other")
        self.assertEqual(call_count[0], 2)
        self.assertEqual(result4, "result_other")

    def test_batch_process(self):
        """Test batch processing."""
        # Define a function to process batches
        processed_batches = []

        def process_batch(batch):
            processed_batches.append(batch)
            return [f"processed_{item}" for item in batch]

        # Process a list of items
        items = [f"item_{i}" for i in range(25)]
        results = self.performance_optimizer.batch_process(process_batch, items)

        # Check that the items were processed in batches
        self.assertEqual(len(processed_batches), 3)  # 25 items / 10 batch size = 3 batches
        self.assertEqual(len(processed_batches[0]), 10)
        self.assertEqual(len(processed_batches[1]), 10)
        self.assertEqual(len(processed_batches[2]), 5)

        # Check that all items were processed
        self.assertEqual(len(results), 25)
        self.assertEqual(results[0], "processed_item_0")
        self.assertEqual(results[24], "processed_item_24")

    def test_parallel_process(self):
        """Test parallel processing."""
        # Define a function to process items
        def process_item(item):
            time.sleep(0.01)  # Simulate work
            return f"processed_{item}"

        # Process a list of items
        items = [f"item_{i}" for i in range(20)]

        # Time sequential processing
        start_time = time.time()
        sequential_results = [process_item(item) for item in items]
        sequential_time = time.time() - start_time

        # Time parallel processing
        start_time = time.time()
        parallel_results = self.performance_optimizer.parallel_process(process_item, items)
        parallel_time = time.time() - start_time

        # Check that all items were processed correctly
        self.assertEqual(len(parallel_results), 20)
        self.assertEqual(set(parallel_results), set(sequential_results))

        # Check that parallel processing was faster
        # Note: This test might be flaky on some systems
        logger.info(f"Sequential time: {sequential_time:.4f}s, Parallel time: {parallel_time:.4f}s")
        self.assertLess(parallel_time, sequential_time)


class TestIntegration(unittest.TestCase):
    """Test integration of all improvements."""

    def setUp(self):
        """Set up test environment."""
        self.query = ArkTSQueryAsync(
            qdrant_url=config.QDRANT_URL,
            collection_name=config.COLLECTION_NAME,
            ollama_url=config.OLLAMA_URL,
            embedding_model=config.EMBEDDING_MODEL
        )
        self.tools = ArkTSImportTools(
            qdrant_url=config.QDRANT_URL,
            collection_name=config.COLLECTION_NAME,
            ollama_url=config.OLLAMA_URL,
            embedding_model=config.EMBEDDING_MODEL
        )

    def test_query_with_error_handling(self):
        """Test query with error handling."""
        # Test with invalid parameters
        try:
            # Use the error handler directly
            error_handler = ErrorHandler()
            with self.assertRaises(ValueError):
                error_handler.validate_query_params(query="button", limit=-1)
        except Exception as e:
            self.fail(f"Error handler validation failed: {str(e)}")

    def test_async_query(self):
        """Test async query."""
        # Run async query
        async def run_async_query():
            return await self.query.suggest_imports_async("button", limit=3)

        async_mgr = AsyncManager()
        results = async_mgr.run_in_new_loop(run_async_query())

        # Check results
        self.assertGreater(len(results), 0)
        self.assertEqual(results[0]['symbol_type'], 'component')
        self.assertEqual(results[0]['symbol_name'], 'Button')

    def test_agno_tools(self):
        """Test Agno tools."""
        # Test component search
        result = self.tools.search_component("Button", limit=3)
        self.assertIn("Button", result)
        self.assertIn("component", result)

        # Test API search
        result = self.tools.search_arkts_api("button", limit=3)
        self.assertIn("Button", result)

        # Note: We skip the import path search test as it depends on specific data
        # that might not be available in the test environment


if __name__ == "__main__":
    unittest.main()
