Comprehensive Agno Agent Test Results
==================================================

Test: UI_Component_Button
Function: search_component
Query: button
Expected: ['Button']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Text
Function: search_component
Query: text
Expected: ['Text']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Image
Function: search_component
Query: image
Expected: ['Image']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_List
Function: search_component
Query: list
Expected: ['List']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Grid
Function: search_component
Query: grid
Expected: ['Grid']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Column
Function: search_component
Query: column
Expected: ['Column']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Row
Function: search_component
Query: row
Expected: ['Row']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Stack
Function: search_component
Query: stack
Expected: ['Stack']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Flex
Function: search_component
Query: flex
Expected: ['Flex']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Scroll
Function: search_component
Query: scroll
Expected: ['Scroll']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Tabs
Function: search_component
Query: tabs
Expected: ['Tabs']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Navigation
Function: search_component
Query: navigation
Expected: ['Navigation']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Swiper
Function: search_component
Query: swiper
Expected: ['Swiper']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Progress
Function: search_component
Query: progress
Expected: ['Progress']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Slider
Function: search_component
Query: slider
Expected: ['Slider']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Toggle
Function: search_component
Query: toggle
Expected: ['Toggle']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Radio
Function: search_component
Query: radio
Expected: ['Radio']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Checkbox
Function: search_component
Query: checkbox
Expected: ['Checkbox']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_TextInput
Function: search_component
Query: text input
Expected: ['TextInput']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_TextArea
Function: search_component
Query: text area
Expected: ['TextArea']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Search
Function: search_component
Query: search
Expected: ['Search']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Select
Function: search_component
Query: select
Expected: ['Select']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_DatePicker
Function: search_component
Query: date picker
Expected: ['DatePicker']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_TimePicker
Function: search_component
Query: time picker
Expected: ['TimePicker']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Calendar
Function: search_component
Query: calendar
Expected: ['CalendarPicker']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Badge
Function: search_component
Query: badge
Expected: ['Badge']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Divider
Function: search_component
Query: divider
Expected: ['Divider']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Loading
Function: search_component
Query: loading
Expected: ['LoadingProgress']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Rating
Function: search_component
Query: rating
Expected: ['Rating']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: UI_Component_Gauge
Function: search_component
Query: gauge
Expected: ['Gauge']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: UI_Components

--------------------------------------------------

Test: Layout_GridRow
Function: search_component
Query: grid row
Expected: ['GridRow']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_GridCol
Function: search_component
Query: grid column
Expected: ['GridCol']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_RelativeContainer
Function: search_component
Query: relative container
Expected: ['RelativeContainer']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_WaterFlow
Function: search_component
Query: water flow
Expected: ['WaterFlow']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_FlowItem
Function: search_component
Query: flow item
Expected: ['FlowItem']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_ListItem
Function: search_component
Query: list item
Expected: ['ListItem']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_GridItem
Function: search_component
Query: grid item
Expected: ['GridItem']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_TabContent
Function: search_component
Query: tab content
Expected: ['TabContent']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_Panel
Function: search_component
Query: panel
Expected: ['Panel']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_SideBar
Function: search_component
Query: sidebar
Expected: ['SideBarContainer']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_Refresh
Function: search_component
Query: refresh
Expected: ['Refresh']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_Stepper
Function: search_component
Query: stepper
Expected: ['Stepper']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_StepperItem
Function: search_component
Query: stepper item
Expected: ['StepperItem']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_NavDestination
Function: search_component
Query: nav destination
Expected: ['NavDestination']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Layout_NavRouter
Function: search_component
Query: nav router
Expected: ['NavRouter']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Layout_Components

--------------------------------------------------

Test: Advanced_Dialog
Function: search_component
Query: dialog
Expected: ['Dialog']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Popup
Function: search_component
Query: popup
Expected: ['Popup']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Menu
Function: search_component
Query: menu
Expected: ['Menu']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_MenuItem
Function: search_component
Query: menu item
Expected: ['MenuItem']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_ActionSheet
Function: search_component
Query: action sheet
Expected: ['ActionSheet']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_AlertDialog
Function: search_component
Query: alert dialog
Expected: ['AlertDialog']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_CustomDialog
Function: search_component
Query: custom dialog
Expected: ['CustomDialogController']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_RichText
Function: search_component
Query: rich text
Expected: ['RichText']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_RichEditor
Function: search_component
Query: rich editor
Expected: ['RichEditor']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Web
Function: search_component
Query: web
Expected: ['Web']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Video
Function: search_component
Query: video
Expected: ['Video']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Canvas
Function: search_component
Query: canvas
Expected: ['Canvas']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_XComponent
Function: search_component
Query: xcomponent
Expected: ['XComponent']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Particle
Function: search_component
Query: particle
Expected: ['Particle']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_Component3D
Function: search_component
Query: 3d component
Expected: ['Component3D']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_SymbolGlyph
Function: search_component
Query: symbol glyph
Expected: ['SymbolGlyph']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_SymbolSpan
Function: search_component
Query: symbol span
Expected: ['SymbolSpan']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_ImageSpan
Function: search_component
Query: image span
Expected: ['ImageSpan']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_ContainerSpan
Function: search_component
Query: container span
Expected: ['ContainerSpan']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Advanced_StyledString
Function: search_component
Query: styled string
Expected: ['StyledString']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'
Category: Advanced_Components

--------------------------------------------------

Test: Import_Path_Button_Import
Function: search_import_path
Query: Button
Expected: ['Button']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_Text_Import
Function: search_import_path
Query: Text
Expected: ['Text']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_Image_Import
Function: search_import_path
Query: Image
Expected: ['Image']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_List_Import
Function: search_import_path
Query: List
Expected: ['List']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_Grid_Import
Function: search_import_path
Query: Grid
Expected: ['Grid']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_UIAbility_Import
Function: search_import_path
Query: UIAbility
Expected: ['UIAbility']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_router_Import
Function: search_import_path
Query: router
Expected: ['router']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_preferences_Import
Function: search_import_path
Query: preferences
Expected: ['preferences']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_http_Import
Function: search_import_path
Query: http
Expected: ['http']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_fs_Import
Function: search_import_path
Query: fs
Expected: ['fs']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_audio_Import
Function: search_import_path
Query: audio
Expected: ['audio']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_camera_Import
Function: search_import_path
Query: camera
Expected: ['camera']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_bluetooth_Import
Function: search_import_path
Query: bluetooth
Expected: ['bluetooth']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_wifi_Import
Function: search_import_path
Query: wifi
Expected: ['wifi']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: Import_Path_window_Import
Function: search_import_path
Query: window
Expected: ['window']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'
Category: Import_Paths

--------------------------------------------------

Test: System_API_UIAbility
Function: search_arkts_api
Query: ui ability
Expected: ['UIAbility']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_AbilityStage
Function: search_arkts_api
Query: ability stage
Expected: ['AbilityStage']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Want
Function: search_arkts_api
Query: want
Expected: ['Want']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Context
Function: search_arkts_api
Query: context
Expected: ['Context']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Configuration
Function: search_arkts_api
Query: configuration
Expected: ['Configuration']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Router
Function: search_arkts_api
Query: router
Expected: ['router']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Preferences
Function: search_arkts_api
Query: preferences
Expected: ['preferences']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_RelationalStore
Function: search_arkts_api
Query: relational store
Expected: ['relationalStore']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_FileSystem
Function: search_arkts_api
Query: file system
Expected: ['fs']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_HTTP
Function: search_arkts_api
Query: http
Expected: ['http']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Bluetooth
Function: search_arkts_api
Query: bluetooth
Expected: ['bluetooth']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_WiFi
Function: search_arkts_api
Query: wifi
Expected: ['wifi']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Location
Function: search_arkts_api
Query: location
Expected: ['geoLocationManager']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Sensor
Function: search_arkts_api
Query: sensor
Expected: ['sensor']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Camera
Function: search_arkts_api
Query: camera
Expected: ['camera']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Audio
Function: search_arkts_api
Query: audio
Expected: ['audio']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Notification
Function: search_arkts_api
Query: notification
Expected: ['notificationManager']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Vibrator
Function: search_arkts_api
Query: vibrator
Expected: ['vibrator']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Battery
Function: search_arkts_api
Query: battery
Expected: ['batteryInfo']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Display
Function: search_arkts_api
Query: display
Expected: ['display']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Window
Function: search_arkts_api
Query: window
Expected: ['window']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Accessibility
Function: search_arkts_api
Query: accessibility
Expected: ['accessibility']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Security
Function: search_arkts_api
Query: security
Expected: ['cryptoFramework']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Account
Function: search_arkts_api
Query: account
Expected: ['appAccount']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: System_API_Bundle
Function: search_arkts_api
Query: bundle
Expected: ['bundleManager']
Success: False
Analysis: Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'
Category: System_APIs

--------------------------------------------------

Test: Agent_Query_105
Function: handle_agent_query
Query: ArkTS search: 'Button component'
Expected: ['Button']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_106
Function: handle_agent_query
Query: ArkTS search: 'Text display'
Expected: ['Text']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_107
Function: handle_agent_query
Query: ArkTS search: 'Image component'
Expected: ['Image']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_108
Function: handle_agent_query
Query: ArkTS search: 'List component'
Expected: ['List']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_109
Function: handle_agent_query
Query: ArkTS search: 'Grid layout'
Expected: ['Grid']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_110
Function: handle_agent_query
Query: ArkTS search: 'UIAbility class'
Expected: ['UIAbility']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_111
Function: handle_agent_query
Query: ArkTS search: 'router navigation'
Expected: ['router']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_112
Function: handle_agent_query
Query: ArkTS search: 'http requests'
Expected: ['http']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_113
Function: handle_agent_query
Query: ArkTS search: 'file system'
Expected: ['fs']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_114
Function: handle_agent_query
Query: ArkTS search: 'preferences storage'
Expected: ['preferences']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_115
Function: handle_agent_query
Query: ArkTS search: 'camera functionality'
Expected: ['camera']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_116
Function: handle_agent_query
Query: ArkTS search: 'audio playback'
Expected: ['audio']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_117
Function: handle_agent_query
Query: ArkTS search: 'bluetooth connection'
Expected: ['bluetooth']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_118
Function: handle_agent_query
Query: ArkTS search: 'wifi management'
Expected: ['wifi']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

Test: Agent_Query_119
Function: handle_agent_query
Query: ArkTS search: 'window operations'
Expected: ['window']
Success: False
Analysis: Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'
Category: Agent_Queries

--------------------------------------------------

