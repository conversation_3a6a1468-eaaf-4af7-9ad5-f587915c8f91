# ArkData (Ark Data Management)

- [Introduction to ArkData](data-mgmt-overview.md)
- Unified Data Definition
  - [Unified Data Definition Overview](unified-data-definition-overview.md)
  - [UTDs](uniform-data-type-descriptors.md)
  - [Uniform Data Structs](uniform-data-structure.md)
  - [Prebuilt UTDs](uniform-data-type-list.md)
- Application Data Persistence
  - [Application Data Persistence Overview](app-data-persistence-overview.md)
  - [Persisting Preferences Data](data-persistence-by-preferences.md)
  - [Persisting KV Store Data](data-persistence-by-kv-store.md)
  - [Persisting RDB Store Data](data-persistence-by-rdb-store.md)
- Distributed Application Data Sync
  - [Distributed Application Data Sync Overview](sync-app-data-across-devices-overview.md)
  - [Cross-Device Sync of KV Stores](data-sync-of-kv-store.md)
  - [Cross-Device Sync of RDB Stores](data-sync-of-rdb-store.md)
  - [Cross-Device Sync of Distributed Data Objects](data-sync-of-distributed-data-object.md)
- Data Reliability and Security
  - [Data Reliability and Security Overview](data-reliability-security-overview.md)
  - [Database Backup and Restore](data-backup-and-restore.md)
  - [Database Encryption](data-encryption.md)
  - [Access Control by Device and Data Level](access-control-by-device-and-data-level.md)
  - [Using an EL5 Database](encrypted_estore_guidelines.md)
- Cross-Application Data Sharing
  - [Data Sharing Overview](data-share-overview.md)
  <!--Del-->
  - One-to-Many Data Sharing (for System Applications Only)
    - [Sharing Data via DataShareExtensionAbility](share-data-by-datashareextensionability.md)
    - [Silent Access via DatamgrService](share-data-by-silent-access.md)
  <!--DelEnd-->
  - Many-to-Many Data Sharing
    - [Sharing Data Using Unified Data Channels](unified-data-channels.md)
- [RelationalStore Development (C/C++)](native-relational-store-guidelines.md)
- [UDMF Development Guide (C/C++)](native-unified-data-management-framework-guidelines.md)
- [Persisting User Preferences (C/C++)](preferences-guidelines.md)
