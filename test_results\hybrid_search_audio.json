{"standard": [{"symbol_name": "audio", "symbol_type": "namespace", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12", "import_statement": "import { audio } from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.6787143}, {"symbol_name": "AudioSessionStrategy", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Audio session strategy. @typedef AudioSessionStrategy @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12", "import_statement": "import { audio.AudioSessionStrategy } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioSessionStrategy", "score": 0.62228334}, {"symbol_name": "AudioR<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12", "import_statement": "import { audio.AudioRenderer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioRenderer", "score": 0.6183092}], "hybrid": [{"symbol_name": "audio", "symbol_type": "namespace", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12", "import_statement": "import { audio } from '@ohos.multimedia.audio';", "parent_symbol": null, "is_nested": false, "score": 0.6787143}, {"symbol_name": "AudioR<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12", "import_statement": "import { audio.AudioRenderer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioRenderer", "score": 0.6183092}, {"symbol_name": "AudioStreamInfo", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12", "import_statement": "import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioStreamInfo", "score": 0.6171435}]}