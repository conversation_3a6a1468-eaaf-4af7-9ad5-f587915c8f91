{"general": "# ArkTS Search Results\n\n## Namespace Results\n\n### 1. audio\n**Module:** @ohos.multimedia.audio\n**Import:** `import { audio } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6787\n\n@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\n\n---\n\n\n\n## Interface Results\n\n### 1. AudioRenderer\n**Module:** @ohos.multimedia.audio\n**Import:** `import { audio.AudioRenderer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6183\n\n**Nested in:** `audio`\n\nProvides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12\n\n---\n\n### 2. AudioStreamInfo\n**Module:** @ohos.multimedia.audio\n**Import:** `import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6171\n\n**Nested in:** `audio`\n\nDescribes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12\n\n---\n\n### 3. AudioCapturer\n**Module:** @ohos.multimedia.audio\n**Import:** `import { audio.AudioCapturer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6054\n\n**Nested in:** `audio`\n\nProvides APIs for audio recording. @typedef AudioCapturer @syscap SystemCapability.Multimedia.Audio.Capturer @crossplatform @since 12\n\n---\n\n### 4. AudioRendererInfo\n**Module:** @ohos.multimedia.audio\n**Import:** `import { audio.AudioRendererInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6037\n\n**Nested in:** `audio`\n\nDescribes audio renderer information. @typedef AudioRendererInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\n\n---\n\n\n", "component": "# Component Search Results\n\n## 1. audio\n**Type:** namespace\n**Import:** `import { audio } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6787\n\n@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\n\n---\n\n## 2. AudioRenderer\n**Type:** interface\n**Import:** `import { audio.AudioRenderer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6183\n\nProvides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12\n\n---\n\n## 3. AudioStreamInfo\n**Type:** interface\n**Import:** `import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6171\n\nDescribes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12\n\n---\n\n## 4. AudioCapturer\n**Type:** interface\n**Import:** `import { audio.AudioCapturer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6054\n\nProvides APIs for audio recording. @typedef AudioCapturer @syscap SystemCapability.Multimedia.Audio.Capturer @crossplatform @since 12\n\n---\n\n## 5. AudioRendererInfo\n**Type:** interface\n**Import:** `import { audio.AudioRendererInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6037\n\nDescribes audio renderer information. @typedef AudioRendererInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\n\n---\n", "import_path": "# Import Path Search Results\n\n## 1. audio\n**Type:** namespace\n**Module:** @ohos.multimedia.audio\n**Import Statement:** `import { audio } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6787\n---\n\n## 2. AudioRenderer\n**Type:** interface\n**Module:** @ohos.multimedia.audio\n**Import Statement:** `import { audio.AudioRenderer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6183\n\n**Note:** This is nested within `audio`.\n---\n\n## 3. AudioStreamInfo\n**Type:** interface\n**Module:** @ohos.multimedia.audio\n**Import Statement:** `import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6171\n\n**Note:** This is nested within `audio`.\n---\n\n## 4. AudioCapturer\n**Type:** interface\n**Module:** @ohos.multimedia.audio\n**Import Statement:** `import { audio.AudioCapturer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6054\n\n**Note:** This is nested within `audio`.\n---\n\n## 5. AudioRendererInfo\n**Type:** interface\n**Module:** @ohos.multimedia.audio\n**Import Statement:** `import { audio.AudioRendererInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6037\n\n**Note:** This is nested within `audio`.\n---\n", "nested": "# Nested Symbol Search Results\n\n## Nested in: None\n\n### 1. audio\n**Type:** namespace\n**Full Name:** None.audio\n**Import:** `import { audio } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6787\n\n@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\n\n---\n\n## Nested in: audio\n\n### 1. AudioRenderer\n**Type:** interface\n**Full Name:** audio.AudioRenderer\n**Import:** `import { audio.AudioRenderer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6183\n\nProvides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12\n\n---\n\n### 2. AudioStreamInfo\n**Type:** interface\n**Full Name:** audio.AudioStreamInfo\n**Import:** `import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6171\n\nDescribes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12\n\n---\n\n### 3. AudioCapturer\n**Type:** interface\n**Full Name:** audio.AudioCapturer\n**Import:** `import { audio.AudioCapturer } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6054\n\nProvides APIs for audio recording. @typedef AudioCapturer @syscap SystemCapability.Multimedia.Audio.Capturer @crossplatform @since 12\n\n---\n\n### 4. AudioRendererInfo\n**Type:** interface\n**Full Name:** audio.AudioRendererInfo\n**Import:** `import { audio.AudioRendererInfo } from '@ohos.multimedia.audio';`\n**Relevance Score:** 0.6037\n\nDescribes audio renderer information. @typedef AudioRendererInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\n\n---\n", "json": "[\n  {\n    \"symbol_name\": \"audio\",\n    \"symbol_type\": \"namespace\",\n    \"import_statement\": \"import { audio } from '@ohos.multimedia.audio';\",\n    \"module_name\": \"@ohos.multimedia.audio\",\n    \"score\": 0.6787143,\n    \"description\": \"@namespace audio @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\"\n  },\n  {\n    \"symbol_name\": \"AudioRenderer\",\n    \"symbol_type\": \"interface\",\n    \"import_statement\": \"import { audio.AudioRenderer } from '@ohos.multimedia.audio';\",\n    \"module_name\": \"@ohos.multimedia.audio\",\n    \"score\": 0.6183092,\n    \"description\": \"Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12\"\n  },\n  {\n    \"symbol_name\": \"AudioStreamInfo\",\n    \"symbol_type\": \"interface\",\n    \"import_statement\": \"import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';\",\n    \"module_name\": \"@ohos.multimedia.audio\",\n    \"score\": 0.6171435,\n    \"description\": \"Describes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12\"\n  },\n  {\n    \"symbol_name\": \"AudioCapturer\",\n    \"symbol_type\": \"interface\",\n    \"import_statement\": \"import { audio.AudioCapturer } from '@ohos.multimedia.audio';\",\n    \"module_name\": \"@ohos.multimedia.audio\",\n    \"score\": 0.60543317,\n    \"description\": \"Provides APIs for audio recording. @typedef AudioCapturer @syscap SystemCapability.Multimedia.Audio.Capturer @crossplatform @since 12\"\n  },\n  {\n    \"symbol_name\": \"AudioRendererInfo\",\n    \"symbol_type\": \"interface\",\n    \"import_statement\": \"import { audio.AudioRendererInfo } from '@ohos.multimedia.audio';\",\n    \"module_name\": \"@ohos.multimedia.audio\",\n    \"score\": 0.6036855,\n    \"description\": \"Describes audio renderer information. @typedef AudioRendererInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @atomicservice @since 12\"\n  }\n]", "table": "| Symbol Name | Symbol Type | Import Statement | Score |\n| --- | --- | --- | --- |\n| audio | namespace | `import { audio } from '@ohos.multimedia.audio';` | 0.6787 |\n| AudioRenderer | interface | `import { audio.AudioRenderer } from '@ohos.multimedia.audio';` | 0.6183 |\n| AudioStreamInfo | interface | `import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';` | 0.6171 |\n| AudioCapturer | interface | `import { audio.AudioCapturer } from '@ohos.multimedia.audio';` | 0.6054 |\n| AudioRendererInfo | interface | `import { audio.AudioRendererInfo } from '@ohos.multimedia.audio';` | 0.6037 |"}