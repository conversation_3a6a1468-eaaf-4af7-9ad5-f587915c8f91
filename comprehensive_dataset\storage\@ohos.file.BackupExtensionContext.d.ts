/*
 * Copyright (c) 2024 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
/**
 * @file
 * @kit CoreFileKit
 */
import ExtensionContext from './application/ExtensionContext';
/**
 * The context of an ability or an application. It allows access to
 * application-specific resources.
 * Can only be obtained through the ability.
 *
 * @extends ExtensionContext
 * @syscap SystemCapability.FileManagement.StorageService.Backup
 * @StageModelOnly
 * @since 12
 */
export default class BackupExtensionContext extends ExtensionContext {
    /**
     * Indicates backup dir.
     *
     * @type { string }
     * @syscap SystemCapability.FileManagement.StorageService.Backup
     * @StageModelOnly
     * @since 12
     */
    readonly backupDir: string;
}
