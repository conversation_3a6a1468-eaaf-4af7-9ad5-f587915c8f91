# Audio Kit

- [Introduction to Audio Kit](audio-kit-intro.md)
- [Selecting an Appropriate Audio Stream Type](using-right-streamusage-and-sourcetype.md)
- [Introduction to Audio Focus and Audio Session](audio-playback-concurrency.md)
- Audio Focus Management
  - [Using AudioSession to Manage Audio Focus (ArkTS)](audio-session-management.md)
  - [Using AudioSession to Manage Audio Focus (C/C++)](using-ohaudio-for-session.md)
- Audio Playback
  - [Audio Playback Overview](audio-playback-overview.md)
  - [Using AudioRenderer for Audio Playback](using-audiorenderer-for-playback.md)
  - [Responding to Audio Output Device Changes](audio-output-device-change.md)
  <!--Del-->
  - [Using TonePlayer for Audio Playback (for System Applications Only)](using-toneplayer-for-playback.md)
  <!--DelEnd-->
  - [Using OHAudio for Audio Playback (C/C++)](using-ohaudio-for-playback.md)
  - [Using AudioHaptic for Audio-Haptic Playback](using-audiohaptic-for-playback.md)
  - [Volume Management](volume-management.md)
  - [Audio Effect Management](audio-effect-management.md)
  <!--Del-->
  - [Spatial Audio Management (for System Applications Only)](audio-spatialization-management.md)
  <!--DelEnd-->
  - [Audio Playback Stream Management](audio-playback-stream-management.md)
  - [Managing Global Audio Output Devices](audio-output-device-management.md)
  <!--Del-->
  - [Distributed Audio Playback (for System Applications Only)](distributed-audio-playback.md)
  <!--DelEnd-->
- Audio Recording
  - [Audio Recording Overview](audio-recording-overview.md)
  - [Using AudioCapturer for Audio Recording](using-audiocapturer-for-recording.md)
  - [Using OHAudio for Audio Recording (C/C++)](using-ohaudio-for-recording.md)
  - [Microphone Management](mic-management.md)
  - [Audio Recording Stream Management](audio-recording-stream-management.md)
  - [Managing Global Audio Input Devices](audio-input-device-management.md)
- Audio Call
  - [Audio Call Overview](audio-call-overview.md)
  - [Developing Audio Call](audio-call-development.md)
- Not Recommended
  - [Switching from OpenSL ES to OHAudio (C/C++)](replace-opensles-by-ohaudio.md)
  - [Using OpenSL ES for Audio Playback (C/C++)](using-opensl-es-for-playback.md)
  - [Using OpenSL ES for Audio Recording (C/C++)](using-opensl-es-for-recording.md)
