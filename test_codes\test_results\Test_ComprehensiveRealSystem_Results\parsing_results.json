{"total_files": 30, "successful_files": 30, "failed_files": 0, "total_symbols": 105304, "symbol_types": {"type": 1132, "namespace": 604, "interface": 7257, "decorator": 93685, "callback_type": 109, "union_type": 1408, "intersection_type": 87, "class": 405, "function": 234, "enum": 312, "const": 34, "reexport_all": 6, "export_assignment": 31}, "file_results": [{"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\lib.dom.d.ts", "file_size": 842681, "success": true, "symbols_count": 681, "parse_time": 0.03919720649719238, "symbol_types": {"type": 1, "namespace": 2, "interface": 14, "decorator": 486, "callback_type": 1, "union_type": 176, "intersection_type": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.dom.d.ts", "file_size": 816786, "success": true, "symbols_count": 777, "parse_time": 0.029356718063354492, "symbol_types": {"namespace": 2, "interface": 14, "decorator": 568, "callback_type": 2, "union_type": 190, "intersection_type": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\tsserverlibrary.d.ts", "file_size": 747710, "success": true, "symbols_count": 2489, "parse_time": 0.14863061904907227, "symbol_types": {"class": 45, "interface": 1244, "type": 185, "function": 50, "namespace": 110, "enum": 41, "const": 9, "reexport_all": 1, "decorator": 632, "callback_type": 12, "union_type": 145, "intersection_type": 12, "export_assignment": 3}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\@babel\\types\\lib\\index.d.ts", "file_size": 611184, "success": true, "symbols_count": 75, "parse_time": 0.02127385139465332, "symbol_types": {"namespace": 2, "decorator": 16, "union_type": 57}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\typescript.d.ts", "file_size": 598789, "success": true, "symbols_count": 2174, "parse_time": 0.11792683601379395, "symbol_types": {"class": 35, "interface": 1022, "type": 183, "function": 44, "namespace": 100, "enum": 41, "reexport_all": 1, "decorator": 594, "callback_type": 10, "union_type": 134, "intersection_type": 7, "export_assignment": 3}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\typescriptServices.d.ts", "file_size": 598776, "success": true, "symbols_count": 2173, "parse_time": 0.13370275497436523, "symbol_types": {"class": 35, "interface": 1022, "type": 183, "function": 44, "namespace": 100, "enum": 41, "reexport_all": 1, "decorator": 594, "callback_type": 10, "union_type": 134, "intersection_type": 7, "export_assignment": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\tsserverlibrary.d.ts", "file_size": 587234, "success": true, "symbols_count": 2105, "parse_time": 0.1090993881225586, "symbol_types": {"class": 27, "interface": 1106, "type": 169, "function": 36, "namespace": 41, "enum": 39, "const": 9, "reexport_all": 1, "decorator": 519, "callback_type": 12, "union_type": 133, "intersection_type": 11, "export_assignment": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\declarations\\common.d.ts", "file_size": 566649, "success": true, "symbols_count": 11561, "parse_time": 0.059435367584228516, "symbol_types": {"class": 14, "interface": 103, "type": 38, "namespace": 2, "enum": 32, "decorator": 11358, "callback_type": 12, "union_type": 2}}, {"file_path": "../Information/default/openharmony/ets/component\\common.d.ts", "file_size": 566343, "success": true, "symbols_count": 11561, "parse_time": 0.0651395320892334, "symbol_types": {"class": 14, "interface": 103, "type": 38, "namespace": 2, "enum": 32, "decorator": 11358, "callback_type": 12, "union_type": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\typescript.d.ts", "file_size": 447512, "success": true, "symbols_count": 1806, "parse_time": 0.09899306297302246, "symbol_types": {"class": 17, "interface": 887, "type": 167, "function": 30, "namespace": 31, "enum": 39, "reexport_all": 1, "decorator": 493, "callback_type": 10, "union_type": 123, "intersection_type": 6, "export_assignment": 2}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\bin\\ark\\build-win\\legacy_api8\\node_modules\\typescript\\lib\\typescriptServices.d.ts", "file_size": 447499, "success": true, "symbols_count": 1805, "parse_time": 0.0881037712097168, "symbol_types": {"class": 17, "interface": 887, "type": 167, "function": 30, "namespace": 31, "enum": 39, "reexport_all": 1, "decorator": 493, "callback_type": 10, "union_type": 123, "intersection_type": 6, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.file.fs.d.ts", "file_size": 372692, "success": true, "symbols_count": 6311, "parse_time": 0.025374889373779297, "symbol_types": {"class": 3, "interface": 19, "namespace": 2, "enum": 4, "decorator": 6281, "callback_type": 1, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\storage\\@ohos.file.fs.d.ts", "file_size": 372692, "success": true, "symbols_count": 6311, "parse_time": 0.020591259002685547, "symbol_types": {"class": 3, "interface": 19, "namespace": 2, "enum": 4, "decorator": 6281, "callback_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.data.relationalStore.d.ts", "file_size": 363266, "success": true, "symbols_count": 3946, "parse_time": 0.03936648368835449, "symbol_types": {"namespace": 1, "class": 20, "interface": 23, "decorator": 3899, "union_type": 2, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\storage\\@ohos.data.relationalStore.d.ts", "file_size": 363266, "success": true, "symbols_count": 3946, "parse_time": 0.037062883377075195, "symbol_types": {"namespace": 1, "class": 20, "interface": 23, "decorator": 3899, "union_type": 2, "export_assignment": 1}}, {"file_path": "../Information/default/hms/ets/api\\@hms.health.store.d.ts", "file_size": 346063, "success": true, "symbols_count": 3639, "parse_time": 0.07672739028930664, "symbol_types": {"namespace": 49, "interface": 67, "decorator": 3512, "union_type": 10, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\hms\\@hms.health.store.d.ts", "file_size": 346063, "success": true, "symbols_count": 3639, "parse_time": 0.0731658935546875, "symbol_types": {"namespace": 49, "interface": 67, "decorator": 3512, "union_type": 10, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.security.cryptoFramework.d.ts", "file_size": 336615, "success": true, "symbols_count": 4570, "parse_time": 0.03809857368469238, "symbol_types": {"namespace": 1, "class": 6, "interface": 59, "decorator": 4503, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\webpack\\types.d.ts", "file_size": 335559, "success": true, "symbols_count": 562, "parse_time": 0.052435874938964844, "symbol_types": {"class": 122, "interface": 263, "type": 1, "namespace": 65, "const": 16, "decorator": 2, "callback_type": 1, "union_type": 55, "intersection_type": 36, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.audio.d.ts", "file_size": 325027, "success": true, "symbols_count": 3746, "parse_time": 0.03981733322143555, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3715, "callback_type": 1, "union_type": 1, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.audio.d.ts", "file_size": 325027, "success": true, "symbols_count": 3746, "parse_time": 0.03992652893066406, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3715, "callback_type": 1, "union_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.image.d.ts", "file_size": 296616, "success": true, "symbols_count": 3818, "parse_time": 0.03935098648071289, "symbol_types": {"namespace": 1, "class": 1, "interface": 20, "decorator": 3794, "union_type": 1, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.image.d.ts", "file_size": 296616, "success": true, "symbols_count": 3818, "parse_time": 0.03618025779724121, "symbol_types": {"namespace": 1, "class": 1, "interface": 20, "decorator": 3794, "union_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.window.d.ts", "file_size": 286762, "success": true, "symbols_count": 3336, "parse_time": 0.04143357276916504, "symbol_types": {"namespace": 1, "interface": 44, "decorator": 3289, "union_type": 1, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.avsession.d.ts", "file_size": 276572, "success": true, "symbols_count": 3258, "parse_time": 0.03531599044799805, "symbol_types": {"namespace": 1, "interface": 39, "decorator": 3213, "callback_type": 1, "union_type": 3, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.avsession.d.ts", "file_size": 276572, "success": true, "symbols_count": 3258, "parse_time": 0.03265547752380371, "symbol_types": {"namespace": 1, "interface": 39, "decorator": 3213, "callback_type": 1, "union_type": 3, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/build-tools\\ets-loader\\node_modules\\typescript\\lib\\lib.webworker.d.ts", "file_size": 272256, "success": true, "symbols_count": 119, "parse_time": 0.014339447021484375, "symbol_types": {"namespace": 1, "interface": 14, "decorator": 14, "union_type": 90}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.multimedia.media.d.ts", "file_size": 270515, "success": true, "symbols_count": 3457, "parse_time": 0.03581523895263672, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3419, "callback_type": 5, "union_type": 4, "export_assignment": 1}}, {"file_path": "../comprehensive_dataset\\multimedia\\@ohos.multimedia.media.d.ts", "file_size": 270515, "success": true, "symbols_count": 3457, "parse_time": 0.030193328857421875, "symbol_types": {"namespace": 1, "interface": 27, "decorator": 3419, "callback_type": 5, "union_type": 4, "export_assignment": 1}}, {"file_path": "../Information/default/openharmony/ets/api\\@ohos.web.webview.d.ts", "file_size": 267704, "success": true, "symbols_count": 3160, "parse_time": 0.04367494583129883, "symbol_types": {"namespace": 1, "class": 25, "interface": 31, "decorator": 3100, "callback_type": 1, "union_type": 1, "export_assignment": 1}}], "errors": []}