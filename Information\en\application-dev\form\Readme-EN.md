# Form Kit
- [Introduction to Form Kit](formkit-overview.md)
- Service Widget Development in Stage Model
  - Developing an ArkTS Widget
    - [ArkTS Widget Working Principles](arkts-ui-widget-working-principles.md)
    - [ArkTS Widget Related Modules](arkts-ui-widget-modules.md)
    - ArkTS Widget Development
      - [Creating an ArkTS Widget](arkts-ui-widget-creation.md)
      - [Configuring Widget Configuration Files](arkts-ui-widget-configuration.md)
      - [Widget Lifecycle Management](arkts-ui-widget-lifecycle.md)
      - Widget Page Development
        - [Widget Page Capability Overview](arkts-ui-widget-page-overview.md)
        - [Using Animations in the Widget](arkts-ui-widget-page-animation.md)
        - [Applying Custom Drawing in the Widget](arkts-ui-widget-page-custom-drawing.md)
      - Widget Event Development
        - [Widget Event Capability Overview](arkts-ui-widget-event-overview.md)
        - [Redirecting to a UIAbility Through the router Event](arkts-ui-widget-event-router.md)
        - [Launching a UIAbility in the Background Through the call Event](arkts-ui-widget-event-call.md)
        - [Updating Widget Content Through the message Event](arkts-ui-widget-event-formextensionability.md)
        - [Updating Widget Content Through the router or call Event](arkts-ui-widget-event-uiability.md)
      - Widget Data Interaction
        - [Widget Data Interaction Overview](arkts-ui-widget-interaction-overview.md)
        - [Configuring a Widget to Update Periodically](arkts-ui-widget-update-by-time.md)
        <!--Del-->
        - [Updating Widget Content Through a Proxy](arkts-ui-widget-update-by-proxy.md)
        <!--DelEnd-->
        - [Updating Local and Online Images in the Widget](arkts-ui-widget-image-update.md)
        - [Updating Widget Content by State](arkts-ui-widget-update-by-status.md)
    <!--Del--> 
    - [Widget Host Development (for System Applications Only)](widget-host-development-guide.md)
    <!--DelEnd-->
  - [Developing a JS Widget](js-ui-widget-development.md)
- [Service Widget Development in FA Model](widget-development-fa.md)
