"""
ArkTS Indexer

This module provides functionality to index ArkTS files for the Import Estimator.
It extracts symbols from ArkTS files and indexes them in Qdrant.
"""

import os
import time
import logging
import argparse
import requests
import concurrent.futures
from concurrent.futures import ThreadPoolExecutor
from typing import List, Dict, Any
from tqdm import tqdm
from qdrant_client import QdrantClient
from qdrant_client.http import models
from component_cards import ArkTSSymbolParser

# Import configuration
import config

# Configure tqdm to work in both console and file
class TqdmToLogger(object):
    """Redirect tqdm progress to logger"""
    def __init__(self, logger, level=logging.INFO):
        self.logger = logger
        self.level = level
        self.last_msg = ""

    def write(self, buf):
        buf = buf.strip()
        if buf and buf != self.last_msg:
            self.last_msg = buf
            self.logger.log(self.level, buf)

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("arkts_indexer.log")
    ]
)
logger = logging.getLogger("ArkTSIndexer")


class ArkTSIndexer:
    """ArkTS Indexer for indexing ArkTS files."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None,
                 ollama_url: str = None, embedding_model: str = None,
                 force_recreate: bool = False):
        """Initialize the indexer.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
            force_recreate: If True, always recreate the collection even if it exists
        """
        self.qdrant_url = qdrant_url or config.QDRANT_URL
        self.collection_name = collection_name or config.COLLECTION_NAME
        self.ollama_url = ollama_url or config.OLLAMA_URL
        self.embedding_model = embedding_model or config.EMBEDDING_MODEL
        self.parser = ArkTSSymbolParser()

        # Set vector size based on embedding model
        self.vector_size = config.VECTOR_SIZES.get(self.embedding_model, config.DEFAULT_VECTOR_SIZE)

        # Initialize Qdrant client with HTTP protocol only
        self.client = QdrantClient(
            url=self.qdrant_url,
            prefer_grpc=False,  # Explicitly disable gRPC
            timeout=60.0,  # Increase timeout for large operations
            check_compatibility=False
        )
        logger.info(f"Initialized Qdrant client with HTTP protocol at {self.qdrant_url}")

        # Ensure collection exists
        self._ensure_collection_exists(force_recreate)

        logger.info(f"ArkTSIndexer initialized with Qdrant at {self.qdrant_url} and Ollama at {self.ollama_url}")
        logger.info(f"Using embedding model {self.embedding_model} with vector size {self.vector_size}")

    def _get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama.

        Args:
            text: Text to embed

        Returns:
            Embedding vector as a list of floats
        """
        # Maximum retry attempts
        max_retries = config.MAX_RETRIES
        retry_delay = config.RETRY_DELAY

        for attempt in range(max_retries):
            try:
                # Call Ollama API to get embedding
                response = requests.post(
                    f"{self.ollama_url}/api/embeddings",
                    json={
                        "model": self.embedding_model,
                        "prompt": text
                    },
                    timeout=config.REQUEST_TIMEOUT
                )

                # Check if request was successful
                if response.status_code == 200:
                    # Extract embedding from response
                    embedding = response.json().get("embedding", [])

                    # Verify embedding dimensions
                    if len(embedding) == self.vector_size:
                        return embedding
                    else:
                        logger.warning(f"Embedding dimension mismatch: expected {self.vector_size}, got {len(embedding)}")
                        # Try to pad or truncate to match expected size
                        if len(embedding) > self.vector_size:
                            logger.warning(f"Truncating embedding from {len(embedding)} to {self.vector_size}")
                            return embedding[:self.vector_size]
                        else:
                            logger.warning(f"Padding embedding from {len(embedding)} to {self.vector_size}")
                            return embedding + [0.0] * (self.vector_size - len(embedding))
                else:
                    logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {response.status_code} {response.text}")

            except requests.exceptions.ConnectionError:
                logger.error(f"Connection error to Ollama server (attempt {attempt+1}/{max_retries}). Is Ollama running?")
            except requests.exceptions.Timeout:
                logger.error(f"Timeout connecting to Ollama server (attempt {attempt+1}/{max_retries})")
            except Exception as e:
                logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {str(e)}")

            # Wait before retrying
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff

        # If all retries failed, return a zero vector
        logger.error(f"All {max_retries} attempts to get embedding failed. Using zero vector.")
        return [0.0] * self.vector_size

    def _ensure_collection_exists(self, force_recreate: bool = False) -> None:
        """Ensure the Qdrant collection exists, create it if it doesn't.

        Args:
            force_recreate: If True, always recreate the collection even if it exists
        """
        try:
            # Check if collection exists
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            # Delete collection if it exists and force_recreate is True
            if self.collection_name in collection_names:
                if force_recreate:
                    logger.info(f"Force recreating collection {self.collection_name}")
                    print(f"\n🗑️  Deleting existing collection: {self.collection_name}")
                    self.client.delete_collection(collection_name=self.collection_name)
                else:
                    logger.info(f"Collection {self.collection_name} already exists")
                    print(f"\n✅ Using existing collection: {self.collection_name}")
                    return

            # Create collection
            logger.info(f"Creating new collection {self.collection_name} with vector size {self.vector_size}")
            print(f"\n🆕 Creating new collection: {self.collection_name} (vector size: {self.vector_size})")

            # Create collection with optimized settings for batch loading
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=models.VectorParams(
                    size=self.vector_size,
                    distance=models.Distance.COSINE
                ),
                # Optimize for batch loading by deferring HNSW graph construction
                hnsw_config=models.HnswConfigDiff(
                    m=16,  # Default HNSW graph parameter
                    ef_construct=100  # Default HNSW graph parameter
                ),
                # Disable indexing during initial upload for better performance
                optimizers_config=models.OptimizersConfigDiff(
                    indexing_threshold=20000  # Default indexing threshold
                )
            )

            # Create payload indices for filtering and sorting
            try:
                logger.info("Creating payload indices for better search performance")
                print("Creating payload indices...")

                # Create indices for commonly used fields
                index_fields = ["symbol_name", "symbol_type", "description", "module_name", "parent_symbol"]

                for field in index_fields:
                    try:
                        self.client.create_payload_index(
                            collection_name=self.collection_name,
                            field_name=field
                        )
                        logger.info(f"Created index for field: {field}")
                    except Exception as field_error:
                        logger.warning(f"Could not create index for field {field}: {str(field_error)}")

                logger.info("Successfully created payload indices")
            except Exception as e:
                logger.warning(f"Could not create payload indices: {str(e)}")
                logger.warning("Continuing without payload indices. Search performance may be affected.")

            logger.info(f"Successfully created collection {self.collection_name}")
            print(f"✅ Collection {self.collection_name} is ready for indexing")
        except Exception as e:
            logger.error(f"Error ensuring collection exists: {str(e)}")
            print(f"❌ Error creating collection: {str(e)}")
            raise



    def _get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a batch of texts by making multiple API calls.

        This processes texts in parallel for better performance.

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        # Use ThreadPoolExecutor to parallelize embedding generation
        with ThreadPoolExecutor(max_workers=min(16, len(texts))) as executor:
            # Submit all embedding tasks
            futures = [executor.submit(self._get_embedding, text) for text in texts]

            # Collect results
            embeddings = []
            for future in concurrent.futures.as_completed(futures):
                try:
                    embedding = future.result()
                    embeddings.append(embedding)
                except Exception as e:
                    logger.error(f"Error getting embedding: {str(e)}")
                    # Use zero vector as fallback
                    embeddings.append([0.0] * self.vector_size)

            return embeddings

    def _index_symbols(self, symbols: List[Dict[str, Any]]) -> None:
        """Index symbols in Qdrant using parallel processing and efficient batching.
        OPTIMIZED FOR MAXIMUM SPEED.

        Args:
            symbols: List of symbols to index
        """
        if not symbols:
            return

        total_symbols = len(symbols)
        logger.info(f"Indexing {total_symbols} symbols using MAXIMUM SPEED settings")
        print(f"\n[1/3] Preparing to index {total_symbols} symbols with MAXIMUM SPEED")

        # Step 1: Generate embeddings with parallel processing
        print(f"\n[2/3] Generating embeddings for {total_symbols} symbols...")

        # Prepare texts for embedding
        texts_to_embed = []
        for symbol in symbols:
            if 'full_name' in symbol:
                text_to_embed = f"{symbol['full_name']} {symbol['symbol_type']} {symbol['description']}"
            else:
                text_to_embed = f"{symbol['symbol_name']} {symbol['symbol_type']} {symbol['description']}"
            texts_to_embed.append(text_to_embed)

        # Process embeddings in parallel using ThreadPoolExecutor with optimized batching
        all_vectors = [None] * len(texts_to_embed)

        # Use config value for embedding batch size
        embedding_batch_size = config.MAX_EMBEDDING_BATCH_SIZE

        # Use ProcessPoolExecutor for CPU-bound tasks (embedding generation)
        # This provides better performance for CPU-intensive tasks
        with tqdm(total=len(texts_to_embed), desc="Generating embeddings", ncols=100) as pbar:
            # Use ThreadPoolExecutor for parallel processing with maximum workers
            with concurrent.futures.ThreadPoolExecutor(max_workers=config.EMBEDDING_MAX_WORKERS) as executor:
                # Process in larger chunks for better efficiency
                for i in range(0, len(texts_to_embed), embedding_batch_size):
                    # Get batch of texts
                    batch_end = min(i + embedding_batch_size, len(texts_to_embed))
                    batch_texts = texts_to_embed[i:batch_end]
                    batch_indices = list(range(i, batch_end))

                    # Try to use batch embedding API if available
                    try:
                        # Get embeddings for the entire batch at once
                        batch_vectors = self._get_embeddings_batch(batch_texts)

                        # Assign vectors to their positions
                        for idx, vector in zip(batch_indices, batch_vectors):
                            all_vectors[idx] = vector
                            pbar.update(1)

                    except Exception as batch_error:
                        logger.warning(f"Batch embedding failed: {str(batch_error)}. Falling back to individual embeddings.")

                        # Fall back to individual embeddings
                        futures = {executor.submit(self._get_embedding, text): idx
                                for idx, text in zip(batch_indices, batch_texts)}

                        # Process results as they complete
                        for future in concurrent.futures.as_completed(futures):
                            idx = futures[future]
                            try:
                                vector = future.result()
                                all_vectors[idx] = vector
                            except Exception as e:
                                logger.error(f"Error generating embedding for symbol {idx}: {str(e)}")
                                # Use zero vector as fallback
                                all_vectors[idx] = [0.0] * self.vector_size
                            pbar.update(1)

        # Step 2: Create points and upload to Qdrant using optimized batch operations
        print(f"\n[3/3] Uploading {total_symbols} symbols to database with maximum parallelism...")

        # Create points with optimized ID generation
        points = []
        for i, symbol in enumerate(symbols):
            # Create unique ID - optimized for speed
            id_str = f"{symbol['symbol_name']}:{symbol['module_name']}:{symbol.get('parent_symbol', '')}"
            point_id = abs(hash(id_str)) % (2**63 - 1)  # Positive 64-bit integer

            # Create point
            point = models.PointStruct(
                id=point_id,
                vector=all_vectors[i],
                payload=symbol
            )
            points.append(point)

        # Use fixed batch size from config for consistent performance
        upload_batch_size = config.UPLOAD_BATCH_SIZE

        # Calculate number of batches
        num_batches = (len(points) + upload_batch_size - 1) // upload_batch_size
        logger.info(f"Uploading {len(points)} points in {num_batches} batches (batch size: {upload_batch_size})")

        # Use multiple threads for uploading batches in parallel
        # This significantly improves upload speed
        with tqdm(total=num_batches, desc="Uploading to database", ncols=100) as pbar:
            # Prepare batches
            batches = []
            for i in range(0, len(points), upload_batch_size):
                batch_end = min(i + upload_batch_size, len(points))
                batch = points[i:batch_end]
                batches.append((i // upload_batch_size + 1, batch))

            # Define worker function for uploading a batch
            def upload_batch(batch_info):
                batch_num, batch = batch_info
                logger.info(f"Uploading batch {batch_num}/{num_batches} with {len(batch)} points")

                # Try with exponential backoff
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        # Use wait=False for faster uploads, we'll sync at the end
                        self.client.upsert(
                            collection_name=self.collection_name,
                            points=batch,
                            wait=False  # Don't wait for confirmation for speed
                        )
                        # If successful, return batch number
                        return batch_num
                    except Exception as e:
                        logger.error(f"Error uploading batch {batch_num}/{num_batches} (attempt {attempt+1}/{max_retries}): {str(e)}")

                        if attempt < max_retries - 1:
                            # Wait before retrying with exponential backoff
                            retry_delay = 2 ** attempt  # 1, 2, 4 seconds
                            logger.info(f"Retrying in {retry_delay} seconds...")
                            time.sleep(retry_delay)
                        else:
                            logger.error(f"Failed to upload batch {batch_num} after {max_retries} attempts")
                            return None

            # Use ThreadPoolExecutor for parallel uploads
            # This is I/O bound so ThreadPoolExecutor is appropriate
            with concurrent.futures.ThreadPoolExecutor(max_workers=min(8, num_batches)) as executor:
                # Submit all batches for processing
                future_to_batch = {executor.submit(upload_batch, batch_info): batch_info[0]
                                  for batch_info in batches}

                # Process results as they complete
                for future in concurrent.futures.as_completed(future_to_batch):
                    batch_num = future_to_batch[future]
                    try:
                        result = future.result()
                        if result is not None:
                            # Update progress bar
                            pbar.update(1)

                            # Log progress periodically
                            if batch_num % 5 == 0 or batch_num == num_batches:
                                progress_pct = (pbar.n / num_batches) * 100
                                logger.info(f"Upload progress: {progress_pct:.1f}% ({pbar.n}/{num_batches} batches)")
                    except Exception as e:
                        logger.error(f"Error processing batch {batch_num}: {str(e)}")
                        pbar.update(1)  # Still update progress even on error

        # Final sync to ensure all data is committed
        try:
            logger.info("Performing final sync to ensure all data is committed...")
            self.client.update_collection(
                collection_name=self.collection_name,
                optimizers_config=models.OptimizersConfigDiff(
                    indexing_threshold=0  # Force immediate indexing
                )
            )
            logger.info("Final sync completed successfully")
        except Exception as e:
            logger.warning(f"Final sync failed: {str(e)}. Data will be indexed asynchronously.")

        logger.info(f"Successfully indexed {len(points)} points")
        print(f"\n✅ Successfully indexed {len(points)} symbols into the database")

    def index_file(self, file_path: str) -> int:
        """Index a single file.

        Args:
            file_path: Path to the file to index

        Returns:
            Number of symbols indexed
        """
        try:
            # Parse symbols from file
            symbols = self.parser.parse_file(file_path)

            # Index symbols
            self._index_symbols(symbols)

            logger.info(f"Indexed {len(symbols)} symbols from {file_path}")
            return len(symbols)
        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {str(e)}")
            return 0





    def index_directory(self, directory_path: str) -> int:
        """Index all files in a directory using MAXIMUM SPEED parallel processing.

        Args:
            directory_path: Path to the directory to index

        Returns:
            Total number of symbols indexed
        """
        start_time = time.time()

        # Find all .d.ts and .d.ets files using parallel processing for large directories
        print(f"\n🔍 Scanning directory: {directory_path} with maximum parallelism")

        # Use a more efficient file scanning approach with parallel processing
        def scan_directory(dir_path):
            found_files = []
            try:
                with os.scandir(dir_path) as entries:
                    for entry in entries:
                        if entry.is_file() and (entry.name.endswith('.d.ts') or entry.name.endswith('.d.ets')):
                            found_files.append(entry.path)
                        elif entry.is_dir():
                            # Add directory to the queue for processing
                            dirs_to_scan.append(entry.path)
            except Exception as e:
                logger.error(f"Error scanning directory {dir_path}: {str(e)}")
            return found_files

        # Initialize variables
        files_to_index = []
        dirs_to_scan = [directory_path]

        # Use ThreadPoolExecutor for parallel directory scanning
        with concurrent.futures.ThreadPoolExecutor(max_workers=16) as executor:
            while dirs_to_scan:
                # Take a batch of directories to scan in parallel
                batch_size = min(100, len(dirs_to_scan))
                current_batch = dirs_to_scan[:batch_size]
                dirs_to_scan = dirs_to_scan[batch_size:]

                # Scan directories in parallel
                futures = [executor.submit(scan_directory, dir_path) for dir_path in current_batch]

                # Process results and add new directories to scan
                for future in concurrent.futures.as_completed(futures):
                    try:
                        batch_files = future.result()
                        files_to_index.extend(batch_files)
                    except Exception as e:
                        logger.error(f"Error processing directory batch: {str(e)}")

        total_files = len(files_to_index)
        logger.info(f"Found {total_files} files to index in {directory_path}")
        print(f"Found {total_files} files to index")

        if total_files == 0:
            logger.warning(f"No .d.ts or .d.ets files found in {directory_path}")
            print("No files to index. Please check the directory path.")
            return 0

        # List to collect all symbols from all files
        all_symbols = []

        # Determine optimal number of workers based on config
        max_workers = config.PARSING_MAX_WORKERS

        # Create progress bar for parsing files
        print(f"\n📄 Parsing {total_files} files using {max_workers} parallel workers (MAXIMUM SPEED)...")

        # Use a more efficient approach with chunking for better load balancing
        # This prevents the "straggler problem" where a few slow files delay completion
        chunk_size = 20  # Process files in chunks for better load balancing

        with tqdm(total=total_files, desc="Parsing files", ncols=100) as pbar:
            # Process files in chunks for better performance
            for i in range(0, total_files, chunk_size):
                end_idx = min(i + chunk_size, total_files)
                current_files = files_to_index[i:end_idx]

                # Use ProcessPoolExecutor for CPU-bound parsing tasks
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # Submit parsing tasks for current chunk
                    future_to_file = {executor.submit(self.parser.parse_file, file_path): file_path
                                     for file_path in current_files}

                    # Process results as they complete
                    for future in concurrent.futures.as_completed(future_to_file):
                        file_path = future_to_file[future]
                        try:
                            symbols = future.result()
                            if symbols:
                                all_symbols.extend(symbols)
                                logger.info(f"Parsed {len(symbols)} symbols from {file_path}")
                            else:
                                logger.info(f"No symbols found in {file_path}")
                        except Exception as e:
                            logger.error(f"Error parsing file {file_path}: {str(e)}")
                        finally:
                            pbar.update(1)

        # Now index all symbols in one batch
        total_symbols = len(all_symbols)
        parsing_time = time.time() - start_time

        logger.info(f"Parsed a total of {total_symbols} symbols from {total_files} files in {parsing_time:.2f} seconds")
        print(f"\n✅ Parsed {total_symbols} symbols from {total_files} files in {parsing_time:.2f} seconds")
        print(f"   Parsing speed: {total_files / parsing_time:.2f} files/second, {total_symbols / parsing_time:.2f} symbols/second")

        if total_symbols == 0:
            logger.warning("No symbols found in any files")
            print("No symbols found to index.")
            return 0

        # Print summary of symbol types
        symbol_types = {}
        for symbol in all_symbols:
            symbol_type = symbol.get('symbol_type', 'unknown')
            symbol_types[symbol_type] = symbol_types.get(symbol_type, 0) + 1

        print("\n📊 Symbol type distribution:")
        for symbol_type, count in sorted(symbol_types.items(), key=lambda x: x[1], reverse=True):
            percentage = (count / total_symbols) * 100
            print(f"  - {symbol_type}: {count} ({percentage:.1f}%)")
            logger.info(f"Symbol type: {symbol_type}, Count: {count}, Percentage: {percentage:.1f}%")

        # Index symbols with optimized batch processing
        if all_symbols:
            indexing_start_time = time.time()

            # Start indexing with maximum speed batch processing
            self._index_symbols(all_symbols)

            indexing_time = time.time() - indexing_start_time
            total_time = time.time() - start_time

            # Calculate performance metrics
            symbols_per_second = total_symbols / indexing_time
            files_per_second = total_files / total_time

            logger.info(f"Indexing completed in {indexing_time:.2f} seconds (total time: {total_time:.2f} seconds)")
            logger.info(f"Performance: {symbols_per_second:.2f} symbols/second, {files_per_second:.2f} files/second")

            print(f"\n🎉 Indexing complete! {total_symbols} symbols indexed in {indexing_time:.2f} seconds")
            print(f"   Total processing time: {total_time:.2f} seconds")
            print(f"   Indexing speed: {symbols_per_second:.2f} symbols/second")
            print(f"   Overall speed: {files_per_second:.2f} files/second")

        return total_symbols


def main():
    """Main function for indexing."""
    parser = argparse.ArgumentParser(description='ArkTS Indexer')
    parser.add_argument('--qdrant-url', type=str, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, help='Ollama embedding model to use')
    parser.add_argument('--directory', type=str, required=True, help='Directory to index')
    parser.add_argument('--force-recreate', action='store_true', help='Force recreate the collection even if it exists')

    args = parser.parse_args()

    # Initialize indexer
    indexer = ArkTSIndexer(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model,
        force_recreate=args.force_recreate
    )

    # Index directory
    total_symbols = indexer.index_directory(args.directory)

    logger.info(f"Indexing complete. Indexed {total_symbols} symbols.")


if __name__ == "__main__":
    main()
