# Introduction to Accessibility Kit

Accessibility is about giving equal access to everyone so that they can access and use information equally and conveniently under any circumstances. It helps narrow the digital divide between people of different classes, regions, ages, and health status in terms of information understanding, exchange, and utilization, so that they can participate in social life more conveniently and enjoy the benefits of technological advances.

The Accessibility Kit provides open capabilities for accessibility services, such as accessibility screen reading and accessibility focus, to better serve people with impairments and scenarios with obstacles.

## Available Capabilities

1. Accessibility status query: provides APIs for querying the accessibility status and touch exploration status of applications, so that applications can better serve people with impairments and scenarios with obstacles based on the accessibility status.

2. Accessibility event sending: provides APIs for sending accessibility events such as proactive focus and proactive reading, so that applications can provide better accessibility experience based on service scenarios.

## Relationship with Other Kits

ArkUI Kit provides Accessibility with the capabilities of defining accessibility component attributes, such as accessibility text and description, and sending accessibility events.
