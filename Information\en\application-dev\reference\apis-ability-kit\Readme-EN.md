# Ability Kit

- ArkTS APIs
  - Stage Model
    - [@ohos.app.ability.Ability (Ability Base Class)](js-apis-app-ability-ability.md)
    - [@ohos.app.ability.AbilityConstant (AbilityConstant)](js-apis-app-ability-abilityConstant.md)
    - [@ohos.app.ability.abilityLifecycleCallback (AbilityLifecycleCallback)](js-apis-app-ability-abilityLifecycleCallback.md)
    - [@ohos.app.ability.AbilityStage (AbilityStage)](js-apis-app-ability-abilityStage.md)
    - [@ohos.app.ability.ActionExtensionAbility (ExtensionAbility for Custom Actions)](js-apis-app-ability-actionExtensionAbility.md)
    - [@ohos.app.ability.application (Application)](js-apis-app-ability-application.md)
    - [@ohos.app.ability.ApplicationStateChangeCallback (ApplicationStateChangeCallback)](js-apis-app-ability-applicationStateChangeCallback.md)
    - [@ohos.app.ability.AtomicServiceOptions (Optional Start Options of EmbeddableUIAbilities)](js-apis-app-ability-atomicServiceOptions.md)
    - [@ohos.app.ability.autoFillManager (autoFillManager)](js-apis-app-ability-autoFillManager.md)
    - [@ohos.app.ability.ChildProcess](js-apis-app-ability-childProcess.md)
    - [@ohos.app.ability.childProcessManager (childProcessManager)](js-apis-app-ability-childProcessManager.md)
    - [@ohos.app.ability.ChildProcessArgs (ChildProcessArgs)](js-apis-app-ability-childProcessArgs.md)
    - [@ohos.app.ability.ChildProcessOptions (ChildProcessOptions)](js-apis-app-ability-childProcessOptions.md)
    - [@ohos.app.ability.common (Context)](js-apis-app-ability-common.md)
    - [@ohos.app.ability.contextConstant (ContextConstant)](js-apis-app-ability-contextConstant.md)
    - [@ohos.app.ability.EmbeddableUIAbility (Embeddable UIAbility)](js-apis-app-ability-embeddableUIAbility.md)
    - [@ohos.app.ability.EmbeddedUIExtensionAbility (ExtensionAbilities for Embeddable UI in Cross-Process Scenarios)](js-apis-app-ability-embeddedUIExtensionAbility.md)
    - [@ohos.app.ability.EnvironmentCallback (EnvironmentCallback)](js-apis-app-ability-environmentCallback.md)
    - [@ohos.app.ability.ExtensionAbility (ExtensionAbility Base Class)](js-apis-app-ability-extensionAbility.md)
    - [@ohos.app.ability.insightIntent (insightIntent)](js-apis-app-ability-insightIntent.md)
    - [@ohos.app.ability.InsightIntentContext (InsightIntent Call Execution Context)](js-apis-app-ability-insightIntentContext.md)
    - [@ohos.app.ability.InsightIntentExecutor (Base Class for InsightIntent Call Execution)](js-apis-app-ability-insightIntentExecutor.md)
    - [@ohos.app.ability.PhotoEditorExtensionAbility (Image Editing)](js-apis-app-ability-photoEditorExtensionAbility.md)
    - [@ohos.app.ability.OpenLinkOptions (OpenLinkOptions)](js-apis-app-ability-openLinkOptions.md)
    - [@ohos.app.ability.ShareExtensionAbility (ExtensionAbility for Sharing)](js-apis-app-ability-shareExtensionAbility.md)
    - [@ohos.app.ability.StartOptions (StartOptions)](js-apis-app-ability-startOptions.md)
    - [@ohos.app.ability.UIAbility (UIAbility)](js-apis-app-ability-uiAbility.md)
    - [@ohos.app.ability.UIExtensionAbility (Base Class for ExtensionAbilities with UI)](js-apis-app-ability-uiExtensionAbility.md)
    - [@ohos.app.ability.UIExtensionContentSession (UI Operation Class for ExtensionAbilities with UI)](js-apis-app-ability-uiExtensionContentSession.md)
    - [@ohos.app.ability.sendableContextManager](js-apis-app-ability-sendableContextManager.md)
    - [@ohos.app.appstartup.StartupConfig](js-apis-app-appstartup-startupConfig.md)
    - [@ohos.app.appstartup.StartupConfigEntry](js-apis-app-appstartup-startupConfigEntry.md)
    - [@ohos.app.appstartup.StartupListener](js-apis-app-appstartup-startupListener.md)
    - [@ohos.app.appstartup.startupManager](js-apis-app-appstartup-startupManager.md)
    - [@ohos.app.appstartup.StartupTask](js-apis-app-appstartup-startupTask.md)
    <!--Del-->
    - [@ohos.app.ability.AbilityConstant (AbilityConstant) (System API)](js-apis-app-ability-abilityConstant-sys.md)
    - [@ohos.app.ability.application (Application) (System API)](js-apis-app-ability-application-sys.md)
    - [@ohos.app.ability.AutoFillExtensionAbility (AutoFillExtensionAbility) (System API)](js-apis-app-ability-autoFillExtensionAbility-sys.md)
    - [@ohos.app.ability.autoStartupManager (autoStartupManager) (System API)](js-apis-app-ability-autoStartupManager-sys.md)
    - [@ohos.app.ability.common (Context) (System API)](js-apis-app-ability-common-sys.md)
    - [@ohos.app.ability.dialogSession (dialogSession) (System API)](js-apis-app-ability-dialogSession-sys.md)
    - [@ohos.app.ability.insightIntent (insightIntent) (System API)](js-apis-app-ability-insightIntent-sys.md)
    - [@ohos.app.ability.insightIntentDriver (Executing InsightIntent Calls) (System API)](js-apis-app-ability-insightIntentDriver-sys.md)
    - [@ohos.app.ability.ServiceExtensionAbility (ServiceExtensionAbility) (System API)](js-apis-app-ability-serviceExtensionAbility-sys.md)
    - [@ohos.app.ability.StartOptions (StartOptions) (System API)](js-apis-app-ability-startOptions-sys.md)
    - [@ohos.app.ability.UIExtensionContentSession (UI Operation Class for ExtensionAbilities with UI) (System API)](js-apis-app-ability-uiExtensionContentSession-sys.md)
    <!--DelEnd-->
  - FA Model
    - [@ohos.ability.ability (Ability)](js-apis-ability-ability.md)
    - [@ohos.ability.featureAbility (FeatureAbility)](js-apis-ability-featureAbility.md)
    - [@ohos.ability.particleAbility (ParticleAbility)](js-apis-ability-particleAbility.md)
  - Both Models (Recommended)
    - [@ohos.abilityAccessCtrl (Ability Access Control)](js-apis-abilityAccessCtrl.md)
    - [@ohos.ability.screenLockFileManager (Sensitive Data Access Management Under Lock Screen)](js-apis-screenLockFileManager.md)
    - [@ohos.app.ability.appManager (appManager)](js-apis-app-ability-appManager.md)
    - [@ohos.app.ability.appRecovery (appRecovery)](js-apis-app-ability-appRecovery.md)
    - [@ohos.app.ability.Configuration (Configuration)](js-apis-app-ability-configuration.md)
    - [@ohos.app.ability.ConfigurationConstant (ConfigurationConstant)](js-apis-app-ability-configurationConstant.md)
    - [@ohos.app.ability.dataUriUtils (DataUriUtils)](js-apis-app-ability-dataUriUtils.md)
    - [@ohos.app.ability.dialogRequest (dialogRequest)](js-apis-app-ability-dialogRequest.md)
    - [@ohos.app.ability.errorManager (ErrorManager)](js-apis-app-ability-errorManager.md)
    - [@ohos.app.ability.Want (Want)](js-apis-app-ability-want.md)
    - [@ohos.app.ability.wantAgent (WantAgent)](js-apis-app-ability-wantAgent.md)
    - [@ohos.app.ability.wantConstant (wantConstant)](js-apis-app-ability-wantConstant.md)
    - [@ohos.continuation.continuationManager (continuationManager)](js-apis-continuation-continuationManager.md)
    <!--Del-->
    - [@ohos.abilityAccessCtrl (Application Access Control) (System API)](js-apis-abilityAccessCtrl-sys.md)
    - [@ohos.ability.screenLockFileManager (Sensitive Data Access Management Under Lock Screen) (System API)](js-apis-screenLockFileManager-sys.md)
    - [@ohos.app.ability.abilityManager (AbilityManager) (System API)](js-apis-app-ability-abilityManager-sys.md)
    - [@ohos.app.ability.appManager (appManager) (System API)](js-apis-app-ability-appManager-sys.md)
    - [@ohos.app.ability.missionManager (missionManager) (System API)](js-apis-app-ability-missionManager-sys.md)
    - [@ohos.app.ability.quickFixManager (quickFixManager) (System API)](js-apis-app-ability-quickFixManager-sys.md)
    - [@ohos.app.ability.wantAgent (WantAgent) (System API)](js-apis-app-ability-wantAgent-sys.md)
    - [@ohos.app.ability.wantConstant (wantConstant) (System API)](js-apis-app-ability-wantConstant-sys.md)
    - [@ohos.app.businessAbilityRouter (Business Ability Router) (System API)](js-apis-businessAbilityRouter-sys.md)
    - [@ohos.application.uriPermissionManager (URI Permission Management) (System API)](js-apis-uripermissionmanager-sys.md)
    <!--DelEnd-->
    - [@ohos.bundle.bundleManager (bundleManager)](js-apis-bundleManager.md)
    - [@ohos.bundle.defaultAppManager (Default Application Management)](js-apis-defaultAppManager.md)
    - [@ohos.bundle.overlay (overlay)](js-apis-overlay.md)
    <!--Del-->
    - [@ohos.bundle.appControl (appControl Module) (System Interface)](js-apis-appControl-sys.md)
    - [@ohos.bundle.bundleManager (bundleManager) (System API)](js-apis-bundleManager-sys.md)
    - [@ohos.bundle.bundleMonitor (bundleMonitor) (System API)](js-apis-bundleMonitor-sys.md)
    - [@ohos.bundle.bundleResourceManager (bundleResourceManager) (System API)](js-apis-bundleResourceManager-sys.md)
    - [@ohos.bundle.defaultAppManager (Default Application Management) (System API)](js-apis-defaultAppManager-sys.md)
    - [@ohos.bundle.distributedBundleManager (distributedBundleManager) (System API)](js-apis-distributedBundleManager-sys.md)
    - [@ohos.bundle.freeInstall (freeInstall) (System API)](js-apis-freeInstall-sys.md)
    - [@ohos.bundle.installer (installer) (System API)](js-apis-installer-sys.md)
    - [@ohos.bundle.launcherBundleManager (launcherBundleManager module) (System API)](js-apis-launcherBundleManager-sys.md)
    - [@ohos.bundle.overlay (overlay) (System API)](js-apis-overlay-sys.md)
    - [@ohos.bundle.shortcutManager (shortcutManager) (System API)](js-apis-shortcutManager-sys.md)
    - [@ohos.distributedMissionManager (Distributed Mission Management) (System API)](js-apis-distributedMissionManager-sys.md)
    - [@ohos.privacyManager (Privacy Management) (System API)](js-apis-privacyManager-sys.md)
    <!--DelEnd-->
  - Dependent Elements and Definitions
    - ability
      - [abilityResult](js-apis-inner-ability-abilityResult.md)
      - [connectOptions](js-apis-inner-ability-connectOptions.md)
      - [dataAbilityHelper](js-apis-inner-ability-dataAbilityHelper.md)
      - [dataAbilityOperation](js-apis-inner-ability-dataAbilityOperation.md)
      - [dataAbilityResult](js-apis-inner-ability-dataAbilityResult.md)
      - [startAbilityParameter](js-apis-inner-ability-startAbilityParameter.md)
      - [want](js-apis-inner-ability-want.md)
    - app
      - [appVersionInfo](js-apis-inner-app-appVersionInfo.md)
      - [context](js-apis-inner-app-context.md)
      - [processInfo](js-apis-inner-app-processInfo.md)
    - application
      - [abilityMonitor](js-apis-inner-application-abilityMonitor.md)
      - [AbilityStageContext](js-apis-inner-application-abilityStageContext.md)
      - [abilityStageMonitor](js-apis-inner-application-abilityStageMonitor.md)
      - [AbilityStartCallback](js-apis-inner-application-abilityStartCallback.md)
      - [ApplicationContext](js-apis-inner-application-applicationContext.md)
      - [BaseContext](js-apis-inner-application-baseContext.md)
      - [Context](js-apis-inner-application-context.md)
      - [EmbeddableUIAbilityContext](js-apis-inner-application-EmbeddableUIAbilityContext.md)
      - [ErrorObserver](js-apis-inner-application-errorObserver.md)
      - [EventHub](js-apis-inner-application-eventHub.md)
      - [ExtensionContext](js-apis-inner-application-extensionContext.md)
      - [LoopObserver](js-apis-inner-application-loopObserver.md)
      - [ProcessInformation](js-apis-inner-application-processInformation.md)
      - [ProcessRunningInfo](js-apis-inner-application-processRunningInfo.md)
      - [UIAbilityContext](js-apis-inner-application-uiAbilityContext.md)
      - [UIExtensionContext](js-apis-inner-application-uiExtensionContext.md)
      - [PhotoEditorExtensionContext](js-apis-app-ability-photoEditorExtensionContext.md)
      - [SendableContext](js-apis-inner-application-sendableContext.md)
      <!--Del-->
      - [AbilityFirstFrameStateData (System API)](js-apis-inner-application-abilityFirstFrameStateData-sys.md)
      - [AbilityFirstFrameStateObserver (System API)](js-apis-inner-application-abilityFirstFrameStateObserver-sys.md)
      - [AbilityForegroundStateObserver (System API)](js-apis-inner-application-abilityForegroundStateObserver-sys)
      - [AbilityRunningInfo (System API)](js-apis-inner-application-abilityRunningInfo-sys.md)
      - [AbilityStateData (System API)](js-apis-inner-application-abilityStateData-sys.md)
      - [AppForegroundStateObserver (System API)](js-apis-inner-application-appForegroundStateObserver-sys.md)
      - [ApplicationContext (System API)](js-apis-inner-application-applicationContext-sys.md)
      - [AutoFillPopupConfig (System API)](js-apis-inner-application-autoFillPopupConfig-sys.md)
      - [ApplicationStateObserver (System API)](js-apis-inner-application-applicationStateObserver-sys.md)
      - [AppStateData (System API)](js-apis-inner-application-appStateData-sys.md)
      - [AutoFillExtensionContext (System API)](js-apis-inner-application-autoFillExtensionContext-sys.md)
      - [AutoFillRequest (System API)](js-apis-inner-application-autoFillRequest-sys.md)
      - [AutoFillType (System API)](js-apis-inner-application-autoFillType-sys.md)
      - [AutoStartupCallback (System API)](js-apis-inner-application-autoStartupCallback-sys.md)
      - [AutoStartupInfo (System API)](js-apis-inner-application-autoStartupInfo-sys.md)
      - [Context (System API)](js-apis-inner-application-context-sys.md)
      - [ContinuableInfo (System API)](js-apis-inner-application-continuableInfo-sys.md)
      - [ContinueCallback (System API)](js-apis-inner-application-continueCallback-sys.md)
      - [ContinueDeviceInfo (System API)](js-apis-inner-application-continueDeviceInfo-sys.md)
      - [ContinueMissionInfo (System API)](js-apis-inner-application-continueMissionInfo-sys.md)
      - [ExtensionRunningInfo (System API)](js-apis-inner-application-extensionRunningInfo-sys.md)
      - [MissionCallbacks (System API)](js-apis-inner-application-missionCallbacks-sys.md)
      - [MissionDeviceInfo (System API)](js-apis-inner-application-missionDeviceInfo-sys.md)
      - [MissionInfo (System API)](js-apis-inner-application-missionInfo-sys.md)
      - [MissionListener (System API)](js-apis-inner-application-missionListener-sys.md)
      - [MissionParameter (System API)](js-apis-inner-application-missionParameter-sys.md)
      - [MissionSnapshot (System API)](js-apis-inner-application-missionSnapshot-sys.md)
      - [MissionSnapshot (System API)](js-apis-inner-application-pageNodeInfo-sys.md)
      - [PageNodeInfo (System API)](js-apis-inner-application-pageNodeInfo-sys.md)
      - [ProcessData (System API)](js-apis-inner-application-processData-sys.md)
      - [ServiceExtensionContext (System API)](js-apis-inner-application-serviceExtensionContext-sys.md)
      - [UIAbilityContext (System API)](js-apis-inner-application-uiAbilityContext-sys.md)
      - [UIExtensionContext (System API)](js-apis-inner-application-uiExtensionContext-sys.md)
      - [ViewData (System API)](js-apis-inner-application-viewData-sys.md)
      - [AutoFillRect (System API)](js-apis-inner-application-autoFillRect-sys.md)
      <!--DelEnd-->
    - bundleManager
      - [abilityInfo](js-apis-bundleManager-abilityInfo.md)
      - [applicationInfo](js-apis-bundleManager-applicationInfo.md)
      - [bundleInfo](js-apis-bundleManager-bundleInfo.md)
      - [elementName](js-apis-bundleManager-elementName.md)
      - [extensionAbilityInfo](js-apis-bundleManager-extensionAbilityInfo.md)
      - [hapModuleInfo](js-apis-bundleManager-hapModuleInfo.md)
      - [metadata](js-apis-bundleManager-metadata.md)
      - [OverlayModuleInfo](js-apis-bundleManager-overlayModuleInfo.md)
      - [Skill](js-apis-bundleManager-skill.md)
      <!--Del-->
      - [ApplicationInfo (System API)](js-apis-bundleManager-ApplicationInfo-sys.md)
      - [AppProvisionInfo (System API)](js-apis-bundleManager-AppProvisionInfo-sys.md)
      - [BundleInfo (System API)](js-apis-bundleManager-BundleInfo-sys.md)
      - [BundlePackInfo (System API)](js-apis-bundleManager-BundlePackInfo-sys.md)
      - [BundleResourceInfo (System API)](js-apis-bundleManager-BundleResourceInfo-sys.md)
      - [BusinessAbilityInfo (System API)](js-apis-bundleManager-businessAbilityInfo-sys.md)
      - [dispatchInfo (System API)](js-apis-bundleManager-dispatchInfo-sys.md)
      - [launcherAbilityInfo (System API)](js-apis-bundleManager-launcherAbilityInfo-sys.md)
      - [LauncherAbilityResourceInfo (System API)](js-apis-bundleManager-LauncherAbilityResourceInfo-sys.md)
      - [permissionDef (System API)](js-apis-bundleManager-permissionDef-sys.md)
      - [recoverableApplicationInfo (System API)](js-apis-bundleManager-recoverableApplicationInfo-sys.md)
      - [remoteAbilityInfo (System API)](js-apis-bundleManager-remoteAbilityInfo-sys.md)
      - [SharedBundleInfo (System API)](js-apis-bundleManager-sharedBundleInfo-sys.md)
      - [shortcutInfo (System API)](js-apis-bundleManager-shortcutInfo-sys.md)
      <!--DelEnd-->
    - continuation
      - [continuationExtraParams](js-apis-continuation-continuationExtraParams.md)
      - [continuationResult](js-apis-continuation-continuationResult.md)
    - security
      - [PermissionRequestResult](js-apis-permissionrequestresult.md)
    - wantAgent
      - [triggerInfo](js-apis-inner-wantAgent-triggerInfo.md)
      - [wantAgentInfo](js-apis-inner-wantAgent-wantAgentInfo.md)
  - APIs No Longer Maintained
    - [@ohos.ability.dataUriUtils (DataUriUtils)](js-apis-ability-dataUriUtils.md)
    - [@ohos.ability.errorCode (ErrorCode)](js-apis-ability-errorCode.md)
    - [@ohos.ability.wantConstant (wantConstant)](js-apis-ability-wantConstant.md)
    - [@ohos.application.appManager (appManager)](js-apis-application-appManager.md)
    - [@ohos.application.Configuration (Configuration)](js-apis-application-configuration.md)
    - [@ohos.application.ConfigurationConstant (ConfigurationConstant)](js-apis-application-configurationConstant.md)
    - [@ohos.application.Want (Want)](js-apis-application-want.md)
    - [@ohos.wantAgent (WantAgent)](js-apis-wantAgent.md)
    <!--Del-->
    - [@ohos.ability.wantConstant (wantConstant) (System API)](js-apis-ability-wantConstant-sys.md)
    - [@ohos.application.abilityManager (AbilityManager) (System API)](js-apis-application-abilityManager-sys.md)
    - [@ohos.application.appManager (appManager) (System API)](js-apis-application-appManager-sys.md)
    - [@ohos.application.missionManager (missionManager) (System API)](js-apis-application-missionManager-sys.md)
    - [@ohos.wantAgent (WantAgent) (System API)](js-apis-wantAgent-sys.md)
    <!--DelEnd-->
    - [@ohos.bundle (Bundle)](js-apis-Bundle.md)
    <!--Del-->
    - [@ohos.bundle (Bundle) (System API)](js-apis-Bundle-sys.md)
    - [@ohos.bundle.innerBundleManager (innerBundleManager) (System API)](js-apis-Bundle-InnerBundleManager-sys.md)
    - [@ohos.distributedBundle (Distributed Bundle Management) (System API)](js-apis-Bundle-distributedBundle-sys.md)
    <!--DelEnd-->
    - [@system.package (Bundle Management)](js-apis-system-package.md)
    - bundle
      - [abilityInfo](js-apis-bundle-AbilityInfo.md)
      - [applicationInfo](js-apis-bundle-ApplicationInfo.md)
      - [bundleInfo](js-apis-bundle-BundleInfo.md)
      - [customizeData](js-apis-bundle-CustomizeData.md)
      - [elementName](js-apis-bundle-ElementName.md)
      - [hapModuleInfo](js-apis-bundle-HapModuleInfo.md)
      - [moduleInfo](js-apis-bundle-ModuleInfo.md)
      <!--Del-->
      - [bundleInstaller (System API)](js-apis-bundle-BundleInstaller-sys.md)
      - [bundleStatusCallback (System API)](js-apis-Bundle-BundleStatusCallback-sys.md)
      - [launcherAbilityInfo (System API)](js-apis-bundle-LauncherAbilityInfo-sys.md)
      - [PermissionDef (System API)](js-apis-bundle-PermissionDef-sys.md)
      - [remoteAbilityInfo (System API)](js-apis-bundle-remoteAbilityInfo-sys.md)
      - [shortcutInfo (System API)](js-apis-bundle-ShortcutInfo-sys.md)
      <!--DelEnd-->
- C APIs
  - Modules
    - [AbilityAccessControl](_ability_access_control.md)
    - [AbilityRuntime](_ability_runtime.md)
    - [Bundle](_bundle.md)
    - [ChildProcess](c-apis-ability-childprocess.md)
  - Header Files
    - [ability_access_control.h](ability__access__control_8h.md)
    - [ability_runtime_common.h](ability__runtime__common_8h.md)
    - [application_context.h](application__context_8h.md)
    - [context_constant.h](context__constant_8h.md)
    - [native_interface_bundle.h](native__interface__bundle.md)
    - [native_child_process.h](native__child__process_8h.md)
  - Structs
    - [OH_NativeBundle_ApplicationInfo](_o_h___native_bundle_application_info.md)
    - [OH_NativeBundle_ElementName](_o_h___native_bundle_element_name.md)
- Error Codes
  - [Ability Error Codes](errorcode-ability.md)
  - [Distributed Scheduler Error Codes](errorcode-DistributedSchedule.md)
  - [Bundle Error Codes](errorcode-bundle.md)
  - [Access Control Error Codes](errorcode-access-token.md)
  - [ohos.screenLockFileManager Error Codes](errorcode-screenLockFileManager.md)
