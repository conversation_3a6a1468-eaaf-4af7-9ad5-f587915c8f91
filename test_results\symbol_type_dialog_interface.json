[{"symbol_name": "AlertDialogParamWithOptions", "symbol_type": "interface", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Defines the dialog param with options. @interface AlertDialogParamWithOptions @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { AlertDialogParamWithOptions } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6333718}, {"symbol_name": "DismissDialogAction", "symbol_type": "interface", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Component dialog dismiss action. @interface DismissDialogAction @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 12", "import_statement": "import { DismissDialogAction } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.6149802}, {"symbol_name": "DismissDialogAction", "symbol_type": "interface", "module_name": "custom_dialog_controller", "is_default": false, "is_ets": false, "description": "Component dialog dismiss action. @interface DismissDialogAction @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 12", "import_statement": "import { DismissDialogAction } from 'custom_dialog_controller';", "parent_symbol": null, "is_nested": false, "score": 0.6149802}]