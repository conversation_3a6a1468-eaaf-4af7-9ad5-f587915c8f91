"""
ArkTS Formatter

This module provides formatting utilities for ArkTS query results.
It formats search results for different types of queries in a way that is
suitable for display to users or consumption by agents.
"""

import logging
import json
from typing import List, Dict, Any, Optional, Union

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSFormatter")


class ArkTSFormatter:
    """Formatter for ArkTS query results."""

    def __init__(self, max_description_length: int = 200, include_scores: bool = True):
        """Initialize the formatter.

        Args:
            max_description_length: Maximum length for descriptions in formatted output
            include_scores: Whether to include scores in formatted output
        """
        self.max_description_length = max_description_length
        self.include_scores = include_scores
        logger.info(f"ArkTSFormatter initialized with max_description_length={max_description_length}, include_scores={include_scores}")

    def format_results_for_agent(self, results: List[Dict[str, Any]], 
                               query_type: str = "general") -> str:
        """Format results for consumption by an agent.

        Args:
            results: List of search results
            query_type: Type of query ("component", "import_path", "general", etc.)

        Returns:
            Formatted results as a string
        """
        if not results:
            return "No results found."
        
        # Select appropriate formatter based on query type
        if query_type == "component":
            return self.format_component_results(results)
        elif query_type == "import_path":
            return self.format_import_path_results(results)
        elif query_type == "nested":
            return self.format_nested_results(results)
        else:
            return self.format_general_results(results)

    def format_component_results(self, results: List[Dict[str, Any]]) -> str:
        """Format component search results.

        Args:
            results: List of component search results

        Returns:
            Formatted results as a string
        """
        formatted = ["# Component Search Results\n"]
        
        for i, result in enumerate(results, 1):
            symbol_name = result.get('symbol_name', 'Unknown')
            symbol_type = result.get('symbol_type', 'Unknown')
            import_statement = result.get('import_statement', 'N/A')
            description = result.get('description', '')
            
            # Truncate description if needed
            if description and len(description) > self.max_description_length:
                description = description[:self.max_description_length] + "..."
            
            # Format result
            formatted.append(f"## {i}. {symbol_name}")
            formatted.append(f"**Type:** {symbol_type}")
            formatted.append(f"**Import:** `{import_statement}`")
            
            if self.include_scores and 'score' in result:
                formatted.append(f"**Relevance Score:** {result['score']:.4f}")
            
            if description:
                formatted.append(f"\n{description}\n")
            
            # Add usage example if it's a component
            if symbol_type.lower() == 'component':
                formatted.append("**Usage Example:**")
                formatted.append("```typescript")
                formatted.append(f"import {{ {symbol_name} }} from '{result.get('module_name', '')}'")
                formatted.append("")
                formatted.append("@Entry")
                formatted.append("@Component")
                formatted.append("struct MyComponent {")
                formatted.append("  build() {")
                formatted.append(f"    {symbol_name}()")
                formatted.append("  }")
                formatted.append("}")
                formatted.append("```\n")
            
            formatted.append("---\n")
        
        return "\n".join(formatted)

    def format_import_path_results(self, results: List[Dict[str, Any]]) -> str:
        """Format import path search results.

        Args:
            results: List of import path search results

        Returns:
            Formatted results as a string
        """
        formatted = ["# Import Path Search Results\n"]
        
        for i, result in enumerate(results, 1):
            symbol_name = result.get('symbol_name', 'Unknown')
            symbol_type = result.get('symbol_type', 'Unknown')
            import_statement = result.get('import_statement', 'N/A')
            module_name = result.get('module_name', 'Unknown')
            
            # Format result
            formatted.append(f"## {i}. {symbol_name}")
            formatted.append(f"**Type:** {symbol_type}")
            formatted.append(f"**Module:** {module_name}")
            formatted.append(f"**Import Statement:** `{import_statement}`")
            
            if self.include_scores and 'score' in result:
                formatted.append(f"**Relevance Score:** {result['score']:.4f}")
            
            # Add additional import information
            if result.get('is_default', False):
                formatted.append("\n**Note:** This is a default export.")
            
            if result.get('is_nested', False) and result.get('parent_symbol'):
                formatted.append(f"\n**Note:** This is nested within `{result['parent_symbol']}`.")
            
            if 'original_module' in result:
                formatted.append(f"\n**Note:** Re-exported from `{result['original_module']}`.")
            
            formatted.append("---\n")
        
        return "\n".join(formatted)

    def format_nested_results(self, results: List[Dict[str, Any]]) -> str:
        """Format nested symbol search results.

        Args:
            results: List of nested symbol search results

        Returns:
            Formatted results as a string
        """
        formatted = ["# Nested Symbol Search Results\n"]
        
        # Group by parent symbol
        grouped = {}
        for result in results:
            parent = result.get('parent_symbol', 'Unknown')
            if parent not in grouped:
                grouped[parent] = []
            grouped[parent].append(result)
        
        # Format each group
        for parent, group_results in grouped.items():
            formatted.append(f"## Nested in: {parent}\n")
            
            for i, result in enumerate(group_results, 1):
                symbol_name = result.get('symbol_name', 'Unknown')
                symbol_type = result.get('symbol_type', 'Unknown')
                full_name = result.get('full_name', f"{parent}.{symbol_name}")
                import_statement = result.get('import_statement', 'N/A')
                
                # Format result
                formatted.append(f"### {i}. {symbol_name}")
                formatted.append(f"**Type:** {symbol_type}")
                formatted.append(f"**Full Name:** {full_name}")
                formatted.append(f"**Import:** `{import_statement}`")
                
                if self.include_scores and 'score' in result:
                    formatted.append(f"**Relevance Score:** {result['score']:.4f}")
                
                description = result.get('description', '')
                if description and len(description) > self.max_description_length:
                    description = description[:self.max_description_length] + "..."
                
                if description:
                    formatted.append(f"\n{description}\n")
                
                formatted.append("---\n")
        
        return "\n".join(formatted)

    def format_general_results(self, results: List[Dict[str, Any]]) -> str:
        """Format general search results.

        Args:
            results: List of general search results

        Returns:
            Formatted results as a string
        """
        formatted = ["# ArkTS Search Results\n"]
        
        # Group by symbol type
        grouped = {}
        for result in results:
            symbol_type = result.get('symbol_type', 'Unknown')
            if symbol_type not in grouped:
                grouped[symbol_type] = []
            grouped[symbol_type].append(result)
        
        # Format each group
        for symbol_type, group_results in grouped.items():
            formatted.append(f"## {symbol_type.capitalize()} Results\n")
            
            for i, result in enumerate(group_results, 1):
                symbol_name = result.get('symbol_name', 'Unknown')
                import_statement = result.get('import_statement', 'N/A')
                module_name = result.get('module_name', 'Unknown')
                
                # Format result
                formatted.append(f"### {i}. {symbol_name}")
                formatted.append(f"**Module:** {module_name}")
                formatted.append(f"**Import:** `{import_statement}`")
                
                if self.include_scores and 'score' in result:
                    formatted.append(f"**Relevance Score:** {result['score']:.4f}")
                
                # Add nested information if applicable
                if result.get('is_nested', False) and result.get('parent_symbol'):
                    formatted.append(f"\n**Nested in:** `{result['parent_symbol']}`")
                
                description = result.get('description', '')
                if description and len(description) > self.max_description_length:
                    description = description[:self.max_description_length] + "..."
                
                if description:
                    formatted.append(f"\n{description}\n")
                
                formatted.append("---\n")
            
            formatted.append("\n")
        
        return "\n".join(formatted)

    def format_as_json(self, results: List[Dict[str, Any]], 
                     include_full_details: bool = False) -> str:
        """Format results as JSON.

        Args:
            results: List of search results
            include_full_details: Whether to include all details in the output

        Returns:
            Formatted results as a JSON string
        """
        if not include_full_details:
            # Create a simplified version with only essential fields
            simplified = []
            for result in results:
                simplified.append({
                    'symbol_name': result.get('symbol_name', 'Unknown'),
                    'symbol_type': result.get('symbol_type', 'Unknown'),
                    'import_statement': result.get('import_statement', 'N/A'),
                    'module_name': result.get('module_name', 'Unknown'),
                    'score': result.get('score', 0.0),
                    'description': result.get('description', '')[:self.max_description_length] 
                        if result.get('description') else ''
                })
            return json.dumps(simplified, indent=2)
        else:
            return json.dumps(results, indent=2)

    def format_as_markdown_table(self, results: List[Dict[str, Any]], 
                               fields: List[str] = None) -> str:
        """Format results as a Markdown table.

        Args:
            results: List of search results
            fields: List of fields to include in the table

        Returns:
            Formatted results as a Markdown table
        """
        if not results:
            return "No results found."
        
        # Default fields if none provided
        if fields is None:
            fields = ['symbol_name', 'symbol_type', 'import_statement', 'score']
        
        # Create table header
        header = "| " + " | ".join(field.replace('_', ' ').title() for field in fields) + " |"
        separator = "| " + " | ".join(['---'] * len(fields)) + " |"
        
        # Create table rows
        rows = []
        for result in results:
            row = []
            for field in fields:
                value = result.get(field, '')
                
                # Format specific fields
                if field == 'score' and isinstance(value, (int, float)):
                    value = f"{value:.4f}"
                elif field == 'description' and value and len(value) > 50:
                    value = value[:47] + "..."
                elif field == 'import_statement':
                    value = f"`{value}`"
                
                row.append(str(value))
            
            rows.append("| " + " | ".join(row) + " |")
        
        # Combine all parts
        return "\n".join([header, separator] + rows)


# Add main function for command-line usage
def main():
    """Main function for formatting."""
    import argparse
    import json
    import sys

    parser = argparse.ArgumentParser(description='ArkTS Formatter')
    parser.add_argument('--input', type=str, help='Input JSON file with search results')
    parser.add_argument('--output', type=str, help='Output file for formatted results')
    parser.add_argument('--format', type=str, choices=['markdown', 'json', 'table'], default='markdown',
                        help='Output format')
    parser.add_argument('--query-type', type=str, choices=['general', 'component', 'import_path', 'nested'],
                        default='general', help='Type of query')
    parser.add_argument('--max-description', type=int, default=200, help='Maximum description length')
    parser.add_argument('--include-scores', action='store_true', help='Include scores in output')
    parser.add_argument('--fields', type=str, help='Comma-separated list of fields for table format')

    args = parser.parse_args()

    # Initialize formatter
    formatter = ArkTSFormatter(
        max_description_length=args.max_description,
        include_scores=args.include_scores
    )

    # Read input
    if args.input:
        with open(args.input, 'r', encoding='utf-8') as f:
            results = json.load(f)
    else:
        # Read from stdin
        results = json.load(sys.stdin)

    # Format results
    if args.format == 'markdown':
        formatted = formatter.format_results_for_agent(results, args.query_type)
    elif args.format == 'json':
        formatted = formatter.format_as_json(results, include_full_details=True)
    elif args.format == 'table':
        fields = args.fields.split(',') if args.fields else None
        formatted = formatter.format_as_markdown_table(results, fields)

    # Write output
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            f.write(formatted)
    else:
        # Write to stdout
        print(formatted)


if __name__ == "__main__":
    main()
