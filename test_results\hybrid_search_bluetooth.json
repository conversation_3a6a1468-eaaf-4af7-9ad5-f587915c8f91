{"standard": [{"symbol_name": "of", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Base", "import_statement": "import { bluetooth.of } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.of", "score": 0.798483}, {"symbol_name": "BLE", "symbol_type": "namespace", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Provides methods to operate or manage Bluetooth. @namespace BLE @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.BLE", "import_statement": "import { bluetooth.BLE } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.BLE", "score": 0.7089386}, {"symbol_name": "bluetooth", "symbol_type": "namespace", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Provides methods to operate or manage Bluetooth. @namespace bluetooth @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager", "import_statement": "import { bluetooth } from '@ohos.bluetooth';", "parent_symbol": null, "is_nested": false, "score": 0.6977185}], "hybrid": [{"symbol_name": "BLE", "symbol_type": "namespace", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Provides methods to operate or manage Bluetooth. @namespace BLE @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.BLE", "import_statement": "import { bluetooth.BLE } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.BLE", "score": 0.7089386}, {"symbol_name": "bluetooth", "symbol_type": "namespace", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Provides methods to operate or manage Bluetooth. @namespace bluetooth @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager", "import_statement": "import { bluetooth } from '@ohos.bluetooth';", "parent_symbol": null, "is_nested": false, "score": 0.6977185}, {"symbol_name": "DeviceClass", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the class of a bluetooth device. @typedef DeviceClass @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.DeviceClass", "import_statement": "import { bluetooth.DeviceClass } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.DeviceClass", "score": 0.6738663}]}