[{"symbol_name": "BondStateParam", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the class of a bluetooth device. @typedef BondStateParam @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.BondStateParam", "import_statement": "import { bluetooth.BondStateParam } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.BondStateParam", "score": 0.0}, {"symbol_name": "GattService", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the Gatt service. @typedef GattService @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.GattService", "import_statement": "import { bluetooth.GattService } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.GattService", "score": 0.0}, {"symbol_name": "DescriptorWriteReq", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the parameters of the Gatt client's characteristic write request. @typedef DescriptorWriteReq @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.DescriptorWriteRequest", "import_statement": "import { bluetooth.DescriptorWriteReq } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.DescriptorWriteReq", "score": 0.0}]