# DevEco Studio User Guide



HUAWEI DevEco Studio for OpenHarmony (DevEco Studio) is a one-stop integrated development environment (IDE) powered by the IntelliJ IDEA Community Edition and oriented to OpenHarmony devices in all scenarios. It allows you to create project templates, and develop, build, debug, and release OpenHarmony applications from end to end.

[DevEco Studio](https://gitee.com/openharmony/docs/blob/master/en/release-notes/OpenHarmony-v4.0-release.md#version-mapping) stands out in the following aspects:

- **Efficient and intelligent code editing**: Code highlighting, intelligent code completion, code error check, automatic code navigation, code formatting, and code search for programming languages such as ArkTS, JavaScript, and C/C++.
- **Low-code development**: A diverse array of features to punch up your UI development productivity, including component drag and drop, visualized data binding, instant previewing, and low-code development for service widgets.
- **Multi-device bidirectional real-time preview**: Bidirectional preview, real-time preview, live preview, component preview, and multi-device preview of UI code to quickly view how your code runs on devices.
- **High-performance build system**: Hvigor, a compilation and building tool to compile and package applications/services in one-click mode, better supporting ArkTS/JS development.
- **One-stop information acquisition**: A one-stop information acquisition platform that takes into account the developer journey of learning, development, and help seeking, in order to facilitate developer activities.
- **Efficient code debugging**: Various debugging capabilities such as TS, JS, and C/C++ code breakpoint setting, single-step execution, and variable viewing, improving the efficiency of analyzing application/service issues.

For details about how to use the tool, see [DevEco Studio User Guide](https://developer.huawei.com/consumer/en/doc/harmonyos-guides-V2/deveco_overview-****************-V2).

