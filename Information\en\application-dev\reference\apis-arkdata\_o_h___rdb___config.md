# OH_Rdb_Config


## Overview

Defines the RDB store configuration.

**Since**: 10

**Related module**: [RDB](_r_d_b.md)


## Summary


### Member Variables

| Name| Description|
| -------- | -------- |
| [selfSize](_r_d_b.md#selfsize) | Size of the struct.|
| [dataBaseDir](_r_d_b.md#databasedir) | Path of the database file.|
| [storeName](_r_d_b.md#storename) | Name of the RDB store.|
| [bundleName](_r_d_b.md#bundlename) | Bundle name.|
| [moduleName](_r_d_b.md#modulename) | Module name. |
| [isEncrypt](_r_d_b.md#isencrypt) | Whether to encrypt the RDB store.|
| [securityLevel](_r_d_b.md#securitylevel) | Security level of the RDB store.|
| [area](_r_d_b.md#area)<sup>11+</sup> | Security area level. For details, see [Rdb_SecurityArea](_r_d_b.md#rdb_securityarea).|
