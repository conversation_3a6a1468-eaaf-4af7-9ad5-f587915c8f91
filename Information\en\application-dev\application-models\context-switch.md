# Context Switching


  | API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding API or Field in the Stage Model| 
| -------- | -------- | -------- |
| [getOrCreateLocalDir(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetorcreatelocaldir7)<br>[getOrCreateLocalDir():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetorcreatelocaldir7-1) | There is no corresponding API in the stage model.| Applications developed on the stage model do not have the operation permission in the application root directory. Therefore, no corresponding API is provided.|
| [verifyPermission(permission:string,options:PermissionOptions,callback:AsyncCallback&lt;number&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextverifypermission7)<br>[verifyPermission(permission:string,callback:AsyncCallback&lt;number&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextverifypermission7-1)<br>[verifyPermission(permission:string,options?:PermissionOptions):Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextverifypermission7-2) | \@ohos.abilityAccessCtrl.d.ts | [verifyAccessTokenSync(tokenID: number, permissionName: Permissions): GrantStatus;](../reference/apis-ability-kit/js-apis-abilityAccessCtrl.md#verifyaccesstokensync9)<br>[verifyAccessToken(tokenID: number, permissionName: Permissions): Promise&lt;GrantStatus&gt;;](../reference/apis-ability-kit/js-apis-abilityAccessCtrl.md#verifyaccesstoken9) |
| [requestPermissionsFromUser(permissions:Array&lt;string&gt;,requestCode:number,resultCallback:AsyncCallback&lt;PermissionRequestResult&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextrequestpermissionsfromuser7)<br>[requestPermissionsFromUser(permissions:Array&lt;string&gt;,requestCode:number):Promise&lt;PermissionRequestResult&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextrequestpermissionsfromuser7-1) | \@ohos.abilityAccessCtrl.d.ts | [requestPermissionsFromUser(context: Context, permissionList: Array&lt;Permissions&gt;, requestCallback: AsyncCallback&lt;PermissionRequestResult&gt;) : void;](../reference/apis-ability-kit/js-apis-abilityAccessCtrl.md#requestpermissionsfromuser9)<br>[requestPermissionsFromUser(context: Context, permissionList: Array&lt;Permissions&gt;) : Promise&lt;PermissionRequestResult&gt;;](../reference/apis-ability-kit/js-apis-abilityAccessCtrl.md#requestpermissionsfromuser9-1) |
| [getApplicationInfo(callback:AsyncCallback&lt;ApplicationInfo&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetapplicationinfo7)<br>[getApplicationInfo():Promise&lt;ApplicationInfo&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetapplicationinfo7-1) | application\Context.d.ts | [applicationInfo: ApplicationInfo;](../reference/apis-ability-kit/js-apis-inner-application-context.md#properties)|
| [getBundleName(callback : AsyncCallback&lt;string&gt;): void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetbundlename7)<br>[getBundleName(): Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetbundlename7-1) | application\UIAbilityContext.d.ts | [abilityInfo.bundleName: string;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#properties)|
| [getDisplayOrientation(callback : AsyncCallback&lt;bundle.DisplayOrientation&gt;): void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetdisplayorientation7)<br>[getDisplayOrientation(): Promise&lt;bundle.DisplayOrientation&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetdisplayorientation7-1) | \@ohos.screen.d.ts | [readonly orientation: Orientation;](../reference/apis-arkui/js-apis-screen-sys.md#orientation) |
| [setDisplayOrientation(orientation:bundle.DisplayOrientation, callback:AsyncCallback&lt;void&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextsetdisplayorientation7)<br>[setDisplayOrientation(orientation:bundle.DisplayOrientation):Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextsetdisplayorientation7-1) | \@ohos.screen.d.ts | [setOrientation(orientation: Orientation, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-arkui/js-apis-screen-sys.md#setorientation)<br>[setOrientation(orientation: Orientation): Promise&lt;void&gt;;](../reference/apis-arkui/js-apis-screen-sys.md#setorientation-1) |
| [setShowOnLockScreen(show:boolean, callback:AsyncCallback&lt;void&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextsetshowonlockscreendeprecated)<br>[setShowOnLockScreen(show:boolean):Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextsetshowonlockscreendeprecated-1) | \@ohos.window.d.ts | [setShowOnLockScreen(showOnLockScreen: boolean): void;](../reference/apis-arkui/js-apis-window-sys.md#setshowonlockscreen9) |
| [setWakeUpScreen(wakeUp:boolean, callback:AsyncCallback&lt;void&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextsetwakeupscreendeprecated)<br>[setWakeUpScreen(wakeUp:boolean):Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextsetwakeupscreendeprecated-1) | \@ohos.window.d.ts | [setWakeUpScreen(wakeUp: boolean): void;](../reference/apis-arkui/js-apis-window-sys.md#setwakeupscreen9) |
| [getProcessInfo(callback:AsyncCallback&lt;ProcessInfo&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetprocessinfo7)<br>[getProcessInfo():Promise&lt;ProcessInfo&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetprocessinfo7-1) | \@ohos.app.ability.abilityManager.d.ts | [getAbilityRunningInfos(callback: AsyncCallback&lt;Array&lt;AbilityRunningInfo&gt;&gt;): void;](../reference/apis-ability-kit/js-apis-app-ability-abilityManager-sys.md#getabilityrunninginfos)<br>[getAbilityRunningInfos(): Promise&lt;Array&lt;AbilityRunningInfo&gt;&gt;;](../reference/apis-ability-kit/js-apis-app-ability-abilityManager-sys.md#getabilityrunninginfos-1) |
| [getElementName(callback:AsyncCallback&lt;ElementName&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetelementname7)<br>[getElementName():Promise&lt;ElementName&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetelementname7-1) | application\UIAbilityContext.d.ts | [abilityInfo.name: string;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#properties)<br>[abilityInfo.bundleName: string;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#properties)|
| [getProcessName(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetprocessname7)<br>[getProcessName():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetprocessname7-1) | \@ohos.app.ability.abilityManager.d.ts | [getAbilityRunningInfos(callback: AsyncCallback&lt;Array&lt;AbilityRunningInfo&gt;&gt;): void;](../reference/apis-ability-kit/js-apis-app-ability-abilityManager-sys.md#getabilityrunninginfos)<br>[getAbilityRunningInfos(): Promise&lt;Array&lt;AbilityRunningInfo&gt;&gt;;](../reference/apis-ability-kit/js-apis-app-ability-abilityManager-sys.md#getabilityrunninginfos-1) |
| [getCallingBundle(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetcallingbundle7)<br>[getCallingBundle():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetcallingbundle7-1) | There is no corresponding API in the stage model.| Applications developed on the stage model can use the **ohos.aafwk.param.callerUid** parameter of **Want.parameters** to obtain the application information of the caller.|
| [getFilesDir(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetfilesdir)<br>[getFilesDir():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetfilesdir-1) | application\Context.d.ts | [filesDir: string;](../reference/apis-ability-kit/js-apis-inner-application-context.md#properties)|
| [getCacheDir(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetcachedir)<br>[getCacheDir():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetcachedir-1) | application\Context.d.ts | [cacheDir: string;](../reference/apis-ability-kit/js-apis-inner-application-context.md#properties)|
| [getOrCreateDistributedDir(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetorcreatedistributeddir7)<br>[getOrCreateDistributedDir():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetorcreatedistributeddir7-1) | application\Context.d.ts | [distributedFilesDir: string;](../reference/apis-ability-kit/js-apis-inner-application-context.md#properties)|
| [getAppType(callback:AsyncCallback&lt;string&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetapptype7)<br>[getAppType():Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetapptype7-1) | application\UIAbilityContext.d.ts | The stage model obtains the application type through the **type** attribute of the **abilityInfo** field.<br>[abilityInfo.type: bundleManager.AbilityType;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#properties)|
| [getHapModuleInfo(callback:AsyncCallback&lt;HapModuleInfo&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgethapmoduleinfo7)<br>[getHapModuleInfo():Promise&lt;HapModuleInfo&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgethapmoduleinfo7-1) | application\UIAbilityContext.d.ts | [currentHapModuleInfo: HapModuleInfo;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#properties)|
| [getAppVersionInfo(callback:AsyncCallback&lt;AppVersionInfo&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetappversioninfo7)<br>[getAppVersionInfo():Promise&lt;AppVersionInfo&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetappversioninfo7-1) | bundle\bundleInfo.d.ts | [readonly name: string;](../reference/apis-ability-kit/js-apis-bundleManager-bundleInfo.md#bundleinfo-1)<br>[readonly versionCode: number;](../reference/apis-ability-kit/js-apis-bundleManager-bundleInfo.md#bundleinfo-1)<br>[readonly versionName: string;](../reference/apis-ability-kit/js-apis-bundleManager-bundleInfo.md#bundleinfo-1) |
| [getApplicationContext():Context;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetapplicationcontext7) | application\Context.d.ts | [getApplicationContext(): ApplicationContext;](../reference/apis-ability-kit/js-apis-inner-application-context.md#contextgetapplicationcontext) |
| [getAbilityInfo(callback:AsyncCallback&lt;AbilityInfo&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetabilityinfo7)<br>[getAbilityInfo():Promise&lt;AbilityInfo&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextgetabilityinfo7-1) | application\UIAbilityContext.d.ts | [abilityInfo: AbilityInfo;](../reference/apis-ability-kit/js-apis-inner-application-uiAbilityContext.md#properties)|
| [isUpdatingConfigurations(callback:AsyncCallback&lt;boolean&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextisupdatingconfigurations7)<br>[isUpdatingConfigurations():Promise&lt;boolean&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextisupdatingconfigurations7-1) | There is no corresponding API in the stage model.| Applications do not restart when the system environment changes. The **onConfigurationUpdated** callback is invoked to notify the applications of the changes. This API provides an empty implementation in the FA model, and the stage model does not provide a corresponding API.|
| [printDrawnCompleted(callback:AsyncCallback&lt;void&gt;):void;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextprintdrawncompleted7)<br>[printDrawnCompleted():Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-app-context.md#contextprintdrawncompleted7-1) | There is no corresponding API in the stage model.| This API provides an empty implementation in the FA model. The stage model does not provide a corresponding API.|
