/**
 * Provides a dialog component.
 * @component Dialog
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
@Component
export struct Dialog {
  /**
   * Creates a dialog component.
   * @param options Dialog options.
   */
  constructor(options?: DialogComponentOptions);

  /**
   * Sets the dialog title.
   * @param title Dialog title.
   * @returns This Dialog component.
   */
  title(title: string): Dialog;

  /**
   * Sets the dialog content.
   * @param content Dialog content.
   * @returns This Dialog component.
   */
  content(content: string): Dialog;

  /**
   * Sets the dialog alignment.
   * @param alignment Dialog alignment.
   * @returns This Dialog component.
   */
  alignment(alignment: DialogAlignment): Dialog;

  /**
   * Sets the dialog offset.
   * @param offset Dialog offset.
   * @returns This Dialog component.
   */
  offset(offset: { x: number, y: number }): Dialog;

  /**
   * Sets the dialog buttons.
   * @param buttons Dialog buttons.
   * @returns This Dialog component.
   */
  buttons(buttons: Array<DialogButton>): Dialog;

  /**
   * Sets whether the dialog is cancelable.
   * @param cancelable Whether the dialog is cancelable.
   * @returns This Dialog component.
   */
  cancelable(cancelable: boolean): Dialog;

  /**
   * Sets the dialog cancel event handler.
   * @param callback Dialog cancel event handler.
   * @returns This Dialog component.
   */
  onCancel(callback: () => void): Dialog;

  /**
   * Sets the dialog dismiss event handler.
   * @param callback Dialog dismiss event handler.
   * @returns This Dialog component.
   */
  onDismiss(callback: () => void): Dialog;
}

/**
 * Defines the dialog component options.
 * @interface DialogComponentOptions
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
interface DialogComponentOptions {
  /**
   * Dialog title.
   */
  title?: string;

  /**
   * Dialog content.
   */
  content?: string;

  /**
   * Dialog alignment.
   */
  alignment?: DialogAlignment;

  /**
   * Dialog offset.
   */
  offset?: { x: number, y: number };

  /**
   * Dialog buttons.
   */
  buttons?: Array<DialogButton>;

  /**
   * Whether the dialog is cancelable.
   */
  cancelable?: boolean;

  /**
   * Dialog cancel event handler.
   */
  onCancel?: () => void;

  /**
   * Dialog dismiss event handler.
   */
  onDismiss?: () => void;
}

/**
 * Defines the dialog button.
 * @interface DialogButton
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
interface DialogButton {
  /**
   * Button text.
   */
  text: string;

  /**
   * Button color.
   */
  color?: string;

  /**
   * Button click event handler.
   */
  onClick?: () => void;
}

/**
 * The alignment of dialog,
 * @enum { number }
 * @syscap SystemCapability.ArkUI.ArkUI.Full
 * @crossplatform
 * @since 7
 */
declare enum DialogComponentAlignment {
  /**
   * Top alignment.
   */
  Top,

  /**
   * Center alignment.
   */
  Center,

  /**
   * Bottom alignment.
   */
  Bottom,

  /**
   * Top start alignment.
   */
  TopStart,

  /**
   * Top end alignment.
   */
  TopEnd,

  /**
   * Center start alignment.
   */
  CenterStart,

  /**
   * Center end alignment.
   */
  CenterEnd,

  /**
   * Bottom start alignment.
   */
  BottomStart,

  /**
   * Bottom end alignment.
   */
  BottomEnd
}

export { Dialog, DialogComponentOptions, DialogButton, DialogComponentAlignment };
