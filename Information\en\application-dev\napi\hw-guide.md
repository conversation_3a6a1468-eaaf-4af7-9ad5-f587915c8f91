# Introduction to Hardware Compatibility


When you use C or C++ to develop OpenHarmony native libraries, hardware matters. OpenHarmony can run on devices of different architectures and from a variety of vendors. It is a great challenge to ensure the compatibility and consistent experience of applications that use OpenHarmony native libraries on different devices.


This section describes the OpenHarmony Application Binary Interfaces (ABIs). It defines the [ABI standards](ohos-abi.md) supported by OpenHarmony. It also describes how to use the [CPU features](cpu-features.md) in your applications without compromising compatibility. Finally, it provides an example to demonstrate how to better use [ARM Neon extensions](neon-guide.md).
