"""
Stress Test for ArkTS Import Suggestion System

This module stress tests the ArkTS import suggestion system by running
a large number of queries in parallel and measuring performance.
"""

import unittest
import logging
import time
import random
import string
import multiprocessing
from typing import List, Dict, Any
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the query classes
from arkts_query import ArkTSQuery
from arkts_query_cached import ArkTSQueryCached
from arkts_query_enhanced import ArkTSQueryEnhanced
from arkts_query_async import ArkTSQueryAsync

# Import utility classes
from async_manager import AsyncManager
from performance_optimizer import PerformanceOptimizer

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TestStress")


class TestStress(unittest.TestCase):
    """Stress test the ArkTS import suggestion system."""

    def setUp(self):
        """Set up the test environment."""
        # Initialize query classes
        self.query = ArkTSQuery()
        self.cached_query = ArkTSQueryCached()
        self.enhanced_query = ArkTSQueryEnhanced()
        self.async_query = ArkTSQueryAsync()

        # Initialize utility classes
        self.async_mgr = AsyncManager()
        self.perf_opt = PerformanceOptimizer(
            cache_size=10000,
            cache_ttl=3600,
            batch_size=10,
            num_threads=multiprocessing.cpu_count()
        )

        # Test queries
        self.common_queries = [
            "button",
            "audio",
            "http",
            "dialog",
            "bluetooth",
            "image",
            "video",
            "text",
            "input",
            "slider"
        ]

        # Generate random queries
        self.random_queries = self._generate_random_queries(100)

        # All queries
        self.all_queries = self.common_queries + self.random_queries

    def _generate_random_queries(self, count: int) -> List[str]:
        """Generate random queries.

        Args:
            count: Number of queries to generate

        Returns:
            List of random queries
        """
        queries = []
        for _ in range(count):
            # Generate a random query of length 3-10
            length = random.randint(3, 10)
            query = ''.join(random.choice(string.ascii_lowercase) for _ in range(length))
            queries.append(query)
        return queries

    def test_high_volume_queries(self):
        """Test high volume of queries."""
        # Process all queries
        start_time = time.time()
        results = []
        for query in self.all_queries:
            results.append(self.query.suggest_imports(query))
        sequential_time = time.time() - start_time

        # Check results
        self.assertEqual(len(results), len(self.all_queries))

        # Log performance
        logger.info(f"Sequential processing of {len(self.all_queries)} queries took {sequential_time:.4f}s")
        logger.info(f"Average time per query: {sequential_time / len(self.all_queries):.4f}s")

    def test_parallel_high_volume_queries(self):
        """Test high volume of queries in parallel."""
        # Process all queries in parallel
        start_time = time.time()
        results = self.perf_opt.parallel_process(
            func=self.query.suggest_imports,
            items=self.all_queries
        )
        parallel_time = time.time() - start_time

        # Check results
        self.assertEqual(len(results), len(self.all_queries))

        # Log performance
        logger.info(f"Parallel processing of {len(self.all_queries)} queries took {parallel_time:.4f}s")
        logger.info(f"Average time per query: {parallel_time / len(self.all_queries):.4f}s")

    def test_cached_high_volume_queries(self):
        """Test high volume of queries with caching."""
        # Clear cache
        self.cached_query.clear_cache()

        # First run (cache miss)
        start_time = time.time()
        first_results = []
        for query in self.all_queries:
            first_results.append(self.cached_query.suggest_imports(query))
        first_run_time = time.time() - start_time

        # Second run (cache hit)
        start_time = time.time()
        second_results = []
        for query in self.all_queries:
            second_results.append(self.cached_query.suggest_imports(query))
        second_run_time = time.time() - start_time

        # Check results
        self.assertEqual(len(first_results), len(self.all_queries))
        self.assertEqual(len(second_results), len(self.all_queries))

        # Log performance
        logger.info(f"First run (cache miss) of {len(self.all_queries)} queries took {first_run_time:.4f}s")
        logger.info(f"Second run (cache hit) of {len(self.all_queries)} queries took {second_run_time:.4f}s")
        logger.info(f"Cache speedup: {first_run_time / max(second_run_time, 0.0001):.2f}x")

    def test_sequential_vs_parallel(self):
        """Test sequential vs parallel processing."""
        # Process queries sequentially
        start_time = time.time()
        sequential_results = []
        for query in self.common_queries:  # Use only common queries for this test
            sequential_results.append(self.query.suggest_imports(query))
        sequential_time = time.time() - start_time

        # Process queries in parallel
        start_time = time.time()
        parallel_results = self.perf_opt.parallel_process(
            func=self.query.suggest_imports,
            items=self.common_queries
        )
        parallel_time = time.time() - start_time

        # Check results
        self.assertEqual(len(parallel_results), len(self.common_queries))

        # Log performance
        logger.info(f"Sequential processing of {len(self.common_queries)} queries took {sequential_time:.4f}s")
        logger.info(f"Parallel processing of {len(self.common_queries)} queries took {parallel_time:.4f}s")
        logger.info(f"Parallel speedup: {sequential_time / max(parallel_time, 0.0001):.2f}x")

    def test_batch_processing(self):
        """Test batch processing performance."""
        # Define batch processing function
        def process_batch(batch):
            results = []
            for query in batch:
                results.append(self.query.suggest_imports(query))
            return results

        # Process queries in batches
        start_time = time.time()
        batch_results = self.perf_opt.batch_process(
            func=process_batch,
            items=self.all_queries,
            adaptive_sizing=True
        )
        batch_time = time.time() - start_time

        # Check results
        self.assertEqual(len(batch_results), len(self.all_queries))

        # Log performance
        logger.info(f"Batch processing of {len(self.all_queries)} queries took {batch_time:.4f}s")
        logger.info(f"Average time per query: {batch_time / len(self.all_queries):.4f}s")

    def test_parallel_batch_processing(self):
        """Test parallel batch processing performance."""
        # Define batch processing function
        def process_batch(batch):
            results = []
            for query in batch:
                results.append(self.query.suggest_imports(query))
            return results

        # Process queries in parallel batches
        start_time = time.time()
        parallel_batch_results = self.perf_opt.parallel_batch_process(
            func=process_batch,
            items=self.all_queries
        )
        parallel_batch_time = time.time() - start_time

        # Check results
        self.assertEqual(len(parallel_batch_results), len(self.all_queries))

        # Log performance
        logger.info(f"Parallel batch processing of {len(self.all_queries)} queries took {parallel_batch_time:.4f}s")
        logger.info(f"Average time per query: {parallel_batch_time / len(self.all_queries):.4f}s")

    def test_memory_usage(self):
        """Test memory usage."""
        import psutil
        import os

        # Get current process
        process = psutil.Process(os.getpid())

        # Measure memory usage before
        memory_before = process.memory_info().rss / 1024 / 1024  # MB

        # Run a smaller number of queries to avoid connection issues
        try:
            for query in self.common_queries:  # Use only common queries
                self.query.suggest_imports(query)
        except Exception as e:
            logger.warning(f"Error during memory test: {str(e)}")
            logger.warning("This is expected if the server is overloaded")

        # Measure memory usage after
        memory_after = process.memory_info().rss / 1024 / 1024  # MB

        # Log memory usage
        logger.info(f"Memory usage before: {memory_before:.2f} MB")
        logger.info(f"Memory usage after: {memory_after:.2f} MB")
        logger.info(f"Memory usage increase: {memory_after - memory_before:.2f} MB")


if __name__ == "__main__":
    # Create a test suite with only the tests we want to run
    suite = unittest.TestSuite()

    # Add only the memory usage test
    suite.addTest(TestStress('test_memory_usage'))

    # Run the test suite
    runner = unittest.TextTestRunner()
    runner.run(suite)
