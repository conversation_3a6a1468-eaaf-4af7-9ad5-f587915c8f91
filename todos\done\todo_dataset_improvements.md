# Veri Seti Genişletme ve İndeksleme Stratejileri Planı

## Mevcut Durum Analizi
- Sınırl<PERSON> sayıda ArkTS dosyası indekslendi
- Qdrant'ın yükleme sınırlamaları nedeniyle büyük veri setleri indekslenemiyor
- İndeksleme performansı optimize edilmemiş

## İyileştirme Adımları

### 1. Kapsamlı Veri Seti Oluşturma ✅ COMPLETED
- [x] Tüm ArkTS API'lerini içeren kapsamlı bir veri seti oluştur ✅
  - [x] OpenHarmony API'lerini ekle ✅
  - [x] HMS (Huawei Mobile Services) API'lerini ekle ✅
  - [x] Üçüncü taraf kütüphaneleri ekle ✅
  - [x] Özel komponentleri ekle ✅

### 2. Veri Seti Kategorilendirme ✅ COMPLETED
- [x] Veri setini kategorilere ayır ✅
  - [x] UI Komponentleri (Button, Text, Image, vb.) ✅
  - [x] Layout Komponentleri (Column, Row, Grid, vb.) ✅
  - [x] Sistem API'leri (Bluetooth, HTTP, Audio, vb.) ✅
  - [x] Veri Yönetimi API'leri (Database, Storage, vb.) ✅
  - [x] Medya API'leri (Camera, Video, Audio, vb.) ✅
  - [x] Sensör API'leri (Accelerometer, Gyroscope, vb.) ✅

### 3. Qdrant Yükleme Sınırlamalarını Aşma [TAMAMLANDI]
- [x] Batch boyutunu küçülterek Qdrant'ın JSON payload boyutu sınırlamasını aş
- [x] Büyük veri setlerini daha küçük parçalara böl
- [x] İndeksleme işlemini daha küçük batch'ler halinde yap

### 4. İndeksleme Performansını İyileştirme ✅ COMPLETED
- [x] İndeksleme işlemini paralelleştir ✅
  - [x] Çoklu iş parçacığı (multi-threading) kullan ✅
  - [x] İş parçacığı havuzu (thread pool) oluştur ✅
  - [x] İş parçacığı sayısını yapılandırılabilir hale getir ✅
- [x] İndeksleme işlemini daha verimli hale getir ✅
  - [x] Dosya okuma işlemlerini optimize et ✅
  - [x] Bellek kullanımını optimize et ✅
  - [x] Disk I/O işlemlerini optimize et ✅

### 5. İndeksleme Stratejilerini İyileştirme ✅ COMPLETED
- [x] Daha gelişmiş indeksleme stratejileri uygula ✅
  - [x] Payload şemalarını açıkça belirt ✅
  - [x] İndeksleme parametrelerini optimize et ✅
  - [x] İndeksleme işlemini daha sağlam hale getir ✅

### 6. İndeksleme İşleminde İlerleme Göstergeleri ✅ COMPLETED
- [x] İndeksleme işleminde ilerleme göstergeleri ekle ✅
  - [x] Terminal çıktısına ilerleme çubuğu ekle ✅
  - [x] İndeksleme işleminin tahmini süresini göster ✅
  - [x] İndeksleme işleminin mevcut durumunu göster ✅

### 7. İndeksleme İşleminde Hata Yönetimi ✅ COMPLETED
- [x] İndeksleme işleminde hata yönetimini iyileştir ✅
  - [x] Hata durumunda kaldığı yerden devam et ✅
  - [x] Hata durumunda otomatik olarak yeniden dene ✅
  - [x] Hata durumunda detaylı log tut ✅

## Örnek Kod

```python
class ParallelIndexer:
    """Parallel indexer for ArkTS files."""

    def __init__(self, directory, num_threads=4, batch_size=100):
        """Initialize the parallel indexer.

        Args:
            directory: Directory to index
            num_threads: Number of threads to use
            batch_size: Batch size for indexing
        """
        self.directory = directory
        self.num_threads = num_threads
        self.batch_size = batch_size
        self.files = []
        self.symbols = []
        self.progress = 0
        self.total_files = 0

    def find_files(self):
        """Find all ArkTS files in the directory."""
        self.files = []
        for root, _, files in os.walk(self.directory):
            for file in files:
                if file.endswith(('.d.ts', '.d.ets')):
                    self.files.append(os.path.join(root, file))
        self.total_files = len(self.files)
        logger.info(f"Found {self.total_files} files to index")

    def process_file(self, file):
        """Process a single file and extract symbols.

        Args:
            file: File to process

        Returns:
            List of symbols extracted from the file
        """
        try:
            with open(file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Extract symbols from the file
            symbols = extract_symbols(content, file)

            # Update progress
            with self.lock:
                self.progress += 1
                progress_percent = (self.progress / self.total_files) * 100
                sys.stdout.write(f"\rIndexing progress: {progress_percent:.2f}% ({self.progress}/{self.total_files})")
                sys.stdout.flush()

            return symbols
        except Exception as e:
            logger.error(f"Error processing file {file}: {str(e)}")
            return []

    def index_files(self):
        """Index all files in parallel."""
        self.find_files()
        self.symbols = []
        self.progress = 0
        self.lock = threading.Lock()

        # Create a thread pool
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            # Submit all files for processing
            future_to_file = {executor.submit(self.process_file, file): file for file in self.files}

            # Process results as they complete
            for future in concurrent.futures.as_completed(future_to_file):
                file = future_to_file[future]
                try:
                    symbols = future.result()
                    self.symbols.extend(symbols)
                except Exception as e:
                    logger.error(f"Error processing file {file}: {str(e)}")

        logger.info(f"\nExtracted {len(self.symbols)} symbols from {self.total_files} files")
        return self.symbols

    def upload_symbols(self, client, collection_name):
        """Upload symbols to Qdrant in batches.

        Args:
            client: Qdrant client
            collection_name: Collection name

        Returns:
            Number of symbols uploaded
        """
        total_symbols = len(self.symbols)
        logger.info(f"Uploading {total_symbols} symbols to Qdrant")

        # Split symbols into batches
        batches = [self.symbols[i:i + self.batch_size] for i in range(0, total_symbols, self.batch_size)]
        total_batches = len(batches)

        # Upload batches
        for i, batch in enumerate(batches):
            try:
                # Convert symbols to points
                points = []
                for symbol in batch:
                    # Create point
                    point = {
                        'id': str(uuid.uuid4()),
                        'vector': symbol['vector'],
                        'payload': {
                            'symbol_name': symbol['symbol_name'],
                            'symbol_type': symbol['symbol_type'],
                            'module_name': symbol['module_name'],
                            'import_statement': symbol['import_statement'],
                            'description': symbol['description'],
                            'parent_symbol': symbol['parent_symbol'],
                            'file_path': symbol['file_path'],
                        }
                    }
                    points.append(point)

                # Upload batch
                client.upsert(
                    collection_name=collection_name,
                    points=points,
                    wait=True
                )

                # Update progress
                progress_percent = ((i + 1) / total_batches) * 100
                sys.stdout.write(f"\rUploading progress: {progress_percent:.2f}% ({i + 1}/{total_batches})")
                sys.stdout.flush()

            except Exception as e:
                logger.error(f"Error uploading batch {i + 1}/{total_batches}: {str(e)}")
                # Retry with smaller batch size
                if len(batch) > 1:
                    logger.info(f"Retrying with smaller batch size")
                    half_size = len(batch) // 2
                    first_half = batch[:half_size]
                    second_half = batch[half_size:]

                    # Upload first half
                    self.upload_batch(client, collection_name, first_half, f"{i + 1}.1/{total_batches}")

                    # Upload second half
                    self.upload_batch(client, collection_name, second_half, f"{i + 1}.2/{total_batches}")
                else:
                    logger.error(f"Failed to upload symbol: {batch[0]['symbol_name']}")

        logger.info(f"\nUploaded {total_symbols} symbols to Qdrant")
        return total_symbols
```

## Beklenen Sonuçlar
- Daha kapsamlı bir veri seti oluşturulacak
- Veri seti kategorilere ayrılacak
- Qdrant'ın yükleme sınırlamaları aşılacak
- İndeksleme performansı iyileşecek
- İndeksleme stratejileri daha gelişmiş olacak
- İndeksleme işleminde ilerleme göstergeleri eklenecek
- İndeksleme işleminde hata yönetimi iyileşecek
