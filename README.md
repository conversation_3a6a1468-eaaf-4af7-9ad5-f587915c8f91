# ArkTS Import Estimator

ArkTS Import Estimator is a tool that suggests import statements for the ArkTS programming language. This tool analyzes source code and documentation to recommend appropriate import statements for a given query.

## Features

- **Comprehensive ArkTS Support**: Supports all ArkTS features:
  - Nested imports
  - Nested classes
  - Nested namespaces
  - Reimport
  - Reexport
  - Type declarations

- **Advanced Search Capabilities**:
  - Vector-based semantic search
  - Text + vector hybrid search
  - Filtering by symbol type
  - Nested symbol search
  - Agent-based natural language queries

- **Qdrant Integration**: Uses Qdrant database for vector and hybrid search.

- **Ollama Integration**: Uses Ollama for generating embeddings.

- **High Performance Optimizations**:
  - Parallel processing with multi-threading support
  - Efficient batch processing for fast indexing
  - Automatic CPU optimization
  - Detailed progress indicators and performance metrics
  - Parallel query processing for batch operations

- **Compatibility Layer**:
  - Support for different Qdrant API versions
  - Graceful fallback mechanisms

## Installation

1. Install required packages:

```bash
pip install -r requirements.txt
```

2. Access the Qdrant database. By default, the system connects to the Qdrant server at `http://gmktec.ai-institute.uk:6333`.

3. Install and run Ollama:
   - [Download and install Ollama](https://ollama.com/download)
   - Pull the embedding model: `ollama pull mxbai-embed-large`

## Usage

### Indexing

To index ArkTS files:

```bash
python arkts_indexer.py --directory "Information/default/openharmony/ets/api"
```

To completely rebuild the database:

```bash
python arkts_indexer.py --directory "Information/default/openharmony/ets/api" --force-recreate
```

For maximum speed indexing of specific folders:

```bash
python index_specific_folders.py
```

You can quickly index large datasets using high-performance indexing, multi-threading, and batch processing. The system automatically optimizes based on CPU count and displays detailed progress.

### Querying

To query for import suggestions:

```bash
python arkts_query.py --query "Button"
```

To use hybrid search:

```bash
python arkts_query.py --query "Button" --hybrid
```

To filter by symbol type:

```bash
python arkts_query.py --query "Button" --type "component"
```

To search for nested symbols:

```bash
python arkts_query.py --nested "UIAbility"
```

To process multiple queries in parallel:

```bash
python arkts_query.py --queries "Button" "Text" "Image" --batch --parallel
```

To use agent-based natural language queries:

```bash
python arkts_query.py --agent-queries "ArkTS search: 'Button component'" --parallel
```

## Configuration

All settings are in the `config.py` file. You can edit this file to change default settings:

- Qdrant server URL
- Qdrant collection name
- Ollama server URL
- Embedding model
- Vector dimensions
- Retry settings
- Search settings
- Logging settings
- Thread pool configuration
- Batch processing settings

## Examples

### Example 1: Import Suggestions for Button Component

```bash
python arkts_query.py --query "Button"
```

Output:

```
Found 5 suggestions for 'Button':

1. Button (component)
   Import: import Button from '@ohos.arkui.componentModules'
   Score: 0.9876

2. ButtonType (enum)
   Import: import { ButtonType } from '@ohos.arkui.componentModules'
   Score: 0.8765
```

### Example 2: Import Suggestions for Context Class

```bash
python arkts_query.py --query "Context"
```

Output:

```
Found 3 suggestions for 'Context':

1. Context (class)
   Import: import Context from '@ohos.app.ability.common'
   Score: 0.9543

2. ApplicationContext (class)
   Import: import ApplicationContext from '@ohos.app.ability.common'
   Score: 0.8765
```

### Example 3: Batch Processing with Parallel Execution

```bash
python arkts_query.py --queries "Button" "Text" "Image" --batch --parallel
```

Output:

```
Processing 3 queries in parallel...
✓ Completed query: Button (0.45s)
✓ Completed query: Text (0.52s)
✓ Completed query: Image (0.48s)

Results for 'Button':
1. Button (component)
   Import: import Button from '@ohos.arkui.componentModules'
   Score: 0.9876

Results for 'Text':
1. Text (component)
   Import: import Text from '@ohos.arkui.componentModules'
   Score: 0.9765

Results for 'Image':
1. Image (component)
   Import: import Image from '@ohos.arkui.componentModules'
   Score: 0.9654
```

## Command Line Parameters

### Indexing Parameters

- `--qdrant-url`: Qdrant server URL
- `--collection`: Qdrant collection name
- `--ollama-url`: Ollama server URL
- `--embedding-model`: Ollama embedding model to use
- `--directory`: Directory to index (required)
- `--force-recreate`: Recreate collection even if it already exists (completely resets the database)

### Query Parameters

- `--qdrant-url`: Qdrant server URL
- `--collection`: Qdrant collection name
- `--ollama-url`: Ollama server URL
- `--embedding-model`: Ollama embedding model to use
- `--query`: Query for import suggestions
- `--queries`: Multiple queries for batch processing
- `--agent-queries`: Natural language queries for agent-based processing
- `--limit`: Maximum number of results
- `--hybrid`: Enable hybrid search
- `--type`: Filter by symbol type (e.g., class, interface, namespace)
- `--nested`: Search for nested symbols within a parent symbol
- `--batch`: Enable batch processing for multiple queries
- `--parallel`: Enable parallel processing for batch operations

## Project Structure

- `config.py`: Contains configuration settings
- `arkts_indexer.py`: Handles indexing operations
- `arkts_query.py`: Handles query operations
- `component_cards.py`: Contains ArkTS symbol parser class
- `qdrant_compatibility.py`: Provides compatibility layer for different Qdrant API versions
- `index_specific_folders.py`: Script for indexing specific folders with maximum speed
- `clear_and_index_fixed.py`: Script for clearing the database and re-indexing
- `test_codes/`: Contains test files
- `test_results/`: Contains test results

## Embedding Models

The system uses Ollama for generating embeddings. The default model is `mxbai-embed-large`, a state-of-the-art large embedding model that outperforms many commercial models.

Other recommended models:
- `nomic-embed-text`: A high-performance model with a large token context window
- `all-minilm`: A smaller and faster model

To use a different model:

```bash
python arkts_query.py --query "Button" --embedding-model "nomic-embed-text"
```

## Performance Optimization

The system includes several performance optimizations:

- **Parallel Processing**: Uses multi-threading for both indexing and querying operations
- **Batch Processing**: Efficiently processes multiple items in batches
- **Thread Pool Management**: Dynamically adjusts thread count based on workload
- **Qdrant Compatibility**: Handles different Qdrant API versions with graceful fallbacks
- **Efficient Embedding Generation**: Optimizes embedding generation with parallel processing
- **Progress Tracking**: Provides detailed progress indicators and performance metrics
