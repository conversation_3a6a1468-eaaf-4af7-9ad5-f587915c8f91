# @ohos.app.ability.insightIntent (insightIntent) (System API)

The **insightIntent** module provides APIs for InsightIntent calls.

> **NOTE**
>
> The initial APIs of this module are supported since API version 11. Newly added APIs will be marked with a superscript to indicate their earliest API version.
>
> The APIs of this module can be used only in the stage model.
>
> This topic describes only system APIs provided by the module. For details about its public APIs, see [@ohos.app.ability.insightIntent (insightIntent)](js-apis-app-ability-insightIntent.md).

## Modules to Import

```ts
import { insightIntent } from '@kit.AbilityKit';
```

## ExecuteMode

Enumerates the InsightIntent call execution modes.

**System capability**: SystemCapability.Ability.AbilityRuntime.Core

| Name| Value| Description|
| -------- | -------- | -------- |
| SERVICE_EXTENSION_ABILITY | 3 | Start a ServiceExtensionAbility.<br>**System API**: This is a system API.|
