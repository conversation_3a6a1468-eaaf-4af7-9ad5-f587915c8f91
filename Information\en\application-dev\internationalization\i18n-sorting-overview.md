# Overview of Multilingual Sorting

The sorting rules for characters may vary according to languages and cultures. The sorting function allows your application to apply different rules to sort content for users in different countries or regions and using different languages. Besides, it helps your application to output sorting results with semantic features, instead of sorting results simply based on alphabetic codes, to facilitate query and search. The function is common in many scenarios, for example, the language list, country/region list, and contact list.

Currently, sorting can be implemented by [local habits](i18n-sorting-local.md) or [indexes](i18n-sorting-index.md).
