"""
ArkTS Query Async

This module extends the ArkTSQueryEnhanced class with asynchronous query capabilities.
It provides the same functionality but with async/await support for better performance
in asynchronous environments.
"""

import time
import logging
import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional, Tuple, Union, Callable
from qdrant_client import QdrantClient, AsyncQdrantClient
from qdrant_client.http import models

# Import the enhanced query class
from arkts_query_enhanced import ArkTSQueryEnhanced

# Import utility classes
from async_manager import AsyncManager
from error_handler import <PERSON>rror<PERSON>andler
from performance_optimizer import PerformanceOptimizer

# Import compatibility layer
from qdrant_compatibility import qdrant_compatibility

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSQueryAsync")


class ArkTSQueryAsync(ArkTSQueryEnhanced):
    """ArkTS Query with asynchronous capabilities."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None,
                 ollama_url: str = None, embedding_model: str = None,
                 cache_ttl: int = 3600, cache_max_size: int = 1000,
                 reranking_weight: float = 0.5):
        """Initialize the async query.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
            cache_ttl: Time-to-live for cache entries in seconds
            cache_max_size: Maximum number of entries in the cache
            reranking_weight: Weight for semantic vs. text match in reranking
        """
        # Initialize the parent class
        super().__init__(
            qdrant_url=qdrant_url,
            collection_name=collection_name,
            ollama_url=ollama_url,
            embedding_model=embedding_model,
            cache_ttl=cache_ttl,
            cache_max_size=cache_max_size,
            reranking_weight=reranking_weight
        )

        # Initialize async client
        self.async_client = AsyncQdrantClient(
            url=self.qdrant_url,
            prefer_grpc=False,
            timeout=60.0,
            check_compatibility=False  # Skip version compatibility check
        )

        logger.info(f"ArkTSQueryAsync initialized with async Qdrant client at {self.qdrant_url}")

    async def _get_embedding_async(self, text: str) -> List[float]:
        """Get embedding from Ollama asynchronously.

        Args:
            text: Text to embed

        Returns:
            Embedding vector as a list of floats
        """
        # Create error handler with retry configuration
        error_handler = ErrorHandler(
            max_retries=config.MAX_RETRIES,
            retry_delay=config.RETRY_DELAY
        )

        # Define the actual embedding function
        async def fetch_embedding():
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.ollama_url}/api/embeddings",
                    json={
                        "model": self.embedding_model,
                        "prompt": text
                    },
                    timeout=config.REQUEST_TIMEOUT
                ) as response:
                    # Check if request was successful
                    if response.status == 200:
                        # Extract embedding from response
                        response_json = await response.json()
                        embedding = response_json.get("embedding", [])

                        # Verify embedding dimensions
                        if len(embedding) == self.vector_size:
                            return embedding
                        else:
                            logger.warning(f"Embedding dimension mismatch: expected {self.vector_size}, got {len(embedding)}")
                            # Try to pad or truncate to match expected size
                            if len(embedding) > self.vector_size:
                                logger.warning(f"Truncating embedding from {len(embedding)} to {self.vector_size}")
                                return embedding[:self.vector_size]
                            else:
                                logger.warning(f"Padding embedding from {len(embedding)} to {self.vector_size}")
                                return embedding + [0.0] * (self.vector_size - len(embedding))
                    else:
                        response_text = await response.text()
                        raise ValueError(f"Error getting embedding: {response.status} {response_text}")

        try:
            # Use AsyncManager to handle retries
            return await AsyncManager.with_retry(
                fetch_embedding,
                max_retries=config.MAX_RETRIES,
                retry_delay=config.RETRY_DELAY
            )
        except Exception as e:
            logger.error(f"All attempts to get embedding failed: {str(e)}. Using zero vector.")
            return [0.0] * self.vector_size

    async def _get_from_cache_or_compute_async(self, compute_func: Callable, *args, **kwargs) -> Any:
        """Get result from cache or compute it asynchronously.

        Args:
            compute_func: Async function to compute the result if not in cache
            *args: Positional arguments for the compute function
            **kwargs: Keyword arguments for the compute function

        Returns:
            Result from cache or computed result
        """
        # Generate cache key
        cache_key = self._generate_cache_key(*args, **kwargs)
        current_time = time.time()

        # Check cache
        if cache_key in self.query_cache:
            cache_time, result = self.query_cache[cache_key]
            if current_time - cache_time < self.cache_ttl:
                logger.debug(f"Cache hit for key: {cache_key}")
                return result
            else:
                logger.debug(f"Cache expired for key: {cache_key}")
        else:
            logger.debug(f"Cache miss for key: {cache_key}")

        # Compute result asynchronously
        result = await compute_func(*args, **kwargs)

        # Store in cache
        self.query_cache[cache_key] = (current_time, result)
        logger.debug(f"Stored result in cache with key: {cache_key}")

        # Trim cache if needed
        if len(self.query_cache) > self.cache_max_size:
            # Remove oldest entries
            sorted_keys = sorted(self.query_cache.keys(),
                               key=lambda k: self.query_cache[k][0])
            for key in sorted_keys[:len(sorted_keys) // 2]:
                del self.query_cache[key]
            logger.debug(f"Trimmed cache to {len(self.query_cache)} entries")

        return result

    async def suggest_imports_async(self, query: str, limit: int = None,
                                  text_weight: float = 0.3) -> List[Dict[str, Any]]:
        """Suggest imports using enhanced hybrid search asynchronously.

        Args:
            query: Query string
            limit: Maximum number of results
            text_weight: Weight for text search vs. vector search (0.0-1.0)

        Returns:
            List of import suggestions
        """
        limit = limit or config.DEFAULT_LIMIT
        text_weight = max(0.0, min(1.0, text_weight))  # Clamp to [0.0, 1.0]
        vector_weight = 1.0 - text_weight

        # Create an error handler for retries and validation
        error_handler = ErrorHandler(
            max_retries=config.MAX_RETRIES,
            retry_delay=config.RETRY_DELAY
        )

        try:
            # Generate query embedding asynchronously with retry
            async def get_embedding_with_retry():
                return await AsyncManager.with_retry(
                    coro_func=self._get_embedding_async,
                    max_retries=config.MAX_RETRIES,
                    retry_delay=config.RETRY_DELAY,
                    text=query
                )

            query_vector = await get_embedding_with_retry()

            # Perform hybrid search with custom scoring
            try:
                # First, get more results than needed for reranking
                extended_limit = min(limit * 3, 100)  # Get more results but cap at 100

                # Define text search conditions
                text_conditions = [
                    models.FieldCondition(
                        key="symbol_name",
                        match=models.MatchText(text=query)
                    ),
                    models.FieldCondition(
                        key="description",
                        match=models.MatchText(text=query)
                    )
                ]

                # Use retry logic for the async search
                async def search_with_retry():
                    return await qdrant_compatibility.search_async(
                        client=self.async_client,
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        query_filter=models.Filter(
                            should=text_conditions
                        ),
                        score_threshold=0.0,  # Include all results for reranking
                        limit=extended_limit,
                        with_payload=True
                    )

                # Perform the search with retry
                results = await search_with_retry()

                # Rerank results
                reranked_results = self._rerank_results(results, query, vector_weight, text_weight)

                # Limit to requested number
                reranked_results = reranked_results[:limit]

                # Validate results
                validated_results = error_handler.validate_results(reranked_results)

                logger.info(f"Async enhanced hybrid search for '{query}' returned {len(validated_results)} results")
                return validated_results

            except Exception as e:
                logger.warning(f"Async enhanced hybrid search failed: {str(e)}")
                # Fall back to synchronous search
                logger.info("Falling back to synchronous search")
                return self.suggest_imports_hybrid(query, limit, text_weight)

        except Exception as e:
            logger.error(f"Error in suggest_imports_async: {str(e)}")
            # Fall back to synchronous search
            return self.suggest_imports_hybrid(query, limit, text_weight)

    async def search_component_async(self, component_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for a specific component asynchronously.

        Args:
            component_name: Name of the component to search for
            limit: Maximum number of results

        Returns:
            List of component search results
        """
        try:
            # Try async search
            return await self._get_from_cache_or_compute_async(
                self.suggest_imports_async,
                f"component {component_name}", limit
            )
        except Exception as e:
            logger.warning(f"Async component search failed: {str(e)}")
            # Fall back to synchronous search
            return self.search_component(component_name, limit)

    async def search_import_path_async(self, symbol_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for import paths asynchronously.

        Args:
            symbol_name: Name of the symbol to find import paths for
            limit: Maximum number of results

        Returns:
            List of import path search results
        """
        try:
            # Try async search
            return await self._get_from_cache_or_compute_async(
                self.suggest_imports_async,
                f"import {symbol_name}", limit
            )
        except Exception as e:
            logger.warning(f"Async import path search failed: {str(e)}")
            # Fall back to synchronous search
            return self.search_import_path(symbol_name, limit)

    async def search_by_symbol_type_async(self, query: str, symbol_type: str,
                                        limit: int = None) -> List[Dict[str, Any]]:
        """Search for symbols of a specific type asynchronously.

        Args:
            query: Query string
            symbol_type: Symbol type to search for
            limit: Maximum number of results

        Returns:
            List of symbols matching the type and query
        """
        try:
            # Try async search
            return await self._get_from_cache_or_compute_async(
                self.suggest_imports_async,
                f"{symbol_type} {query}", limit
            )
        except Exception as e:
            logger.warning(f"Async symbol type search failed: {str(e)}")
            # Fall back to synchronous search
            return self.search_by_symbol_type(query, symbol_type, limit)

    async def handle_agent_query_async(self, agent_query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Handle queries in the format used by Agno agents asynchronously.

        Args:
            agent_query: Query string in the format "ArkTS search: 'query'"
            limit: Maximum number of results

        Returns:
            List of search results formatted for the agent
        """
        limit = limit or config.DEFAULT_LIMIT

        # Parse query
        if not agent_query.startswith("ArkTS search:"):
            logger.error(f"Invalid agent query format: {agent_query}")
            return []

        # Extract actual query
        search_query = agent_query.split("'")[1] if "'" in agent_query else agent_query.replace("ArkTS search:", "").strip()
        logger.info(f"Parsed agent query: '{search_query}'")

        # Determine query type
        if "component" in search_query.lower():
            # Extract component name
            component_name = search_query.split("component")[0].strip()
            logger.info(f"Detected component search for: '{component_name}'")
            return await self.search_component_async(component_name, limit=limit)

        elif "import path" in search_query.lower():
            # Extract symbol name
            symbol_name = search_query.split("import path")[0].strip()
            logger.info(f"Detected import path search for: '{symbol_name}'")
            return await self.search_import_path_async(symbol_name, limit=limit)

        elif any(type_keyword in search_query.lower() for type_keyword in ["class", "interface", "enum", "function", "type"]):
            # Extract symbol type and query
            for type_keyword in ["class", "interface", "enum", "function", "type"]:
                if type_keyword in search_query.lower():
                    symbol_type = type_keyword.capitalize()
                    query = search_query.split(type_keyword)[0].strip()
                    logger.info(f"Detected {symbol_type} search for: '{query}'")
                    return await self.search_by_symbol_type_async(query, symbol_type, limit=limit)

        # If no specific type detected, perform general search
        logger.info(f"Performing general search for: '{search_query}'")
        return await self.suggest_imports_async(search_query, limit=limit)


# Add main function for command-line usage
def main():
    """Main function for async querying."""
    import argparse
    import asyncio

    parser = argparse.ArgumentParser(description='ArkTS Query Async')
    parser.add_argument('--qdrant-url', type=str, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, help='Ollama embedding model to use')
    parser.add_argument('--query', type=str, help='Query for import suggestions')
    parser.add_argument('--limit', type=int, help='Maximum number of results')
    parser.add_argument('--component', type=str, help='Search for a specific component')
    parser.add_argument('--import-path', type=str, help='Search for import paths for a symbol')
    parser.add_argument('--type', type=str, help='Symbol type to search for')
    parser.add_argument('--agent-query', type=str, help='Process an Agno agent query format')
    parser.add_argument('--cache-ttl', type=int, default=3600, help='Cache TTL in seconds')
    parser.add_argument('--cache-max-size', type=int, default=1000, help='Maximum cache size')
    parser.add_argument('--clear-cache', action='store_true', help='Clear the cache before querying')
    parser.add_argument('--reranking-weight', type=float, default=0.5, help='Reranking weight (0.0-1.0)')
    parser.add_argument('--text-weight', type=float, default=0.3, help='Text search weight (0.0-1.0)')

    args = parser.parse_args()

    # Initialize async query
    query = ArkTSQueryAsync(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model,
        cache_ttl=args.cache_ttl,
        cache_max_size=args.cache_max_size,
        reranking_weight=args.reranking_weight
    )

    # Clear cache if requested
    if args.clear_cache:
        query.clear_cache()
        print("Cache cleared")

    # Define async main function
    async def async_main():
        # Process query based on arguments
        if args.agent_query:
            # Handle agent query format
            results = await query.handle_agent_query_async(args.agent_query, limit=args.limit)
            print(f"Found {len(results)} results for agent query: '{args.agent_query}'")

        elif args.component:
            # Search for component
            results = await query.search_component_async(args.component, limit=args.limit)
            print(f"Found {len(results)} components matching '{args.component}':")

        elif args.import_path:
            # Search for import paths
            results = await query.search_import_path_async(args.import_path, limit=args.limit)
            print(f"Found {len(results)} import paths for '{args.import_path}':")

        elif args.type and args.query:
            # Search by symbol type
            results = await query.search_by_symbol_type_async(args.query, args.type, limit=args.limit)
            print(f"Found {len(results)} {args.type} symbols matching '{args.query}':")

        elif args.query:
            # General import suggestions with async enhanced hybrid search
            results = await query.suggest_imports_async(args.query, limit=args.limit, text_weight=args.text_weight)
            print(f"Found {len(results)} suggestions for '{args.query}':")

        else:
            print("Please provide a query parameter. Use --help for available options.")
            return

        # Print results
        for i, result in enumerate(results, 1):
            print(f"\n{i}. {result['symbol_name']} ({result['symbol_type']})")
            print(f"   Import: {result.get('import_statement', 'N/A')}")
            print(f"   Score: {result['score']:.4f}")

            if 'vector_score' in result and 'text_score' in result:
                print(f"   Vector Score: {result['vector_score']:.4f}, Text Score: {result['text_score']:.4f}")

            if result.get('parent_symbol'):
                print(f"   Nested in: {result['parent_symbol']}")

            if result.get('module_name'):
                print(f"   Module: {result['module_name']}")

            if result.get('description'):
                # Truncate long descriptions
                desc = result['description']
                if len(desc) > 100:
                    desc = desc[:97] + "..."
                print(f"   Description: {desc}")

    # Run the async main function using AsyncManager with proper cleanup
    async_mgr = AsyncManager()
    try:
        # Use the event loop context manager for proper cleanup
        with async_mgr.event_loop_context() as loop:
            # Create a task for the async main function
            task = loop.create_task(async_main())

            # Run the task with a timeout
            loop.run_until_complete(
                asyncio.wait_for(task, timeout=60.0)
            )
    except asyncio.TimeoutError:
        print("Operation timed out after 60 seconds")
    except Exception as e:
        print(f"Error running async main: {str(e)}")


if __name__ == "__main__":
    main()
