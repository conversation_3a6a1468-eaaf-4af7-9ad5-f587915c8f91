# OH_Drawing_GpuContextOptions


## Overview

The OH_Drawing_GpuContextOptions struct describes the options about the GPU context.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| bool [allowPathMaskCaching](#allowpathmaskcaching) | Whether to allow path mask textures to be cached. The value **true** means to allow the path mask textures to be cached, and **false** means the opposite.| 


## Member Variable Description


### allowPathMaskCaching

```
bool OH_Drawing_GpuContextOptions::allowPathMaskCaching
```

**Description**

Whether to allow path mask textures to be cached. The value **true** means to allow the path mask textures to be cached, and **false** means the opposite.
