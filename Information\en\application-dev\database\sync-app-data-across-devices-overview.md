# Distributed Application Data Sync Overview


## When to Use

The distributed application data sync allows the data of an application to be synced with other devices that are connected to form a Virtual Device. This feature enables seamless sync, modification, and query of use application data across trusted devices.

For example, when data is added, deleted, or modified for an application on a device, the same application on another device can obtain the updated data. You can use this feature in the distributed Gallery, Notepad, Contacts, and File Manager. 

For details about how to subscribe to database change notifications between different applications, see [Cross-Application Data Sharing](data-share-overview.md).

The data storage modes vary depending on the lifecycle of data to be synced:

- Temporary data has a short lifecycle and is usually stored in memory. For example, distributed data objects are recommended for process data generated by game applications.

- Persistent data has a long lifecycle and needs to be stored in databases. You can use RDB stores or KV stores based on data characteristics and relationships. For example, RDB stores are recommended for storing Gallery attribute information, such as albums, covers, and images, and KV stores are recommended for storing Gallery image thumbnails.


## Basic Concepts

In a distributed scenario, cross-device collaboration demands consistent data between the devices in the same network.


The data consistency can be classified into the following types:


- Strong consistency: When data is inserted, deleted, or modified on a device, other devices in the same network can immediately obtain the updated data.

- Weak consistency: When data is added, deleted, or modified on a device, other devices in the same network may or may not obtain the updates. The data on these devices may be inconsistent after a certain period of time.

- Eventual consistency: When data is added, deleted, or modified on a device, other devices in the same network may not obtain the updates immediately. However, data on these devices will become consistent after a certain period of time.


Strong consistency has high requirements on distributed data management and may be used in distributed server deployment. Because mobile devices are not always online and there is no central node, the cross-device application data sync supports eventual consistency only.


## Access Control Mechanism in Cross-Device Sync

When data is synced across devices, access control is performed based on the device level and data security label. For details, see [Access Control Mechanism in Cross-Device Sync](access-control-by-device-and-data-level.md#access-control-mechanism-in-cross-device-sync).
