# LifecycleApp Switching


| API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding API in the Stage Model|
| -------- | -------- | -------- |
| onShow?(): void; | \@ohos.window.d.ts | [on(eventType: 'windowStageEvent', callback: Callback&lt;WindowStageEventType&gt;): void;](../reference/apis-arkui/js-apis-window.md#onwindowstageevent9)<br>Listens for SHOWN, indicating a switching to the foreground.|
| onHide?(): void; | \@ohos.window.d.ts | [on(eventType: 'windowStageEvent', callback: Callback&lt;WindowStageEventType&gt;): void;](../reference/apis-arkui/js-apis-window.md#onwindowstageevent9)<br>Listens for HIDDEN, indicating a switching to the background.|
| onDestroy?(): void; | \@ohos.app.ability.UIAbility.d.ts | [onDestroy(): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityondestroy) |
| onCreate?(): void; | \@ohos.app.ability.UIAbility.d.ts | [onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityoncreate) |
| onWindowDisplayModeChanged?(isShownInMultiWindow: boolean, newConfig: resourceManager.Configuration): void; | There is no corresponding API in the stage model.| No corresponding API is provided.|
| onStartContinuation?(): boolean; | There is no corresponding API in the stage model.| In the stage model, an application does not need to detect whether the continuation is successful (detected when the application initiates the continuation request). Therefore, the **onStartContinuation()** callback is deprecated.|
| onSaveData?(data: Object): boolean; | \@ohos.app.ability.UIAbility.d.ts | [onContinue(wantParam: Record&lt;string, Object&gt;): AbilityConstant.OnContinueResult;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityoncontinue) |
| onCompleteContinuation?(result: number): void; | application\ContinueCallback.d.ts | [onContinueDone(result: number): void;](../reference/apis-ability-kit/js-apis-inner-application-continueCallback-sys.md#continuecallbackoncontinuedone) |
| onRestoreData?(data: Object): void; | \@ohos.app.ability.UIAbility.d.ts | [onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityoncreate)<br>[onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityonnewwant)<br>In multiton or singleton mode, the target ability completes data restoration in the **onCreate()** callback. In the callback, **launchParam.launchReason** is used to determine whether it is a continuation-based launch scenario. If it is, the data saved before continuation can be obtained from the **want** parameter.|
| onRemoteTerminated?(): void; | application\ContinueCallback.d.ts | [onContinueDone(result: number): void;](../reference/apis-ability-kit/js-apis-inner-application-continueCallback-sys.md#continuecallbackoncontinuedone) |
| onSaveAbilityState?(outState: PacMap): void; | \@ohos.app.ability.UIAbility.d.ts | [onSaveState(reason: AbilityConstant.StateType, wantParam : Record&lt;string, Object&gt;): AbilityConstant.OnSaveResult;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityonsavestate) |
| onRestoreAbilityState?(inState: PacMap): void; | \@ohos.app.ability.UIAbility.d.ts | [onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityoncreate)<br>After an application is restarted, the **onCreate()** callback is triggered. In the callback, **launchParam.launchReason** is used to determine whether it is a self-recovery scenario. If it is, the data saved before the restart can be obtained from the **want** parameter.|
| onInactive?(): void; | \@ohos.app.ability.UIAbility.d.ts | [onBackground(): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityonbackground) |
| onActive?(): void; | \@ohos.app.ability.UIAbility.d.ts | [onForeground(): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityonforeground) |
| onNewWant?(want: Want): void; | \@ohos.app.ability.UIAbility.d.ts | [onNewWant(want: Want, launchParam: AbilityConstant.LaunchParam): void;](../reference/apis-ability-kit/js-apis-app-ability-uiAbility.md#uiabilityonnewwant) |
| onMemoryLevel?(level: number): void | \@ohos.app.ability.UIAbility.d.ts | [onMemoryLevel(level: AbilityConstant.MemoryLevel): void;](../reference/apis-ability-kit/js-apis-app-ability-ability.md#abilityonmemorylevel) |
