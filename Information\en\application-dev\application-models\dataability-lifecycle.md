# DataAbility Lifecycle


You can implement lifecycle callbacks (as described in the table below) in **data.js** or **data.ets**.  


**Table 1** DataAbility lifecycle APIs

| API| Description| 
| -------- | -------- |
| onInitialized?(info: AbilityInfo): void | Called during ability initialization to initialize the relational database (RDB).| 
| update?(uri: string, valueBucket: rdb.ValuesBucket, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;number&gt;): void | Updates data in the database.| 
| query?(uri: string, columns: Array&lt;string&gt;, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;ResultSet&gt;): void | Queries data in the database.| 
| delete?(uri: string, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;number&gt;): void | Deletes one or more data records from the database.| 
| normalizeUri?(uri: string, callback: AsyncCallback&lt;string&gt;): void | Normalizes the URI. A normalized URI applies to cross-device use, persistence, backup, and restore. When the context changes, it ensures that the same data item can be referenced.| 
| batchInsert?(uri: string, valueBuckets: Array&lt;rdb.ValuesBucket&gt;, callback: AsyncCallback&lt;number&gt;): void | Inserts multiple data records into the database.| 
| denormalizeUri?(uri: string, callback: AsyncCallback&lt;string&gt;): void | Converts a normalized URI generated by **normalizeUri** into a denormalized URI.| 
| insert?(uri: string, valueBucket: rdb.ValuesBucket, callback: AsyncCallback&lt;number&gt;): void | Inserts a data record into the database.| 
| openFile?(uri: string, mode: string, callback: AsyncCallback&lt;number&gt;): void | Opens a file.| 
| getFileTypes?(uri: string, mimeTypeFilter: string, callback: AsyncCallback&lt;Array&lt;string&gt;&gt;): void | Obtains the MIME type of a file.| 
| getType?(uri: string, callback: AsyncCallback&lt;string&gt;): void | Obtains the MIME type matching the data specified by the URI.| 
| executeBatch?(ops: Array&lt;DataAbilityOperation&gt;, callback: AsyncCallback&lt;Array&lt;DataAbilityResult&gt;&gt;): void | Operates data in the database in batches.| 
| call?(method: string, arg: string, extras: PacMap, callback: AsyncCallback&lt;PacMap&gt;): void | Calls a custom API.| 
