# OH_VObject


## Overview

Defines the allowed data field types.

**Since**: 10

**Related module**: [RDB](_r_d_b.md)


## Summary


### Member Variables

| Name| Description|
| -------- | -------- |
| [id](_r_d_b.md#id-35) | Unique identifier of the **OH_VObject** struct.|
| [putInt64](_r_d_b.md#putint64-22) | Converts a single parameter or an array of the int64 type into a value of the OH_VObject type.|
| [putDouble](_r_d_b.md#putdouble) | Converts a single parameter or an array of the int64 type into a value of the OH_VObject type.|
| [putText](_r_d_b.md#puttext-22) | Converts a character array of the char type to a value of the OH_VObject type.|
| [putTexts](_r_d_b.md#puttexts) | Converts a string array of the char type to a value of the OH_VObject type.|
| [destroy](_r_d_b.md#destroy-44) | Destroys an OH_VObject object and reclaims the memory occupied.|
