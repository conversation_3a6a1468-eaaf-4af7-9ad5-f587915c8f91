# AppStorageV2: Storing Application-wide UI State

To enhance the capability of the state management framework to store global UI status variables of applications, you are advised to use AppStorageV2.

>**NOTE**
>
>AppStorageV2 is supported since API version 12.
>
>State management V2 is still under development, and some features may be incomplete or not always work as expected.


## Overview

AppStorage V2 is a singleton to be created when the application UI is started. Its purpose is to provide central storage for application UI state attributes. And AppStorageV2 retains data during application running. Each attribute is accessed using a unique key, which is a string.

UI components synchronize application state attributes with AppStorageV2. AppStorageV2 can be accessed during implementation of application service logic as well.

AppStorageV2 supports state sharing among multiple UIAbility instances in the [main thread](../application-models/thread-model-stage.md) of an application.


## How to Use

### connect: Creating or Obtaining Stored Data

```JavaScript
static connect<T extends object>(
    type: TypeConstructorWithArgs<T>,
    keyOrDefaultCreator?: string | StorageDefaultCreator<T>,
    defaultCreator?: StorageDefaultCreator<T> 
): T | undefined;
```

| connect      | Description                                                 |
| ------------ | ----------------------------------------------------- |
| Parameter        | **type**: specified type. If no **key** is specified, the name of the **type** is used as the **key**.<br> **keyOrDefaultCreater**: specified key or default constructor.<br> **defaultCreator**: default constructor.                                         |
| Return value      | After creating or obtaining data, value is returned. Otherwise, **undefined** is returned. |

>**NOTE**
>
>1. The third parameter is used when no key is specified or the second parameter is invalid. Otherwise, the second parameter is used.
>
>2. If the data has been stored in AppStorageV2, you can obtain the stored data without using the default constructor. Otherwise, you must specify the default constructor. If no constructor is specified, the application exception occurs.
>
>3. Ensure that the data types match the key. If different types of data are connected to the same key, the application exception occurs. 
>
>4. You are advised to use meaningful values for keys. The values can contain letters, digits, and underscores (_) and a maximum of 255 characters. If use invalid characters or null characters, undefined behavior may occur.
>
>5. When match the key with the [\@Observed](arkts-observed-and-objectlink.md) object, specify the key or customize the **name** attribute.

### remove: Deleting the Stored Data of a Specified Key

```JavaScript
static remove<T>(keyOrType: string | TypeConstructorWithArgs<T>): void;
```

| remove       | Description                                                 |
| ------------ | ----------------------------------------------------- |
| Parameter        | **keyOrType**: key to be deleted. If the key is of the **Type**, the key to be deleted is the name of the **Type**.                                         |
| Return value      | None. |

>**NOTE**
>
>If a key that does not exist in AppStorage V2 is deleted, a warning is reported.

### keys: Returning All Keys Stored in AppStorageV2

```JavaScript
static keys(): Array<string>;
```

| keys         | Description                                                 |
| ------------ | ----------------------------------------------------- |
| Parameter        | None.                                        |
| Return value      | All keys stored in AppStorageV2. |


## Constraints

1. This singleton must be used together with the UI thread only. Other threads, for example, @Sendable decorator is not supported.

2. Types such as collections.Set and collections.Map are not supported.

3. Non-buildin types, such as native PixelMap, NativePointer, and ArrayList types, are not supported.

## Use Scenarios

### Storing Data Between Two Pages

Page 1
```ts
import { router, AppStorageV2 } from '@kit.ArkUI';
import { Sample } from '../Sample';

@Entry
@ComponentV2
struct Page1 {
  // Create a KV pair whose key is Sample in AppStorageV2 (if the key exists, the data in AppStorageV2 is returned) and associate it with prop.
  @Local prop: Sample = AppStorageV2.connect(Sample, () => new Sample())!;

  build() {
    Column() {
      Button('Go to page2')
        .onClick(() => {
          router.pushUrl({
            url: 'pages/Page2'
          })
        })

      Button('Page1 connect the key Sample')
        .onClick(() => {
          // Create a KV pair whose key is Sample in AppStorageV2 (if the key exists, the data in AppStorageV2 is returned) and associate it with prop.
          this.prop = AppStorageV2.connect(Sample, 'Sample', () => new Sample())!;
        })

      Button('Page1 remove the key Sample')
        .onClick(() => {
          // After being deleted from AppStorageV2, prop will no longer be associated with the value whose key is Sample.
          AppStorageV2.remove(Sample);
        })

      Text(`Page1 add 1 to prop.p1: ${this.prop.p1}`)
        .fontSize(30)
        .onClick(() => {
          this.prop.p1++;
        })

      Text(`Page1 add 1 to prop.p2: ${this.prop.p2}`)
        .fontSize(30)
        .onClick(() => {
          // The page is not re-rendered, but the value of p2 is changed.
          this.prop.p2++;
        })

      // Obtain all keys in the current AppStorageV2.
      Text(`all keys in AppStorage: ${AppStorageV2.keys()}`)
        .fontSize(30)
    }
  }
}
```

Page 2
```ts
import { AppStorageV2 } from '@kit.ArkUI';
import { Sample } from '../Sample';

@Entry
@ComponentV2
struct Page2 {
  // Create a KV pair whose key is Sample in AppStorageV2 (if the key exists, the data in AppStorageV2 is returned) and associate it with prop.
  @Local prop: Sample = AppStorageV2.connect(Sample, () => new Sample())!;

  build() {
    Column() {
      Button('Page2 connect the key Sample1')
        .onClick(() => {
          // Create a KV pair whose key is Sample1 in AppStorageV2 (if the key exists, the data in AppStorageV2 is returned) and associate it with prop.
          this.prop = AppStorageV2.connect(Sample, 'Sample1', () => new Sample())!;
        })

      Text(`Page2 add 1 to prop.p1: ${this.prop.p1}`)
        .fontSize(30)
        .onClick(() => {
          this.prop.p1++;
        })

      Text(`Page2 add 1 to prop.p2: ${this.prop.p2}`)
        .fontSize(30)
        .onClick(() => {
          // The page is not re-rendered, but the value of p2 is changed, which is performed after re-initialization.
          this.prop.p2++;
        })

      // Obtain all keys in the current AppStorageV2.
      Text(`all keys in AppStorage: ${AppStorageV2.keys()}`)
        .fontSize(30)
    }
  }
}
```

Data page
```ts
// Data center
@ObservedV2
export class Sample {
  @Trace p1: number = 0;
  p2: number = 10;
}
```
