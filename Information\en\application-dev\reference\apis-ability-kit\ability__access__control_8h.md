# ability_access_control.h


## Overview

Declares the APIs for implementing process access control.

**Library**: ability_access_control.so

**File to include**: <accesstoken/ability_access_control.h>

**System capability**: SystemCapability.Security.AccessToken

**Since**: 12

**Related module**: [AbilityAccessControl](_ability_access_control.md)


## Summary


### Functions

| Name| Description| 
| -------- | -------- |
| bool [OH_AT_CheckSelfPermission](_ability_access_control.md#oh_at_checkselfpermission) (const char \*permission) | Checks whether the specified permission is granted to the application. | 
