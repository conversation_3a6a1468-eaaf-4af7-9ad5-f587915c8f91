[project]
name = "agno"
version = "1.5.4"
description = "Agno: a lightweight library for building Reasoning Agents"
requires-python = ">=3.7,<4"
readme = "README.md"
license = { file = "LICENSE" }
authors = [
  {name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>"}
]
keywords = [
  "agent",
  "reasoning",
  "llm",
  "large-language-model",
  "framework",
]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Mozilla Public License 2.0 (MPL 2.0)",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.7",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Operating System :: OS Independent",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]

dependencies = [
  "docstring-parser",
  "gitpython",
  "httpx",
  "pydantic-settings",
  "pydantic",
  "python-dotenv",
  "python-multipart",
  "pyyaml",
  "rich",
  "tomli",
  "typer",
  "typing-extensions",
]

[project.optional-dependencies]
dev = ["mypy", "pytest", "pytest-asyncio", "pytest-cov", "pytest-mock", "ruff", "timeout-decorator", "types-pyyaml", "types-aiofiles", "fastapi", "uvicorn", "arxiv"]

# Models integration test dependencies
integration-tests = [
    "exa_py",
    "duckduckgo-search",
    "yfinance",
    "sqlalchemy",
    "Pillow",
]

# Dependencies for Telemetry
opentelemetry = ["opentelemetry-sdk", "opentelemetry-exporter-otlp"]
weave = ["weave"]
openlit = ["openlit", "agno[opentelemetry]"]
arize = ["arize-phoenix", "agno[opentelemetry]", "opentelemetry-exporter-otlp-proto-grpc", "opentelemetry-distro"]
langfuse = ["langfuse"]

# Dependencies for Models
azure = ["azure-ai-inference", "aiohttp"]
anthropic = ["anthropic"]
cohere = ["cohere"]
google = ["google-genai"]
groq = ["groq"]
mistral = ["mistralai"]
openai = ["openai"]
ollama = ["ollama"]
ibm = ["ibm-watsonx-ai"]
lmstudio = ["lmstudio"]
litellm = ["litellm"]
meta = ["llama-api-client"]
cerebras = ["cerebras-cloud-sdk"]

# Dependencies for Tools
apify = ["apify-client"]
cartesia = ["cartesia"]
exa = ["exa_py"]
mem0 = ["mem0ai"]
yfinance = ["yfinance"]
ddg = ["duckduckgo-search"]
duckdb = ["duckdb"]
newspaper = ["newspaper4k", "lxml_html_clean"]
youtube = ["youtube_transcript_api"]
firecrawl = ["firecrawl-py"]
github = ["PyGithub"]
gmail = ["google-api-python-client", "google-auth-httplib2", "google-auth-oauthlib"]
googlemaps = ["googlemaps", "google-maps-places"]
todoist = ["todoist-api-python"]
elevenlabs = ["elevenlabs"]
fal = ["fal_client"]
webex = ["webexpythonsdk"]
mcp = ["mcp"]
browserbase = ["browserbase", "playwright"]
agentql = ["agentql"]
confluence = ["atlassian-python-api"]
zep = ["zep-cloud"]
google_bigquery = ["google-cloud-bigquery"]

# Dependencies for Storage
sql = ["sqlalchemy"]
postgres = ["psycopg-binary", "psycopg"]
sqlite = ["sqlalchemy"]
gcs = ["google-cloud-storage"]
redis = ["redis"]

# Dependencies for Vector databases
pgvector = ["pgvector"]
chromadb = ["chromadb"]
lancedb = ["lancedb==0.20.0", "tantivy"]
qdrant = ["qdrant-client"]
couchbase = ["couchbase"]
cassandra = ["cassio"]
mongodb = ["pymongo[srv]"]
singlestore = ["sqlalchemy"]
weaviate = ["weaviate-client"]
milvusdb = ["pymilvus"]
clickhouse = ["clickhouse-connect"]
pinecone = ["pinecone==5.4.2"]

# Dependencies for Knowledge
pdf = ["pypdf", "rapidocr_onnxruntime"]
docx = ["python-docx"]
text = ["aiofiles"]
csv = ["aiofiles"]

# Dependencies for Performance
performance = ["memory_profiler"]

# Dependencies for Running cookbook
cookbooks = ["inquirer", "email_validator"]

# Dependencies for Docker
docker = ["agno-docker"]

# Dependencies for AWS
aws = ["agno-aws", "agno-docker"]

# All models
models = [
  "agno[anthropic]",
  "agno[azure]",
  "agno[cohere]",
  "agno[google]",
  "agno[groq]",
  "agno[mistral]",
  "agno[ollama]",
  "agno[openai]",
  "agno[ibm]",
  "agno[litellm]",
  "agno[meta]",
  "agno[cerebras]"
]

# All tools
tools = [
  "agno[apify]",
  "agno[exa]",
  "agno[cartesia]",
  "agno[ddg]",
  "agno[duckdb]",
  "agno[newspaper]",
  "agno[youtube]",
  "agno[firecrawl]",
  "agno[github]",
  "agno[gmail]",
  "agno[googlemaps]",
  "agno[todoist]",
  "agno[elevenlabs]",
  "agno[fal]",
  "agno[webex]",
  "agno[mcp]",
  "agno[browserbase]",
  "agno[agentql]",
#  "agno[yfinance]",  # Broken right now
  "agno[confluence]",
  "agno[zep]",
  "agno[mem0]",
  "agno[google_bigquery]",
]

# All storage
storage = [
  "agno[sql]",
  "agno[postgres]",
  "agno[sqlite]",
  "agno[gcs]",
  "agno[redis]",
]

# All vector databases
vectordbs = [
  "agno[pgvector]",
  "agno[chromadb]",
  "agno[lancedb]",
  "agno[qdrant]",
  "agno[couchbase]",
  "agno[cassandra]",
  "agno[mongodb]",
  "agno[singlestore]",
  "agno[weaviate]",
  "agno[milvusdb]",
  "agno[clickhouse]",
  "agno[pinecone]"
]

# All knowledge
knowledge = [
  "agno[pdf]",
  "agno[docx]",
  "agno[text]",
  "agno[csv]"
]

# All libraries for testing
tests = [
    "agno[dev]",
    "agno[models]",
    "agno[tools]",
    "agno[storage]",
    "agno[vectordbs]",
    "agno[knowledge]",
    "agno[performance]",
    "agno[cookbooks]",
    "twine",
    "build",
]

[project.scripts]
ag = "agno.cli.entrypoint:agno_cli"
agno = "agno.cli.entrypoint:agno_cli"

[project.urls]
homepage = "https://agno.com"
documentation = "https://docs.agno.com"

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]
include = ["agno*"]

[tool.setuptools.package-data]
agno = ["py.typed"]
include = ["LICENSE"]

[tool.pytest.ini_options]
log_cli = true
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"

[tool.ruff]
line-length = 120
# Ignore `F401` (import violations) in all `__init__.py` files
[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]

[tool.mypy]
check_untyped_defs = true
no_implicit_optional = true
warn_unused_configs = true
disable_error_code = ["override"]
plugins = ["pydantic.mypy"]
exclude = ["tests*"]

[[tool.mypy.overrides]]
module = [
  "agentql.*",
  "aiofiles.*",
  "altair.*",
  "anthropic.*",
  "apify_client.*",
  "arxiv.*",
  "atlassian.*",
  "azure.ai.inference.*",
  "azure.core.*",
  "boto3.*",
  "botocore.*",
  "bs4.*",
  "bson.*",
  "browserbase.*",
  "cassio.*",
  "cerebras.*",
  "cerebras_cloud_sdk.*",
  "chonkie.*",
  "chromadb.*",
  "clickhouse_connect.*",
  "clip.*",
  "cohere.*",
  "crawl4ai.*",
  "docker.*",
  "docx.*",
  "duckdb.*",
  "duckduckgo_search.*",
  "email_validator.*",
  "e2b_code_interpreter.*",
  "exa_py.*",
  "fastapi.*",
  "filetype.*",
  "firecrawl.*",
  "github.*",
  "google.*",
  "googlemaps.*",
  "google_maps_places.*",
  "google_auth_oauthlib.*",
  "googleapiclient.*",
  "googlesearch.*",
  "groq.*",
  "huggingface_hub.*",
  "ibm_watsonx_ai.*",
  "imghdr.*",
  "jira.*",
  "kubernetes.*",
  "lancedb.*",
  "langchain_core.*",
  "langchain.*",
  "llama_index.*",
  "llama_api_client.*",
  "litellm.*",
  "mem0.*",
  "mcp.*",
  "memory_profiler.*",
  "mistralai.*",
  "mlx_whisper.*",
  "nest_asyncio.*",
  "newspaper.*",
  "numpy.*",
  "ollama.*",
  "openai.*",
  "openbb.*",
  "pandas.*",
  "pgvector.*",
  "PIL.*",
  "pinecone_text.*",
  "pinecone.*",
  "playwright.sync_api.*",
  "psycopg.*",
  "psycopg2.*",
  "pyarrow.*",
  "pycountry.*",
  "pymongo.*",
  "pypdf.*",
  "pytz.*",
  "qdrant_client.*",
  "rapidocr_onnxruntime.*",
  "redis.*",
  "replicate.*",
  "requests.*",
  "scrapegraph_py.*",
  "sentence_transformers.*",
  "serpapi.*",
  "setuptools.*",
  "simplejson.*",
  "slack_sdk.*",
  "spider.*",
  "sqlalchemy.*",
  "starlette.*",
  "streamlit.*",
  "tantivy.*",
  "tavily.*",
  "textract.*",
  "timeout_decorator.*",
  "tiktoken.*",
  "torch.*",
  "todoist_api_python.*",
  "tweepy.*",
  "twilio.*",
  "tzlocal.*",
  "upstash_vector.*",
  "urllib3.*",
  "uvicorn.*",
  "vertexai.*",
  "voyageai.*",
  "weaviate.*",
  "webexpythonsdk.*",
  "wikipedia.*",
  "yaml.*",
  "yfinance.*",
  "youtube_transcript_api.*",
  "couchbase.*",
  "acouchbase.*",
  "zep_cloud.*"
]
ignore_missing_imports = true
