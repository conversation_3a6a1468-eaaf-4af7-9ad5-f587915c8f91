# OH_Filter


## Overview

The OH_Filter struct describes a filter used to generate a filter pixel map.

**Since**: 12

**Related module**: [EffectKit](effect_kit.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| std::vector&lt; sk_sp&lt; SkImageFilter &gt; &gt; [skFilters_](#skfilters_) | Container for storing the pointer to the filter object. | 


## Member Variable Description


### skFilters_

```
std::vector<sk_sp<SkImageFilter> > OH_Filter::skFilters_
```
**Description**

Container for storing the pointer to the filter object.
