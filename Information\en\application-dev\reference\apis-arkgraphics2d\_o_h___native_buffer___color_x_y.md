# OH_NativeBuffer_ColorXY


## Overview

The OH_NativeBuffer_ColorXY struct describes the X and Y coordinates of the primary color.

**System capability**: SystemCapability.Graphic.Graphic2D.NativeBuffer

**Since**: 12

**Related module**: [OH_NativeBuffer](_o_h___native_buffer.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| float [x](#x) | X coordinate of the primary color.| 
| float [y](#y) | Y coordinate of the primary color.| 


## Member Variable Description


### x

```
float OH_NativeBuffer_ColorXY::x
```

**Description**

X coordinate of the primary color.


### y

```
float OH_NativeBuffer_ColorXY::y
```

**Description**

Y coordinate of the primary color.
