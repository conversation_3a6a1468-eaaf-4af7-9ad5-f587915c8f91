"""
ArkTS Query with Caching

This module extends the ArkTSQuery class with caching capabilities.
It provides the same functionality as ArkTSQuery but with improved performance
through caching of query results.
"""

import time
import logging
import hashlib
import json
from typing import List, Dict, Any, Optional, Callable, Tuple, Union
from qdrant_client.http import models

# Import the original ArkTSQuery class
from arkts_query import ArkTSQuery

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSQueryCached")


class ArkTSQueryCached(ArkTSQuery):
    """ArkTS Query with caching capabilities."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None,
                 ollama_url: str = None, embedding_model: str = None,
                 cache_ttl: int = 3600, cache_max_size: int = 1000):
        """Initialize the query with caching.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
            cache_ttl: Time-to-live for cache entries in seconds (default: 1 hour)
            cache_max_size: Maximum number of entries in the cache (default: 1000)
        """
        # Initialize the parent class
        super().__init__(
            qdrant_url=qdrant_url,
            collection_name=collection_name,
            ollama_url=ollama_url,
            embedding_model=embedding_model
        )

        # Initialize cache
        self.cache_ttl = cache_ttl
        self.cache_max_size = cache_max_size
        self._init_cache()

        logger.info(f"ArkTSQueryCached initialized with TTL: {cache_ttl}s, max size: {cache_max_size}")

    def _init_cache(self) -> None:
        """Initialize the query cache."""
        self.query_cache = {}
        logger.debug("Query cache initialized")

    def _generate_cache_key(self, *args, **kwargs) -> str:
        """Generate a cache key from the arguments.

        Args:
            *args: Positional arguments
            **kwargs: Keyword arguments

        Returns:
            Cache key as a string
        """
        # Convert args and kwargs to a string representation
        args_str = str(args)
        kwargs_str = str(sorted(kwargs.items()))
        
        # Combine and hash
        combined = f"{args_str}_{kwargs_str}"
        return hashlib.md5(combined.encode()).hexdigest()

    def _get_from_cache_or_compute(self, compute_func: Callable, *args, **kwargs) -> Any:
        """Get result from cache or compute it.

        Args:
            compute_func: Function to compute the result if not in cache
            *args: Positional arguments for the compute function
            **kwargs: Keyword arguments for the compute function

        Returns:
            Result from cache or computed result
        """
        # Generate cache key
        cache_key = self._generate_cache_key(*args, **kwargs)
        current_time = time.time()
        
        # Check cache
        if cache_key in self.query_cache:
            cache_time, result = self.query_cache[cache_key]
            if current_time - cache_time < self.cache_ttl:
                logger.debug(f"Cache hit for key: {cache_key}")
                return result
            else:
                logger.debug(f"Cache expired for key: {cache_key}")
        else:
            logger.debug(f"Cache miss for key: {cache_key}")
        
        # Compute result
        result = compute_func(*args, **kwargs)
        
        # Store in cache
        self.query_cache[cache_key] = (current_time, result)
        logger.debug(f"Stored result in cache with key: {cache_key}")
        
        # Trim cache if needed
        if len(self.query_cache) > self.cache_max_size:
            # Remove oldest entries
            sorted_keys = sorted(self.query_cache.keys(), 
                               key=lambda k: self.query_cache[k][0])
            for key in sorted_keys[:len(sorted_keys) // 2]:
                del self.query_cache[key]
            logger.debug(f"Trimmed cache to {len(self.query_cache)} entries")
        
        return result

    def clear_cache(self) -> None:
        """Clear the query cache."""
        self.query_cache.clear()
        logger.info("Query cache cleared")

    def suggest_imports(self, query: str, limit: int = None, use_hybrid: bool = None) -> List[Dict[str, Any]]:
        """Suggest imports for a query with caching.

        Args:
            query: Query string
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search

        Returns:
            List of import suggestions
        """
        return self._get_from_cache_or_compute(
            super().suggest_imports,
            query, limit, use_hybrid
        )

    def filter_suggestions_by_type(self, query: str, symbol_type: str, limit: int = None) -> List[Dict[str, Any]]:
        """Filter import suggestions by symbol type with caching.

        Args:
            query: Query string
            symbol_type: Symbol type to filter by
            limit: Maximum number of results

        Returns:
            List of filtered import suggestions
        """
        return self._get_from_cache_or_compute(
            super().filter_suggestions_by_type,
            query, symbol_type, limit
        )

    def search_nested_symbols(self, parent_symbol: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for nested symbols within a parent symbol with caching.

        Args:
            parent_symbol: Parent symbol name
            limit: Maximum number of results

        Returns:
            List of nested symbols
        """
        return self._get_from_cache_or_compute(
            super().search_nested_symbols,
            parent_symbol, limit
        )

    def search_nested_by_type(self, parent_symbol: str, symbol_type: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for nested symbols of a specific type with caching.

        Args:
            parent_symbol: Parent symbol name
            symbol_type: Type of nested symbols to search for
            limit: Maximum number of results

        Returns:
            List of nested symbols of the specified type
        """
        return self._get_from_cache_or_compute(
            super().search_nested_by_type,
            parent_symbol, symbol_type, limit
        )

    def search_component(self, component_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for a specific component by name with caching.

        Args:
            component_name: Name of the component to search for
            limit: Maximum number of results

        Returns:
            List of component search results
        """
        return self._get_from_cache_or_compute(
            super().search_component,
            component_name, limit
        )

    def search_import_path(self, symbol_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for import paths for a specific symbol with caching.

        Args:
            symbol_name: Name of the symbol to find import paths for
            limit: Maximum number of results

        Returns:
            List of import path search results
        """
        return self._get_from_cache_or_compute(
            super().search_import_path,
            symbol_name, limit
        )

    def search_by_symbol_type(self, query: str, symbol_type: str, limit: int = None, use_hybrid: bool = None) -> List[Dict[str, Any]]:
        """Search for symbols of a specific type with caching.

        Args:
            query: Query string
            symbol_type: Symbol type to search for
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search

        Returns:
            List of symbols matching the type and query
        """
        return self._get_from_cache_or_compute(
            super().search_by_symbol_type,
            query, symbol_type, limit, use_hybrid
        )

    def handle_agent_query(self, agent_query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Handle queries in the format used by Agno agents with caching.

        Args:
            agent_query: Query string in the format "ArkTS search: 'query'"
            limit: Maximum number of results

        Returns:
            List of search results formatted for the agent
        """
        return self._get_from_cache_or_compute(
            super().handle_agent_query,
            agent_query, limit
        )


# Add main function for command-line usage
def main():
    """Main function for querying with caching."""
    import argparse

    parser = argparse.ArgumentParser(description='ArkTS Query with Caching')
    parser.add_argument('--qdrant-url', type=str, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, help='Ollama embedding model to use')
    parser.add_argument('--query', type=str, help='Query for import suggestions')
    parser.add_argument('--limit', type=int, help='Maximum number of results')
    parser.add_argument('--hybrid', action='store_true', help='Use hybrid search')
    parser.add_argument('--type', type=str, help='Filter by symbol type')
    parser.add_argument('--nested', type=str, help='Search for nested symbols within a parent')
    parser.add_argument('--nested-type', type=str, help='Type of nested symbols to search for')
    parser.add_argument('--component', type=str, help='Search for a specific component')
    parser.add_argument('--import-path', type=str, help='Search for import paths for a symbol')
    parser.add_argument('--agent-query', type=str, help='Process an Agno agent query format')
    parser.add_argument('--cache-ttl', type=int, default=3600, help='Cache TTL in seconds')
    parser.add_argument('--cache-max-size', type=int, default=1000, help='Maximum cache size')
    parser.add_argument('--clear-cache', action='store_true', help='Clear the cache before querying')

    args = parser.parse_args()

    # Initialize query with caching
    query = ArkTSQueryCached(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model,
        cache_ttl=args.cache_ttl,
        cache_max_size=args.cache_max_size
    )

    # Clear cache if requested
    if args.clear_cache:
        query.clear_cache()
        print("Cache cleared")

    # Process query based on arguments
    if args.agent_query:
        # Handle agent query format
        results = query.handle_agent_query(args.agent_query, limit=args.limit)
        print(f"Found {len(results)} results for agent query: '{args.agent_query}'")
    
    elif args.component:
        # Search for component
        results = query.search_component(args.component, limit=args.limit)
        print(f"Found {len(results)} components matching '{args.component}':")
    
    elif args.import_path:
        # Search for import paths
        results = query.search_import_path(args.import_path, limit=args.limit)
        print(f"Found {len(results)} import paths for '{args.import_path}':")
    
    elif args.nested and args.nested_type:
        # Search for nested symbols of specific type
        results = query.search_nested_by_type(args.nested, args.nested_type, limit=args.limit)
        print(f"Found {len(results)} nested {args.nested_type} symbols in '{args.nested}':")
    
    elif args.nested:
        # Search for all nested symbols
        results = query.search_nested_symbols(args.nested, limit=args.limit)
        print(f"Found {len(results)} nested symbols in '{args.nested}':")
    
    elif args.type and args.query:
        # Search by symbol type
        results = query.search_by_symbol_type(args.query, args.type, limit=args.limit, use_hybrid=args.hybrid)
        print(f"Found {len(results)} {args.type} symbols matching '{args.query}':")
    
    elif args.query:
        # General import suggestions
        results = query.suggest_imports(args.query, limit=args.limit, use_hybrid=args.hybrid)
        print(f"Found {len(results)} suggestions for '{args.query}':")
    
    else:
        print("Please provide a query parameter. Use --help for available options.")
        return

    # Print results
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['symbol_name']} ({result['symbol_type']})")
        print(f"   Import: {result.get('import_statement', 'N/A')}")
        print(f"   Score: {result['score']:.4f}")
        
        if result.get('parent_symbol'):
            print(f"   Nested in: {result['parent_symbol']}")
        
        if result.get('module_name'):
            print(f"   Module: {result['module_name']}")
            
        if result.get('description'):
            # Truncate long descriptions
            desc = result['description']
            if len(desc) > 100:
                desc = desc[:97] + "..."
            print(f"   Description: {desc}")


if __name__ == "__main__":
    main()
