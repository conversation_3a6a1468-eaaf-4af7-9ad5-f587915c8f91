{"first_query_time": 2.085681915283203, "second_query_time": 0.0, "speedup": 20856.81915283203, "results": [{"symbol_name": "<PERSON><PERSON>", "symbol_type": "component", "module_name": "button_component", "is_default": false, "is_ets": true, "description": "Provides a button component. @component Button @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @since 7", "import_statement": "import { Button } from 'button_component';", "parent_symbol": null, "is_nested": false, "score": 0.7021555}, {"symbol_name": "ButtonType", "symbol_type": "enum", "module_name": "button", "is_default": false, "is_ets": false, "description": "Provides a button component. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonType } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.6679334}, {"symbol_name": "ButtonOptions", "symbol_type": "interface", "module_name": "button", "is_default": false, "is_ets": false, "description": "Defines the button options. @interface ButtonOptions @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonOptions } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.65621006}]}