# @ohos.application.ConfigurationConstant (ConfigurationConstant)

The **ConfigurationConstant** module provides the enumerated values of the environment configuration information.

> **NOTE**
> 
> The APIs of this module are supported since API version 8 and deprecated since API version 9. You are advised to use [@ohos.app.ability.ConfigurationConstant](js-apis-app-ability-configurationConstant.md) instead. Newly added APIs will be marked with a superscript to indicate their earliest API version.

## Modules to Import

```ts
import ConfigurationConstant from '@ohos.application.ConfigurationConstant';
```

## ColorMode

Enumerates the color modes.

**System capability**: SystemCapability.Ability.AbilityBase

| Name| Value| Description| 
| -------- | -------- | -------- |
| COLOR_MODE_NOT_SET | -1 | Unspecified color mode.| 
| COLOR_MODE_DARK | 0 | Dark mode.| 
| COLOR_MODE_LIGHT | 1 | Light mode.| 
