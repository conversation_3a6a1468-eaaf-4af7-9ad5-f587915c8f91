# Introduction to Localization Kit


Users in different locales have different cultural backgrounds and speak different languages. Therefore, if your application is to be released for use in different locales, you must consider the language and cultural differences in different locales. Internationalization and localization can have your application UI displayed in line with local user habits. The improved user experience, in turn, will help your application to extend its reach to potential markets.


Internationalization (known as i18n) is a set of multilingual and multicultural capabilities, including locale-specific features as well as time zone and DST features. Where, locale-specific features provide access to setting the date and time, numbers and units of measurement, phone numbers, calendars, and languages of different locales, while the time zone and DST features provide access to obtaining the time zone and DST transition. Internationalization usually happens in the application design and development phase. It employs a universal design without specifying a dedicated language.


To help applications adapt to different markets, internationalization provides some general guidelines for application development. For example, DO NOT assume user cultures and habits. Specifically, do not assume commas as number group separators in all locales and hard code commas as the number group separators in the application code. Another example is to separate UI elements (such as images and character strings) from the code logic as application resources. To launch an application in certain locales, you just need to have the corresponding resources translated into the target language. This helps avoid code modification, eliminating the need for application redesign and development.


Localization (known as l10n) happens in the application customization phase. It is a process of translating and localizing application resources to meet users' language and cultural requirements in dedicated locales. It fulfills the needs for multilingual resource configuration, including resource translation, taboo check, and localization testing.


The purpose of multilingual resource configuration is to configure content proper for the desired country/locale and language so that the application UI can load and display the content in line with local user habits. Resource translation is a basic step in the localization process. It consists of UI element translation and code translation. Resources turn into multilingual resources only after being translated. To help improve efficiency, UI elements are separated from the code logic during translation. On completion of translation, UI elements are loaded to the application resource file of the corresponding language by type (such as image, audio, and video). When resources are loaded on the application UI, the corresponding resources are loaded and displayed based on the application language list.


The localization process also includes taboo check and localization testing. Taboo check helps you to identify the content that may lead to negative public opinions, ranging from politics to religion and culture, etc. Localization testing allows you to check for untranslated strings, incorrect translations, and improper UI layout and display.
