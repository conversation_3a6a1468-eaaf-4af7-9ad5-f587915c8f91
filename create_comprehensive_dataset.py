"""
Create a comprehensive test dataset for ArkTS import suggestion system.
This script creates a more diverse dataset with various component types and APIs.
"""

import os
import shutil
import json
import glob
from pathlib import Path

def create_directory(directory):
    """Create a directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def copy_file(source, destination):
    """Copy a file from source to destination."""
    try:
        shutil.copy2(source, destination)
        print(f"Copied: {source} -> {destination}")
        return True
    except Exception as e:
        print(f"Error copying {source}: {e}")
        return False

def find_files(base_dir, pattern):
    """Find files matching a pattern in a directory."""
    return glob.glob(os.path.join(base_dir, pattern), recursive=True)

def main():
    """Main function."""
    # Create test dataset directory
    test_dataset_dir = "comprehensive_dataset"
    create_directory(test_dataset_dir)
    
    # Base directories
    openharmony_dir = "Information\\default\\openharmony"
    hms_dir = "Information\\default\\hms"
    
    # Categories to include
    categories = [
        # UI Components
        {"name": "Basic UI", "patterns": [
            f"{openharmony_dir}\\ets\\component\\button*.d.*",
            f"{openharmony_dir}\\ets\\component\\text*.d.*",
            f"{openharmony_dir}\\ets\\component\\image*.d.*",
            f"{openharmony_dir}\\ets\\component\\icon*.d.*",
            f"{openharmony_dir}\\ets\\component\\label*.d.*",
            f"{openharmony_dir}\\ets\\component\\span*.d.*",
        ]},
        {"name": "Layout", "patterns": [
            f"{openharmony_dir}\\ets\\component\\column*.d.*",
            f"{openharmony_dir}\\ets\\component\\row*.d.*",
            f"{openharmony_dir}\\ets\\component\\flex*.d.*",
            f"{openharmony_dir}\\ets\\component\\grid*.d.*",
            f"{openharmony_dir}\\ets\\component\\list*.d.*",
            f"{openharmony_dir}\\ets\\component\\stack*.d.*",
        ]},
        {"name": "Navigation", "patterns": [
            f"{openharmony_dir}\\ets\\component\\navigator*.d.*",
            f"{openharmony_dir}\\ets\\component\\tabs*.d.*",
            f"{openharmony_dir}\\ets\\component\\tab_content*.d.*",
            f"{openharmony_dir}\\ets\\component\\navigation*.d.*",
        ]},
        {"name": "Container", "patterns": [
            f"{openharmony_dir}\\ets\\component\\scroll*.d.*",
            f"{openharmony_dir}\\ets\\component\\swiper*.d.*",
            f"{openharmony_dir}\\ets\\component\\panel*.d.*",
            f"{openharmony_dir}\\ets\\component\\refresh*.d.*",
        ]},
        {"name": "Dialog", "patterns": [
            f"{openharmony_dir}\\ets\\component\\alert_dialog*.d.*",
            f"{openharmony_dir}\\ets\\component\\custom_dialog*.d.*",
            f"{openharmony_dir}\\ets\\component\\dialog*.d.*",
            f"{openharmony_dir}\\ets\\api\\*dialog*.d.*",
        ]},
        {"name": "Form", "patterns": [
            f"{openharmony_dir}\\ets\\component\\checkbox*.d.*",
            f"{openharmony_dir}\\ets\\component\\radio*.d.*",
            f"{openharmony_dir}\\ets\\component\\toggle*.d.*",
            f"{openharmony_dir}\\ets\\component\\slider*.d.*",
            f"{openharmony_dir}\\ets\\component\\rating*.d.*",
            f"{openharmony_dir}\\ets\\component\\text_input*.d.*",
            f"{openharmony_dir}\\ets\\component\\text_area*.d.*",
            f"{openharmony_dir}\\ets\\component\\search*.d.*",
        ]},
        
        # System APIs
        {"name": "Multimedia", "patterns": [
            f"{openharmony_dir}\\ets\\api\\@ohos.multimedia*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.media*.d.*",
        ]},
        {"name": "Network", "patterns": [
            f"{openharmony_dir}\\ets\\api\\@ohos.net*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.request*.d.*",
        ]},
        {"name": "Storage", "patterns": [
            f"{openharmony_dir}\\ets\\api\\@ohos.data*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.storage*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.file*.d.*",
        ]},
        {"name": "Device", "patterns": [
            f"{openharmony_dir}\\ets\\api\\@ohos.bluetooth*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.sensor*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.device*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.wifi*.d.*",
        ]},
        {"name": "System", "patterns": [
            f"{openharmony_dir}\\ets\\api\\@ohos.app*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.bundle*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.os*.d.*",
            f"{openharmony_dir}\\ets\\api\\@ohos.power*.d.*",
        ]},
        
        # HMS APIs
        {"name": "HMS", "patterns": [
            f"{hms_dir}\\ets\\api\\@hms*.d.*",
        ]},
        
        # Advanced Components
        {"name": "Advanced", "patterns": [
            f"{openharmony_dir}\\ets\\api\\@ohos.arkui.advanced*.d.*",
        ]},
    ]
    
    # Find and copy files
    copied_files = []
    for category in categories:
        print(f"\nProcessing category: {category['name']}")
        category_files = []
        
        for pattern in category['patterns']:
            files = find_files(".", pattern)
            category_files.extend(files)
            print(f"  Found {len(files)} files matching pattern: {pattern}")
        
        # Create category directory
        category_dir = os.path.join(test_dataset_dir, category['name'].lower().replace(" ", "_"))
        create_directory(category_dir)
        
        # Copy files
        for file in category_files:
            destination = os.path.join(category_dir, os.path.basename(file))
            if copy_file(file, destination):
                copied_files.append(destination)
    
    # Copy our custom component files
    custom_files = [
        "test_dataset/button_component.d.ets",
        "test_dataset/dialog_component.d.ets",
        "test_dataset/text.d.ets",
        "test_dataset/image.d.ets"
    ]
    
    custom_dir = os.path.join(test_dataset_dir, "custom")
    create_directory(custom_dir)
    
    for file in custom_files:
        if os.path.exists(file):
            destination = os.path.join(custom_dir, os.path.basename(file))
            if copy_file(file, destination):
                copied_files.append(destination)
    
    # Create a list of copied files
    with open(os.path.join(test_dataset_dir, "files.txt"), "w") as f:
        for file in copied_files:
            f.write(f"{file}\n")
    
    print(f"\nCreated comprehensive test dataset with {len(copied_files)} files.")
    print(f"Files list saved to {os.path.join(test_dataset_dir, 'files.txt')}")

if __name__ == "__main__":
    main()
