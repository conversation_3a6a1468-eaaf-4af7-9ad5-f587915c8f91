# Singular/Plural Form Selection

Requirements on the singular and plural formats of nouns or unit expressions vary according to languages. Some languages do not distinguish singular and plural forms, while some other languages have two forms or more. For example, singular and plural forms are extensively used in English, but not Chinese. Different from English, quantifiers are used to express quantities in Chinese.

Singular and plural numbers are usually distinguished by the following categories:

- zero: 0 or numbers ending with 0

- one: 1 or numbers ending with 1

- two: numbers ending with 2

- few: numbers with a small value

- many: numbers with a large value

- other: other cases

For example, in Arabic, the rules are as follows:

- zero : 0

- one: 1

- two: 2

- few: 3 to 10, 103 to 110, 1003...

- many: 11 to 26, 111, 1011...

- other: 100 to 102, 200 to 202, 1000, 10000...

## How to Develop

For details about how to use the APIs, see [getPluralStringValueSync](../reference/apis-localization-kit/js-apis-resource-manager.md#getpluralstringvaluesync10).

```ts
import { BusinessError } from '@ohos.base'; 

try {  
  this.context.resourceManager.getPluralStringByNameSync("test", 1);} 
catch (error) {  
  let code = (error as BusinessError).code;  
  let message = (error as BusinessError).message;  
  console.error(`getPluralStringByNameSync failed, error code: ${code}, message: ${message}.`);
}
```
