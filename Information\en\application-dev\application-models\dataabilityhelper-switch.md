# DataAbilityHelper Switching


  | API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding API in the Stage Model| 
| -------- | -------- | -------- |
| [openFile(uri: string, mode: string, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperopenfile)<br>[openFile(uri: string, mode: string): Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperopenfile-1) | \@ohos.data.fileAccess.d.ts | [openFile(uri: string, flags: OPENFLAGS) : Promise&lt;number&gt;;](../reference/apis-core-file-kit/js-apis-fileAccess-sys.md#openfile)<br>[openFile(uri: string, flags: OPENFLAGS, callback: AsyncCallback&lt;number&gt;) : void;](../reference/apis-core-file-kit/js-apis-fileAccess-sys.md#openfile-1) |
| [on(type: 'dataChange', uri: string, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperondatachange) | \@ohos.data.dataShare.d.ts | [on(type: 'dataChange', uri: string, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#ondatachange) |
| [off(type: 'dataChange', uri: string, callback?: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperoffdatachange) | \@ohos.data.dataShare.d.ts | [off(type: 'dataChange', uri: string, callback?: AsyncCallback&lt;void&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#offdatachange) |
| [getType(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpergettype)<br>[getType(uri: string): Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpergettype-1) | There is no corresponding API in the stage model.| The stage model does not support cross-process URI access. You are advised to use [the want parameter to carry the file descriptor and file information](file-processing-apps-startup.md) for cross-process file access.|
| [getFileTypes(uri: string, mimeTypeFilter: string, callback: AsyncCallback&lt;Array&lt;string&gt;&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpergetfiletypes)<br>[getFileTypes(uri: string, mimeTypeFilter: string): Promise&lt;Array&lt;string&gt;&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpergetfiletypes-1) | There is no corresponding API in the stage model.| The stage model does not support cross-process URI access. You are advised to use [the want parameter to carry the file descriptor and file information](file-processing-apps-startup.md) for cross-process file access.|
| [normalizeUri(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpernormalizeuri)<br>[normalizeUri(uri: string): Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpernormalizeuri-1) | \@ohos.data.dataShare.d.ts | [normalizeUri(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#normalizeuri)<br>[normalizeUri(uri: string): Promise&lt;string&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#normalizeuri-1) |
| [denormalizeUri(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperdenormalizeuri)<br>[denormalizeUri(uri: string): Promise&lt;string&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperdenormalizeuri-1) | \@ohos.data.dataShare.d.ts | [denormalizeUri(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#denormalizeuri)<br>[denormalizeUri(uri: string): Promise&lt;string&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#denormalizeuri-1) |
| [notifyChange(uri: string, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpernotifychange)<br>[notifyChange(uri: string): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpernotifychange-1) | \@ohos.data.dataShare.d.ts | [notifyChange(uri: string, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#notifychange)<br>[notifyChange(uri: string): Promise&lt;void&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#notifychange-1) |
| [insert(uri: string, valuesBucket: rdb.ValuesBucket, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperinsert)<br>[insert(uri: string, valuesBucket: rdb.ValuesBucket): Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperinsert-1) | \@ohos.data.dataShare.d.ts | [insert(uri: string, value: ValuesBucket, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#insert)<br>[insert(uri: string, value: ValuesBucket): Promise&lt;number&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#insert-1) |
| [batchInsert(uri: string, valuesBuckets: Array&lt;rdb.ValuesBucket&gt;, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperbatchinsert)<br>[batchInsert(uri: string, valuesBuckets: Array&lt;rdb.ValuesBucket&gt;): Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperbatchinsert-1) | \@ohos.data.dataShare.d.ts | [batchInsert(uri: string, values: Array&lt;ValuesBucket&gt;, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#batchinsert)<br>[batchInsert(uri: string, values: Array&lt;ValuesBucket&gt;): Promise&lt;number&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#batchinsert-1) |
| [delete(uri: string, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperdelete)<br>[delete(uri: string, predicates?: dataAbility.DataAbilityPredicates): Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperdelete-1)<br>[delete(uri: string, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperdelete-2) | \@ohos.data.dataShare.d.ts | [delete(uri: string, predicates: dataSharePredicates.DataSharePredicates, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#delete)<br>[delete(uri: string, predicates: dataSharePredicates.DataSharePredicates): Promise&lt;number&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#delete-1) |
| [update(uri: string, valuesBucket: rdb.ValuesBucket, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperupdate)<br>[update(uri: string, valuesBucket: rdb.ValuesBucket, predicates?: dataAbility.DataAbilityPredicates): Promise&lt;number&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperupdate-1)<br>[update(uri: string, valuesBucket: rdb.ValuesBucket, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperupdate) | \@ohos.data.dataShare.d.ts | [update(uri: string, predicates: dataSharePredicates.DataSharePredicates, value: ValuesBucket, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#update)<br>[update(uri: string, predicates: dataSharePredicates.DataSharePredicates, value: ValuesBucket): Promise&lt;number&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#update-1) |
| [query(uri: string, columns: Array&lt;string&gt;, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;ResultSet&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperquery)<br>[query(uri: string, callback: AsyncCallback&lt;ResultSet&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperquery-1)<br>[query(uri: string, columns: Array&lt;string&gt;, callback: AsyncCallback&lt;ResultSet&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperquery-2)<br>[query(uri: string, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;ResultSet&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperquery-3)<br>[query(uri: string, columns?: Array&lt;string&gt;, predicates?: dataAbility.DataAbilityPredicates): Promise&lt;ResultSet&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperquery-4) | \@ohos.data.dataShare.d.ts | [query(uri: string, predicates: dataSharePredicates.DataSharePredicates, columns: Array&lt;string&gt;, callback: AsyncCallback&lt;DataShareResultSet&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#query)<br>[query(uri: string, predicates: dataSharePredicates.DataSharePredicates, columns: Array&lt;string&gt;): Promise&lt;DataShareResultSet&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#query-1) |
| [call(uri: string, method: string, arg: string, extras: PacMap, callback: AsyncCallback&lt;PacMap&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpercall)<br>[call(uri: string, method: string, arg: string, extras: PacMap): Promise&lt;PacMap&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelpercall-1) | There is no corresponding API in the stage model.| No corresponding API is provided.|
| [executeBatch(uri: string, operations: Array&lt;DataAbilityOperation&gt;, callback: AsyncCallback&lt;Array&lt;DataAbilityResult&gt;&gt;): void;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperexecutebatch)<br>[executeBatch(uri: string, operations: Array&lt;DataAbilityOperation&gt;): Promise&lt;Array&lt;DataAbilityResult&gt;&gt;;](../reference/apis-ability-kit/js-apis-inner-ability-dataAbilityHelper.md#dataabilityhelperexecutebatch-1) | There is no corresponding API in the stage model.| No corresponding API is provided.|
