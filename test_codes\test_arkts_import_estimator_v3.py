"""
Test script for ArkTS Import Estimator.

This script tests the functionality of the ArkTS Import Estimator by:
1. Indexing sample d.ts and d.ets files
2. Testing import suggestions
3. Testing nested symbol search
4. Testing hybrid search
"""

import os
import sys
import logging
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from arkts_import_estimator import ArkTSImportEstimator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("test_results/Test_ArkTS_Import_Estimator_Results/test_v3.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TestArkTSImportEstimator")

# Create results directory if it doesn't exist
os.makedirs("test_results/Test_ArkTS_Import_Estimator_Results", exist_ok=True)

# Collection name for tests
TEST_COLLECTION = "test_arkts_imports_v3"

def main():
    """Run all tests."""
    logger.info("Starting ArkTS Import Estimator tests...")
    
    # Initialize estimator with test collection
    estimator = ArkTSImportEstimator(
        qdrant_url="http://gmktec.ai-institute.uk:6333",
        collection_name=TEST_COLLECTION
    )
    
    # Step 1: Test indexing
    logger.info("Testing indexing functionality...")
    
    # Index sample files
    sample_dir = "Information/default/openharmony/ets/api"
    total_symbols = estimator.index_directory(sample_dir)
    
    logger.info(f"Indexed {total_symbols} symbols from {sample_dir}")
    
    indexing_success = total_symbols > 0
    
    # Step 2: Test import suggestions
    logger.info("Testing import suggestions functionality...")
    
    # Test queries
    test_queries = [
        "Button",
        "UIAbility",
        "Context",
        "Component",
        "interface"
    ]
    
    suggestions_success = True
    
    for query in test_queries:
        logger.info(f"Testing query: {query}")
        
        # Get suggestions
        suggestions = estimator.suggest_imports(query, limit=5, use_hybrid=True)
        
        # Log results
        logger.info(f"Found {len(suggestions)} suggestions for '{query}'")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"{i}. {suggestion['import_statement']} (Score: {suggestion['score']:.4f})")
            logger.info(f"   Type: {suggestion['symbol_type']}, Module: {suggestion['module_name']}")
            if suggestion['description']:
                logger.info(f"   Description: {suggestion['description'][:100]}...")
            if suggestion['is_nested']:
                logger.info(f"   Nested in: {suggestion['parent_symbol']}")
        
        # Check if suggestions were found
        if not suggestions:
            logger.warning(f"No suggestions found for '{query}'")
            suggestions_success = False
    
    # Step 3: Test nested symbols
    logger.info("Testing nested symbols functionality...")
    
    # Test parent symbols
    test_parents = [
        "UIAbility",
        "Context",
        "common"
    ]
    
    nested_success = True
    
    for parent in test_parents:
        logger.info(f"Testing parent symbol: {parent}")
        
        # Get nested symbols
        nested_symbols = estimator.search_nested_symbols(parent, limit=5)
        
        # Log results
        logger.info(f"Found {len(nested_symbols)} nested symbols for '{parent}'")
        for i, symbol in enumerate(nested_symbols, 1):
            logger.info(f"{i}. {symbol['symbol_name']} ({symbol['symbol_type']})")
            logger.info(f"   Import: {symbol['import_statement']}")
            if symbol.get('full_name'):
                logger.info(f"   Full name: {symbol['full_name']}")
            if symbol['description']:
                logger.info(f"   Description: {symbol['description'][:100]}...")
    
    # Step 4: Test hybrid search
    logger.info("Testing hybrid search functionality...")
    
    # Test queries
    test_queries = [
        "user interface component",
        "application context",
        "network request",
        "data storage"
    ]
    
    hybrid_success = True
    
    for query in test_queries:
        logger.info(f"Testing semantic query: {query}")
        
        # Get suggestions with hybrid search
        hybrid_suggestions = estimator.suggest_imports(query, limit=5, use_hybrid=True)
        
        # Get suggestions with vector search only
        vector_suggestions = estimator.suggest_imports(query, limit=5, use_hybrid=False)
        
        # Log results
        logger.info(f"Found {len(hybrid_suggestions)} hybrid suggestions for '{query}'")
        for i, suggestion in enumerate(hybrid_suggestions, 1):
            logger.info(f"{i}. {suggestion['import_statement']} (Score: {suggestion['score']:.4f})")
        
        logger.info(f"Found {len(vector_suggestions)} vector suggestions for '{query}'")
        for i, suggestion in enumerate(vector_suggestions, 1):
            logger.info(f"{i}. {suggestion['import_statement']} (Score: {suggestion['score']:.4f})")
        
        # Check if suggestions were found
        if not hybrid_suggestions and not vector_suggestions:
            logger.warning(f"No suggestions found for '{query}'")
            hybrid_success = False
    
    # Step 5: Test filtering by symbol type
    logger.info("Testing filtering by symbol type...")
    
    # Test type filters
    test_filters = [
        ("component", "UI"),
        ("class", "Ability"),
        ("interface", "Data"),
        ("namespace", "common")
    ]
    
    filter_success = True
    
    for symbol_type, query in test_filters:
        logger.info(f"Testing filter: type={symbol_type}, query={query}")
        
        # Get filtered suggestions
        suggestions = estimator.filter_suggestions_by_type(query, symbol_type, limit=5)
        
        # Log results
        logger.info(f"Found {len(suggestions)} {symbol_type} suggestions for '{query}'")
        for i, suggestion in enumerate(suggestions, 1):
            logger.info(f"{i}. {suggestion['import_statement']} (Score: {suggestion['score']:.4f})")
            logger.info(f"   Type: {suggestion['symbol_type']}, Module: {suggestion['module_name']}")
        
        # Verify all suggestions have the correct type
        for suggestion in suggestions:
            if suggestion['symbol_type'] != symbol_type:
                logger.error(f"Suggestion has incorrect type: {suggestion['symbol_type']} != {symbol_type}")
                filter_success = False
    
    # Log results
    logger.info("\nTest Results:")
    logger.info(f"Indexing Test: {'PASSED' if indexing_success else 'FAILED'}")
    logger.info(f"Import Suggestions Test: {'PASSED' if suggestions_success else 'FAILED'}")
    logger.info(f"Nested Symbols Test: {'PASSED' if nested_success else 'FAILED'}")
    logger.info(f"Hybrid Search Test: {'PASSED' if hybrid_success else 'FAILED'}")
    logger.info(f"Filter by Type Test: {'PASSED' if filter_success else 'FAILED'}")
    
    # Overall result
    overall_success = all([
        indexing_success,
        suggestions_success,
        nested_success,
        hybrid_success,
        filter_success
    ])
    
    logger.info(f"\nOverall Test Result: {'PASSED' if overall_success else 'FAILED'}")
    
    return overall_success

if __name__ == "__main__":
    # Create test directory if it doesn't exist
    os.makedirs("test_codes", exist_ok=True)
    os.makedirs("test_results/Test_ArkTS_Import_Estimator_Results", exist_ok=True)
    
    # Run tests
    success = main()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
