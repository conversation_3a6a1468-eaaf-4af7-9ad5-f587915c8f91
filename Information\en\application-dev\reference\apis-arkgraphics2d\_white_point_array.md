# WhitePointArray


## Overview

The WhitePointArray struct describes a white point array. Each white point indicates the coordinates of white in the active color space.

**Since**: 13

**Related module**: [NativeColorSpaceManager](_native_color_space_manager.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| float [arr](#arr) [2] | White point array.| 


## Member Variable Description


### arr

```
float WhitePointArray::arr[2]
```

**Description**

White point array.
