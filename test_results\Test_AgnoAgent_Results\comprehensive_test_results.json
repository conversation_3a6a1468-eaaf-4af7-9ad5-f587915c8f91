[{"test_name": "UI_Component_Button", "function": "search_component", "query": "button", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Text", "function": "search_component", "query": "text", "expected_symbols": ["Text"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Image", "function": "search_component", "query": "image", "expected_symbols": ["Image"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_List", "function": "search_component", "query": "list", "expected_symbols": ["List"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Grid", "function": "search_component", "query": "grid", "expected_symbols": ["Grid"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Column", "function": "search_component", "query": "column", "expected_symbols": ["Column"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Row", "function": "search_component", "query": "row", "expected_symbols": ["Row"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Stack", "function": "search_component", "query": "stack", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Flex", "function": "search_component", "query": "flex", "expected_symbols": ["Flex"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Scroll", "function": "search_component", "query": "scroll", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Tabs", "function": "search_component", "query": "tabs", "expected_symbols": ["Tabs"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Navigation", "function": "search_component", "query": "navigation", "expected_symbols": ["Navigation"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Swiper", "function": "search_component", "query": "swiper", "expected_symbols": ["Swiper"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Progress", "function": "search_component", "query": "progress", "expected_symbols": ["Progress"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Slider", "function": "search_component", "query": "slider", "expected_symbols": ["Slide<PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Toggle", "function": "search_component", "query": "toggle", "expected_symbols": ["Toggle"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Radio", "function": "search_component", "query": "radio", "expected_symbols": ["Radio"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Checkbox", "function": "search_component", "query": "checkbox", "expected_symbols": ["Checkbox"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_TextInput", "function": "search_component", "query": "text input", "expected_symbols": ["TextInput"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_TextArea", "function": "search_component", "query": "text area", "expected_symbols": ["TextArea"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Search", "function": "search_component", "query": "search", "expected_symbols": ["Search"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Select", "function": "search_component", "query": "select", "expected_symbols": ["Select"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_DatePicker", "function": "search_component", "query": "date picker", "expected_symbols": ["DatePicker"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_TimePicker", "function": "search_component", "query": "time picker", "expected_symbols": ["TimePicker"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Calendar", "function": "search_component", "query": "calendar", "expected_symbols": ["CalendarPicker"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Badge", "function": "search_component", "query": "badge", "expected_symbols": ["Badge"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Divider", "function": "search_component", "query": "divider", "expected_symbols": ["Divider"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Loading", "function": "search_component", "query": "loading", "expected_symbols": ["LoadingProgress"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Rating", "function": "search_component", "query": "rating", "expected_symbols": ["Rating"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "UI_Component_Gauge", "function": "search_component", "query": "gauge", "expected_symbols": ["Gauge"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "UI_Components"}, {"test_name": "Layout_GridRow", "function": "search_component", "query": "grid row", "expected_symbols": ["GridRow"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_GridCol", "function": "search_component", "query": "grid column", "expected_symbols": ["GridCol"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_RelativeContainer", "function": "search_component", "query": "relative container", "expected_symbols": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_WaterFlow", "function": "search_component", "query": "water flow", "expected_symbols": ["WaterFlow"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_FlowItem", "function": "search_component", "query": "flow item", "expected_symbols": ["FlowItem"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_ListItem", "function": "search_component", "query": "list item", "expected_symbols": ["ListItem"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_GridItem", "function": "search_component", "query": "grid item", "expected_symbols": ["GridItem"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_TabContent", "function": "search_component", "query": "tab content", "expected_symbols": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_Panel", "function": "search_component", "query": "panel", "expected_symbols": ["Panel"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_SideBar", "function": "search_component", "query": "sidebar", "expected_symbols": ["SideBarContainer"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_Refresh", "function": "search_component", "query": "refresh", "expected_symbols": ["Refresh"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_Stepper", "function": "search_component", "query": "stepper", "expected_symbols": ["Stepper"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_StepperItem", "function": "search_component", "query": "stepper item", "expected_symbols": ["StepperItem"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_NavDestination", "function": "search_component", "query": "nav destination", "expected_symbols": ["NavDestination"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Layout_NavRouter", "function": "search_component", "query": "nav router", "expected_symbols": ["NavR<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Layout_Components"}, {"test_name": "Advanced_Dialog", "function": "search_component", "query": "dialog", "expected_symbols": ["Dialog"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Popup", "function": "search_component", "query": "popup", "expected_symbols": ["Popup"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Menu", "function": "search_component", "query": "menu", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_MenuItem", "function": "search_component", "query": "menu item", "expected_symbols": ["MenuItem"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_ActionSheet", "function": "search_component", "query": "action sheet", "expected_symbols": ["ActionSheet"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_AlertDialog", "function": "search_component", "query": "alert dialog", "expected_symbols": ["AlertDialog"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_CustomDialog", "function": "search_component", "query": "custom dialog", "expected_symbols": ["CustomDialogController"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_RichText", "function": "search_component", "query": "rich text", "expected_symbols": ["RichText"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_RichEditor", "function": "search_component", "query": "rich editor", "expected_symbols": ["RichEditor"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Web", "function": "search_component", "query": "web", "expected_symbols": ["Web"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Video", "function": "search_component", "query": "video", "expected_symbols": ["Video"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Canvas", "function": "search_component", "query": "canvas", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_XComponent", "function": "search_component", "query": "xcomponent", "expected_symbols": ["XComponent"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Particle", "function": "search_component", "query": "particle", "expected_symbols": ["Particle"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_Component3D", "function": "search_component", "query": "3d component", "expected_symbols": ["Component3D"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_SymbolGlyph", "function": "search_component", "query": "symbol glyph", "expected_symbols": ["SymbolGlyph"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_SymbolSpan", "function": "search_component", "query": "symbol span", "expected_symbols": ["SymbolSpan"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_ImageSpan", "function": "search_component", "query": "image span", "expected_symbols": ["ImageSpan"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_ContainerSpan", "function": "search_component", "query": "container span", "expected_symbols": ["ContainerSpan"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Advanced_StyledString", "function": "search_component", "query": "styled string", "expected_symbols": ["StyledString"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_component() missing 1 required positional argument: 'self'", "category": "Advanced_Components"}, {"test_name": "Import_Path_Button_Import", "function": "search_import_path", "query": "<PERSON><PERSON>", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_Text_Import", "function": "search_import_path", "query": "Text", "expected_symbols": ["Text"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_Image_Import", "function": "search_import_path", "query": "Image", "expected_symbols": ["Image"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_List_Import", "function": "search_import_path", "query": "List", "expected_symbols": ["List"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_Grid_Import", "function": "search_import_path", "query": "Grid", "expected_symbols": ["Grid"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_UIAbility_Import", "function": "search_import_path", "query": "UIAbility", "expected_symbols": ["UIAbility"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_router_Import", "function": "search_import_path", "query": "router", "expected_symbols": ["router"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_preferences_Import", "function": "search_import_path", "query": "preferences", "expected_symbols": ["preferences"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_http_Import", "function": "search_import_path", "query": "http", "expected_symbols": ["http"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_fs_Import", "function": "search_import_path", "query": "fs", "expected_symbols": ["fs"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_audio_Import", "function": "search_import_path", "query": "audio", "expected_symbols": ["audio"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_camera_Import", "function": "search_import_path", "query": "camera", "expected_symbols": ["camera"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_bluetooth_Import", "function": "search_import_path", "query": "bluetooth", "expected_symbols": ["bluetooth"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_wifi_Import", "function": "search_import_path", "query": "wifi", "expected_symbols": ["wifi"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "Import_Path_window_Import", "function": "search_import_path", "query": "window", "expected_symbols": ["window"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_import_path() missing 1 required positional argument: 'self'", "category": "Import_Paths"}, {"test_name": "System_API_UIAbility", "function": "search_arkts_api", "query": "ui ability", "expected_symbols": ["UIAbility"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_AbilityStage", "function": "search_arkts_api", "query": "ability stage", "expected_symbols": ["AbilityStage"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Want", "function": "search_arkts_api", "query": "want", "expected_symbols": ["Want"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Context", "function": "search_arkts_api", "query": "context", "expected_symbols": ["Context"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Configuration", "function": "search_arkts_api", "query": "configuration", "expected_symbols": ["Configuration"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Router", "function": "search_arkts_api", "query": "router", "expected_symbols": ["router"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Preferences", "function": "search_arkts_api", "query": "preferences", "expected_symbols": ["preferences"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_RelationalStore", "function": "search_arkts_api", "query": "relational store", "expected_symbols": ["relationalStore"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_FileSystem", "function": "search_arkts_api", "query": "file system", "expected_symbols": ["fs"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_HTTP", "function": "search_arkts_api", "query": "http", "expected_symbols": ["http"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Bluetooth", "function": "search_arkts_api", "query": "bluetooth", "expected_symbols": ["bluetooth"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_WiFi", "function": "search_arkts_api", "query": "wifi", "expected_symbols": ["wifi"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Location", "function": "search_arkts_api", "query": "location", "expected_symbols": ["geoLocationManager"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Sensor", "function": "search_arkts_api", "query": "sensor", "expected_symbols": ["sensor"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Camera", "function": "search_arkts_api", "query": "camera", "expected_symbols": ["camera"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Audio", "function": "search_arkts_api", "query": "audio", "expected_symbols": ["audio"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Notification", "function": "search_arkts_api", "query": "notification", "expected_symbols": ["notificationManager"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Vibrator", "function": "search_arkts_api", "query": "vibrator", "expected_symbols": ["vibrator"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Battery", "function": "search_arkts_api", "query": "battery", "expected_symbols": ["batteryInfo"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Display", "function": "search_arkts_api", "query": "display", "expected_symbols": ["display"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Window", "function": "search_arkts_api", "query": "window", "expected_symbols": ["window"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Accessibility", "function": "search_arkts_api", "query": "accessibility", "expected_symbols": ["accessibility"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Security", "function": "search_arkts_api", "query": "security", "expected_symbols": ["cryptoFramework"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Account", "function": "search_arkts_api", "query": "account", "expected_symbols": ["appAccount"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "System_API_Bundle", "function": "search_arkts_api", "query": "bundle", "expected_symbols": ["bundleManager"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.search_arkts_api() missing 1 required positional argument: 'self'", "category": "System_APIs"}, {"test_name": "Agent_Query_105", "function": "handle_agent_query", "query": "ArkTS search: 'Button component'", "expected_symbols": ["<PERSON><PERSON>"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_106", "function": "handle_agent_query", "query": "ArkTS search: 'Text display'", "expected_symbols": ["Text"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_107", "function": "handle_agent_query", "query": "ArkTS search: 'Image component'", "expected_symbols": ["Image"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_108", "function": "handle_agent_query", "query": "ArkTS search: 'List component'", "expected_symbols": ["List"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_109", "function": "handle_agent_query", "query": "ArkTS search: 'Grid layout'", "expected_symbols": ["Grid"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_110", "function": "handle_agent_query", "query": "ArkTS search: 'UIAbility class'", "expected_symbols": ["UIAbility"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_111", "function": "handle_agent_query", "query": "ArkTS search: 'router navigation'", "expected_symbols": ["router"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_112", "function": "handle_agent_query", "query": "ArkTS search: 'http requests'", "expected_symbols": ["http"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_113", "function": "handle_agent_query", "query": "ArkTS search: 'file system'", "expected_symbols": ["fs"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_114", "function": "handle_agent_query", "query": "ArkTS search: 'preferences storage'", "expected_symbols": ["preferences"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_115", "function": "handle_agent_query", "query": "ArkTS search: 'camera functionality'", "expected_symbols": ["camera"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_116", "function": "handle_agent_query", "query": "ArkTS search: 'audio playback'", "expected_symbols": ["audio"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_117", "function": "handle_agent_query", "query": "ArkTS search: 'bluetooth connection'", "expected_symbols": ["bluetooth"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_118", "function": "handle_agent_query", "query": "ArkTS search: 'wifi management'", "expected_symbols": ["wifi"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}, {"test_name": "Agent_Query_119", "function": "handle_agent_query", "query": "ArkTS search: 'window operations'", "expected_symbols": ["window"], "result": null, "success": false, "analysis": "Exception occurred: ArkTSImportTools.handle_agent_query() missing 1 required positional argument: 'self'", "category": "Agent_Queries"}]