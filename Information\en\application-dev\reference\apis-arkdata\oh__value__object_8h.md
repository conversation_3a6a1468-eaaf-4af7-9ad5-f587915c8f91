# oh_value_object.h


## Overview

Provides type conversion methods.

**File to include**: <database/rdb/oh_value_object.h>

**Library**: libnative_rdb_ndk.z.so

**Since**: 10

**Related module**: [RDB](_r_d_b.md)


## Summary


### Structs

| Name| Description|
| -------- | -------- |
| [OH_VObject](_o_h___v_object.md) | Defines the allowed data field types.|


### Types

| Name| Description|
| -------- | -------- |
| [OH_VObject](_r_d_b.md#oh_vobject) | Indicates the allowed data field types. |
