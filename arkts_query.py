"""
ArkTS Query

This module provides functionality to query ArkTS import suggestions.
It searches for symbols in the Qdrant database and returns import suggestions.
It also provides specialized search functions for Agno Agent integration.
"""

import time
import logging
import argparse
import requests
import threading
import concurrent.futures
from typing import List, Dict, Any, Optional, Union, Callable
from qdrant_client import QdrantClient
from qdrant_client.http import models
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

# Import configuration
import config

# Import compatibility layer
from qdrant_compatibility import qdrant_compatibility

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format=config.LOG_FORMAT,
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSQuery")


class ArkTSQuery:
    """ArkTS Query for suggesting import statements."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None,
                 ollama_url: str = None, embedding_model: str = None):
        """Initialize the query.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the Ollama embedding model to use
        """
        self.qdrant_url = qdrant_url or config.QDRANT_URL
        self.collection_name = collection_name or config.COLLECTION_NAME
        self.ollama_url = ollama_url or config.OLLAMA_URL
        self.embedding_model = embedding_model or config.EMBEDDING_MODEL

        # Set vector size based on embedding model
        self.vector_size = config.VECTOR_SIZES.get(self.embedding_model, config.DEFAULT_VECTOR_SIZE)

        # Initialize Qdrant client with compatibility check disabled
        self.client = QdrantClient(
            url=self.qdrant_url,
            prefer_grpc=False,
            timeout=60.0,
            check_compatibility=False  # Skip version compatibility check
        )

        # Configure connection pooling if using httpx client
        if hasattr(self.client, '_client') and hasattr(self.client._client, 'openapi_client'):
            try:
                # Try to set connection pool limits for httpx client
                if hasattr(self.client._client.openapi_client.client, '_client'):
                    httpx_client = self.client._client.openapi_client.client._client
                    if hasattr(httpx_client, 'transport') and hasattr(httpx_client.transport, 'pool'):
                        # Set pool limits
                        httpx_client.transport.pool.max_connections = config.CONNECTION_POOL_SIZE
                        logger.info(f"Set connection pool size to {config.CONNECTION_POOL_SIZE}")
            except Exception as e:
                logger.warning(f"Failed to configure connection pooling: {str(e)}")

        logger.info(f"ArkTSQuery initialized with Qdrant at {self.qdrant_url} and Ollama at {self.ollama_url}")
        logger.info(f"Using embedding model {self.embedding_model} with vector size {self.vector_size}")

    def _get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama.

        Args:
            text: Text to embed

        Returns:
            Embedding vector as a list of floats
        """
        # Maximum retry attempts
        max_retries = config.MAX_RETRIES
        retry_delay = config.RETRY_DELAY

        for attempt in range(max_retries):
            try:
                # Call Ollama API to get embedding
                response = requests.post(
                    f"{self.ollama_url}/api/embeddings",
                    json={
                        "model": self.embedding_model,
                        "prompt": text
                    },
                    timeout=config.REQUEST_TIMEOUT
                )

                # Check if request was successful
                if response.status_code == 200:
                    # Extract embedding from response
                    embedding = response.json().get("embedding", [])

                    # Verify embedding dimensions
                    if len(embedding) == self.vector_size:
                        return embedding
                    else:
                        logger.warning(f"Embedding dimension mismatch: expected {self.vector_size}, got {len(embedding)}")
                        # Try to pad or truncate to match expected size
                        if len(embedding) > self.vector_size:
                            logger.warning(f"Truncating embedding from {len(embedding)} to {self.vector_size}")
                            return embedding[:self.vector_size]
                        else:
                            logger.warning(f"Padding embedding from {len(embedding)} to {self.vector_size}")
                            return embedding + [0.0] * (self.vector_size - len(embedding))
                else:
                    logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {response.status_code} {response.text}")

            except requests.exceptions.ConnectionError:
                logger.error(f"Connection error to Ollama server (attempt {attempt+1}/{max_retries}). Is Ollama running?")
            except requests.exceptions.Timeout:
                logger.error(f"Timeout connecting to Ollama server (attempt {attempt+1}/{max_retries})")
            except Exception as e:
                logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {str(e)}")

            # Wait before retrying
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff

        # If all retries failed, return a zero vector
        logger.error(f"All {max_retries} attempts to get embedding failed. Using zero vector.")
        return [0.0] * self.vector_size

    def _get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Get embeddings for a batch of texts.

        This is more efficient than calling _get_embedding multiple times.

        Args:
            texts: List of texts to embed

        Returns:
            List of embedding vectors
        """
        if not texts:
            return []

        # If only one text, use single embedding method
        if len(texts) == 1:
            return [self._get_embedding(texts[0])]

        # Maximum retry attempts
        max_retries = config.MAX_RETRIES
        retry_delay = config.RETRY_DELAY

        # Maximum batch size to avoid overloading the server
        max_batch_size = getattr(config, "MAX_EMBEDDING_BATCH_SIZE", 10)

        # Process in smaller batches if needed
        all_embeddings = []
        for i in range(0, len(texts), max_batch_size):
            batch = texts[i:i + max_batch_size]
            logger.debug(f"Processing embedding batch {i//max_batch_size + 1}/{(len(texts) + max_batch_size - 1)//max_batch_size}")

            # Try to get embeddings for this batch
            for attempt in range(max_retries):
                try:
                    # Ollama doesn't support batch embeddings directly, so we need to make multiple requests
                    # But we can use connection pooling to make it more efficient
                    with requests.Session() as session:
                        embeddings = []
                        for text in batch:
                            response = session.post(
                                f"{self.ollama_url}/api/embeddings",
                                json={
                                    "model": self.embedding_model,
                                    "prompt": text
                                },
                                timeout=config.REQUEST_TIMEOUT
                            )

                            if response.status_code == 200:
                                embedding = response.json().get("embedding", [])

                                # Verify embedding dimensions
                                if len(embedding) != self.vector_size:
                                    logger.warning(f"Embedding dimension mismatch: expected {self.vector_size}, got {len(embedding)}")
                                    # Pad or truncate
                                    if len(embedding) > self.vector_size:
                                        embedding = embedding[:self.vector_size]
                                    else:
                                        embedding = embedding + [0.0] * (self.vector_size - len(embedding))

                                embeddings.append(embedding)
                            else:
                                raise Exception(f"Error: {response.status_code} {response.text}")

                    # If we got here, all embeddings were successful
                    all_embeddings.extend(embeddings)
                    break

                except Exception as e:
                    logger.error(f"Error getting batch embeddings (attempt {attempt+1}/{max_retries}): {str(e)}")

                    # Wait before retrying
                    if attempt < max_retries - 1:
                        time.sleep(retry_delay)
                        retry_delay *= 2  # Exponential backoff
                    else:
                        # If all retries failed, return zero vectors for this batch
                        logger.error(f"All {max_retries} attempts to get batch embeddings failed. Using zero vectors.")
                        all_embeddings.extend([[0.0] * self.vector_size for _ in batch])

        return all_embeddings

    def suggest_imports(self, query: str, limit: int = None, use_hybrid: bool = None) -> List[Dict[str, Any]]:
        """Suggest import statements for a query.

        Args:
            query: Query string
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search (vector + text)

        Returns:
            List of import suggestions
        """
        limit = limit or config.DEFAULT_LIMIT
        use_hybrid = use_hybrid if use_hybrid is not None else config.DEFAULT_USE_HYBRID

        try:
            # Generate query embedding using Ollama
            query_vector = self._get_embedding(query)

            if use_hybrid:
                try:
                    # Try with newer API
                    # Hybrid search (vector + text)
                    results = qdrant_compatibility.search(
                        client=self.client,
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        query_filter=models.Filter(
                            should=[
                                models.FieldCondition(
                                    key="symbol_name",
                                    match=models.MatchText(text=query)
                                ),
                                models.FieldCondition(
                                    key="description",
                                    match=models.MatchText(text=query)
                                )
                            ]
                        ),
                        limit=limit
                    )
                except Exception as e:
                    logger.warning(f"Hybrid search with newer API failed: {str(e)}")
                    # Fall back to vector search only
                    logger.warning("Falling back to vector search only")
                    results = qdrant_compatibility.search(
                        client=self.client,
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        limit=limit
                    )
            else:
                # Vector search only
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    limit=limit
                )

            # Format results
            suggestions = []
            for result in results:
                suggestion = result.payload
                suggestion['score'] = result.score
                suggestions.append(suggestion)

            return suggestions
        except Exception as e:
            logger.error(f"Error suggesting imports: {str(e)}")
            return []

    def _process_single_query(self, query: str, query_vector: List[float], limit: int, use_hybrid: bool) -> List[Dict[str, Any]]:
        """Process a single query with its vector.

        This is a helper method for parallel processing.

        Args:
            query: Query string
            query_vector: Query embedding vector
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search

        Returns:
            List of import suggestions
        """
        try:
            if use_hybrid:
                # Hybrid search (vector + text)
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        should=[
                            models.FieldCondition(
                                key="symbol_name",
                                match=models.MatchText(text=query)
                            ),
                            models.FieldCondition(
                                key="description",
                                match=models.MatchText(text=query)
                            )
                        ]
                    ),
                    limit=limit
                )
            else:
                # Vector search only
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    limit=limit
                )

            # Format results
            suggestions = []
            for result in results:
                suggestion = result.payload
                suggestion['score'] = result.score
                suggestions.append(suggestion)

            return suggestions

        except Exception as e:
            logger.error(f"Error processing query '{query}': {str(e)}")
            return []  # Empty results for this query

    def suggest_imports_batch(self, queries: List[str], limit: int = None, use_hybrid: bool = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Suggest import statements for multiple queries in batch.

        This is more efficient than calling suggest_imports multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            queries: List of query strings
            limit: Maximum number of results per query
            use_hybrid: Whether to use hybrid search (vector + text)
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of import suggestions, one list per query
        """
        if not queries:
            return []

        # If only one query, use single query method
        if len(queries) == 1:
            return [self.suggest_imports(queries[0], limit, use_hybrid)]

        limit = limit or config.DEFAULT_LIMIT
        use_hybrid = use_hybrid if use_hybrid is not None else config.DEFAULT_USE_HYBRID

        # Determine whether to use parallel processing
        use_parallel = parallel and len(queries) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        try:
            # Generate query embeddings in batch
            query_vectors = self._get_embeddings_batch(queries)

            # Initialize results list with empty lists
            all_results = [[] for _ in range(len(queries))]

            if use_parallel:
                logger.info(f"Processing {len(queries)} queries in parallel with {min(config.MAX_WORKERS, len(queries))} workers")

                # Define a worker function for parallel processing
                def worker(idx, query, query_vector):
                    logger.debug(f"Worker processing query {idx+1}/{len(queries)}: '{query}'")
                    return idx, self._process_single_query(query, query_vector, limit, use_hybrid)

                # Process queries in parallel using a thread pool
                with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(queries))) as executor:
                    # Submit all tasks
                    future_to_idx = {
                        executor.submit(worker, i, query, query_vector): i
                        for i, (query, query_vector) in enumerate(zip(queries, query_vectors))
                    }

                    # Process results as they complete
                    for future in as_completed(future_to_idx, timeout=config.THREAD_TIMEOUT):
                        try:
                            idx, result = future.result()
                            all_results[idx] = result
                        except Exception as e:
                            idx = future_to_idx[future]
                            logger.error(f"Worker for query '{queries[idx]}' failed: {str(e)}")
                            all_results[idx] = []  # Empty results for this query
            else:
                # Process queries sequentially
                logger.info(f"Processing {len(queries)} queries sequentially")
                for i, (query, query_vector) in enumerate(zip(queries, query_vectors)):
                    logger.debug(f"Processing batch query {i+1}/{len(queries)}: '{query}'")
                    all_results[i] = self._process_single_query(query, query_vector, limit, use_hybrid)

            return all_results

        except Exception as e:
            logger.error(f"Error in batch import suggestions: {str(e)}")
            return [[] for _ in queries]  # Return empty results for all queries

    def filter_suggestions_by_type(self, query: str, symbol_type: str, limit: int = None) -> List[Dict[str, Any]]:
        """Filter import suggestions by symbol type.

        Args:
            query: Query string
            symbol_type: Symbol type to filter by
            limit: Maximum number of results

        Returns:
            List of filtered import suggestions
        """
        limit = limit or config.DEFAULT_LIMIT

        try:
            # Generate query embedding using Ollama
            query_vector = self._get_embedding(query)

            try:
                # Try with newer API
                # Search with type filter
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="symbol_type",
                                match=models.MatchValue(value=symbol_type)
                            )
                        ]
                    ),
                    limit=limit
                )
            except Exception as e:
                logger.warning(f"Type filter search with newer API failed: {str(e)}")
                # Fall back to vector search only
                logger.warning("Falling back to vector search only")
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    limit=limit
                )

            # Format results
            suggestions = []
            for result in results:
                suggestion = result.payload
                suggestion['score'] = result.score
                suggestions.append(suggestion)

            return suggestions
        except Exception as e:
            logger.error(f"Error filtering suggestions: {str(e)}")
            return []

    def search_nested_symbols(self, parent_symbol: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for nested symbols within a parent symbol.

        Args:
            parent_symbol: Parent symbol name
            limit: Maximum number of results

        Returns:
            List of nested symbols
        """
        limit = limit or config.DEFAULT_LIMIT

        try:
            # Create a dummy vector for search (required by Qdrant)
            dummy_vector = [0.0] * self.vector_size

            try:
                # Try with newer API
                # Search with parent filter
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=dummy_vector,
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="parent_symbol",
                                match=models.MatchValue(value=parent_symbol)
                            )
                        ]
                    ),
                    limit=limit
                )
            except Exception as e:
                logger.warning(f"Parent filter search with newer API failed: {str(e)}")
                # Fall back to vector search only
                logger.warning("Falling back to vector search only")
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=dummy_vector,
                    limit=limit
                )

            # Format results
            nested_symbols = []
            for result in results:
                symbol = result.payload
                symbol['score'] = result.score
                nested_symbols.append(symbol)

            return nested_symbols
        except Exception as e:
            logger.error(f"Error searching nested symbols: {str(e)}")
            return []

    def _process_single_nested_symbols(self, parent_symbol: str, limit: int) -> List[Dict[str, Any]]:
        """Process a single nested symbols search.

        This is a helper method for parallel processing.

        Args:
            parent_symbol: Parent symbol name
            limit: Maximum number of results

        Returns:
            List of nested symbols
        """
        try:
            # Create a dummy vector for search (required by Qdrant)
            dummy_vector = [0.0] * self.vector_size

            # Search with parent filter
            results = qdrant_compatibility.search(
                client=self.client,
                collection_name=self.collection_name,
                query_vector=dummy_vector,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="parent_symbol",
                            match=models.MatchValue(value=parent_symbol)
                        )
                    ]
                ),
                limit=limit
            )

            # Format results
            nested_symbols = []
            for result in results:
                symbol = result.payload
                symbol['score'] = result.score
                nested_symbols.append(symbol)

            return nested_symbols

        except Exception as e:
            logger.warning(f"Parent filter search failed for '{parent_symbol}': {str(e)}")
            # Fall back to vector search only
            try:
                # Create a dummy vector for search (required by Qdrant)
                dummy_vector = [0.0] * self.vector_size

                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=dummy_vector,
                    limit=limit
                )

                # Filter results by parent symbol
                nested_symbols = []
                for result in results:
                    if result.payload.get('parent_symbol') == parent_symbol:
                        symbol = result.payload
                        symbol['score'] = result.score
                        nested_symbols.append(symbol)

                return nested_symbols
            except Exception as fallback_error:
                logger.error(f"Fallback also failed for '{parent_symbol}': {str(fallback_error)}")
                return []  # Empty results for this parent symbol

    def search_nested_symbols_batch(self, parent_symbols: List[str], limit: int = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Search for nested symbols within multiple parent symbols in batch.

        This is more efficient than calling search_nested_symbols multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            parent_symbols: List of parent symbol names
            limit: Maximum number of results per parent symbol
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of nested symbols, one list per parent symbol
        """
        if not parent_symbols:
            return []

        # If only one parent symbol, use single parent method
        if len(parent_symbols) == 1:
            return [self.search_nested_symbols(parent_symbols[0], limit)]

        limit = limit or config.DEFAULT_LIMIT

        # Determine whether to use parallel processing
        use_parallel = parallel and len(parent_symbols) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        try:
            # Initialize results list with empty lists
            all_results = [[] for _ in range(len(parent_symbols))]

            if use_parallel:
                logger.info(f"Processing {len(parent_symbols)} nested symbols searches in parallel with {min(config.MAX_WORKERS, len(parent_symbols))} workers")

                # Define a worker function for parallel processing
                def worker(idx, parent_symbol):
                    logger.debug(f"Worker processing nested symbols search {idx+1}/{len(parent_symbols)}: '{parent_symbol}'")
                    return idx, self._process_single_nested_symbols(parent_symbol, limit)

                # Process parent symbols in parallel using a thread pool
                with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(parent_symbols))) as executor:
                    # Submit all tasks
                    future_to_idx = {
                        executor.submit(worker, i, parent_symbol): i
                        for i, parent_symbol in enumerate(parent_symbols)
                    }

                    # Process results as they complete
                    for future in as_completed(future_to_idx, timeout=config.THREAD_TIMEOUT):
                        try:
                            idx, result = future.result()
                            all_results[idx] = result
                        except Exception as e:
                            idx = future_to_idx[future]
                            logger.error(f"Worker for parent symbol '{parent_symbols[idx]}' failed: {str(e)}")
                            all_results[idx] = []  # Empty results for this parent symbol
            else:
                # Process parent symbols sequentially
                logger.info(f"Processing {len(parent_symbols)} nested symbols searches sequentially")
                for i, parent_symbol in enumerate(parent_symbols):
                    logger.debug(f"Processing batch nested symbols search {i+1}/{len(parent_symbols)}: '{parent_symbol}'")
                    all_results[i] = self._process_single_nested_symbols(parent_symbol, limit)

            return all_results

        except Exception as e:
            logger.error(f"Error in batch nested symbols search: {str(e)}")
            return [[] for _ in parent_symbols]  # Return empty results for all parent symbols

    def search_nested_by_type(self, parent_symbol: str, symbol_type: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for nested symbols of a specific type within a parent symbol.

        Args:
            parent_symbol: Parent symbol name
            symbol_type: Type of nested symbols to search for (Class, Interface, etc.)
            limit: Maximum number of results

        Returns:
            List of nested symbols of the specified type
        """
        limit = limit or config.DEFAULT_LIMIT

        try:
            # Create a dummy vector for search (required by Qdrant)
            dummy_vector = [0.0] * self.vector_size

            # Search with parent and type filter
            try:
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=dummy_vector,
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="parent_symbol",
                                match=models.MatchValue(value=parent_symbol)
                            ),
                            models.FieldCondition(
                                key="symbol_type",
                                match=models.MatchValue(value=symbol_type)
                            )
                        ]
                    ),
                    limit=limit
                )
            except Exception as e:
                logger.warning(f"Nested type filter search failed: {str(e)}")
                # Fall back to basic nested search and filter by type
                nested_symbols = self.search_nested_symbols(parent_symbol, limit=limit*2)
                filtered_symbols = [s for s in nested_symbols if s.get('symbol_type') == symbol_type]
                return filtered_symbols[:limit]

            # Format results
            nested_symbols = []
            for result in results:
                symbol = result.payload
                symbol['score'] = result.score
                nested_symbols.append(symbol)

            return nested_symbols
        except Exception as e:
            logger.error(f"Error searching for nested {symbol_type} symbols in {parent_symbol}: {str(e)}")
            return []

    def _process_single_nested_by_type(self, parent_symbol: str, symbol_type: str, limit: int) -> List[Dict[str, Any]]:
        """Process a single nested symbols by type search.

        This is a helper method for parallel processing.

        Args:
            parent_symbol: Parent symbol name
            symbol_type: Type of nested symbols to search for
            limit: Maximum number of results

        Returns:
            List of nested symbols of the specified type
        """
        try:
            # Create a dummy vector for search (required by Qdrant)
            dummy_vector = [0.0] * self.vector_size

            # Search with parent and type filter
            results = qdrant_compatibility.search(
                client=self.client,
                collection_name=self.collection_name,
                query_vector=dummy_vector,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="parent_symbol",
                            match=models.MatchValue(value=parent_symbol)
                        ),
                        models.FieldCondition(
                            key="symbol_type",
                            match=models.MatchValue(value=symbol_type)
                        )
                    ]
                ),
                limit=limit
            )

            # Format results
            nested_symbols = []
            for result in results:
                symbol = result.payload
                symbol['score'] = result.score
                nested_symbols.append(symbol)

            return nested_symbols

        except Exception as e:
            logger.warning(f"Nested type filter search failed for '{parent_symbol}': {str(e)}")
            # Fall back to basic nested search and filter by type
            try:
                nested_symbols = self.search_nested_symbols(parent_symbol, limit=limit*2)
                filtered_symbols = [s for s in nested_symbols if s.get('symbol_type') == symbol_type]
                return filtered_symbols[:limit]
            except Exception as fallback_error:
                logger.error(f"Fallback also failed for '{parent_symbol}': {str(fallback_error)}")
                return []  # Empty results for this parent symbol

    def search_nested_by_type_batch(self, parent_symbols: List[str], symbol_type: str, limit: int = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Search for nested symbols of a specific type within multiple parent symbols in batch.

        This is more efficient than calling search_nested_by_type multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            parent_symbols: List of parent symbol names
            symbol_type: Type of nested symbols to search for (Class, Interface, etc.)
            limit: Maximum number of results per parent symbol
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of nested symbols of the specified type, one list per parent symbol
        """
        if not parent_symbols:
            return []

        # If only one parent symbol, use single parent method
        if len(parent_symbols) == 1:
            return [self.search_nested_by_type(parent_symbols[0], symbol_type, limit)]

        limit = limit or config.DEFAULT_LIMIT

        # Determine whether to use parallel processing
        use_parallel = parallel and len(parent_symbols) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        try:
            # Initialize results list with empty lists
            all_results = [[] for _ in range(len(parent_symbols))]

            if use_parallel:
                logger.info(f"Processing {len(parent_symbols)} nested symbols by type searches in parallel with {min(config.MAX_WORKERS, len(parent_symbols))} workers")

                # Define a worker function for parallel processing
                def worker(idx, parent_symbol):
                    logger.debug(f"Worker processing nested symbols by type search {idx+1}/{len(parent_symbols)}: '{parent_symbol}' for type '{symbol_type}'")
                    return idx, self._process_single_nested_by_type(parent_symbol, symbol_type, limit)

                # Process parent symbols in parallel using a thread pool
                with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(parent_symbols))) as executor:
                    # Submit all tasks
                    future_to_idx = {
                        executor.submit(worker, i, parent_symbol): i
                        for i, parent_symbol in enumerate(parent_symbols)
                    }

                    # Process results as they complete
                    for future in as_completed(future_to_idx, timeout=config.THREAD_TIMEOUT):
                        try:
                            idx, result = future.result()
                            all_results[idx] = result
                        except Exception as e:
                            idx = future_to_idx[future]
                            logger.error(f"Worker for parent symbol '{parent_symbols[idx]}' failed: {str(e)}")
                            all_results[idx] = []  # Empty results for this parent symbol
            else:
                # Process parent symbols sequentially
                logger.info(f"Processing {len(parent_symbols)} nested symbols by type searches sequentially")
                for i, parent_symbol in enumerate(parent_symbols):
                    logger.debug(f"Processing batch nested symbols by type search {i+1}/{len(parent_symbols)}: '{parent_symbol}' for type '{symbol_type}'")
                    all_results[i] = self._process_single_nested_by_type(parent_symbol, symbol_type, limit)

            return all_results

        except Exception as e:
            logger.error(f"Error in batch nested symbols by type search for type '{symbol_type}': {str(e)}")
            return [[] for _ in parent_symbols]  # Return empty results for all parent symbols

    def search_component(self, component_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for a specific component by name.

        Args:
            component_name: Name of the component to search for
            limit: Maximum number of results

        Returns:
            List of component search results
        """
        limit = limit or config.DEFAULT_LIMIT

        try:
            # Generate query embedding
            query_vector = self._get_embedding(component_name)

            # Search for components
            try:
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="symbol_type",
                                match=models.MatchValue(value="component")
                            )
                        ],
                        should=[
                            models.FieldCondition(
                                key="symbol_name",
                                match=models.MatchText(text=component_name)
                            )
                        ]
                    ),
                    limit=limit
                )
            except Exception as e:
                logger.warning(f"Component search with filters failed: {str(e)}")
                # Fall back to general search and filter by component type
                general_results = self.suggest_imports(component_name, limit=limit*2)
                components = [r for r in general_results if r.get('symbol_type') == "component"]
                return components[:limit]

            # Format results
            components = []
            for result in results:
                component = result.payload
                component['score'] = result.score
                components.append(component)

            return components
        except Exception as e:
            logger.error(f"Error searching for component {component_name}: {str(e)}")
            return []

    def _process_single_component(self, component_name: str, query_vector: List[float], limit: int) -> List[Dict[str, Any]]:
        """Process a single component search with its vector.

        This is a helper method for parallel processing.

        Args:
            component_name: Name of the component to search for
            query_vector: Query embedding vector
            limit: Maximum number of results

        Returns:
            List of component search results
        """
        try:
            # Search for components
            results = qdrant_compatibility.search(
                client=self.client,
                collection_name=self.collection_name,
                query_vector=query_vector,
                query_filter=models.Filter(
                    must=[
                        models.FieldCondition(
                            key="symbol_type",
                            match=models.MatchValue(value="component")
                        )
                    ],
                    should=[
                        models.FieldCondition(
                            key="symbol_name",
                            match=models.MatchText(text=component_name)
                        )
                    ]
                ),
                limit=limit
            )

            # Format results
            components = []
            for result in results:
                component = result.payload
                component['score'] = result.score
                components.append(component)

            return components

        except Exception as e:
            logger.warning(f"Component search with filters failed for '{component_name}': {str(e)}")
            # Fall back to general search and filter by component type
            try:
                general_results = self.suggest_imports(component_name, limit=limit*2)
                components = [r for r in general_results if r.get('symbol_type') == "component"]
                return components[:limit]
            except Exception as fallback_error:
                logger.error(f"Fallback also failed for '{component_name}': {str(fallback_error)}")
                return []  # Empty results for this component

    def search_components_batch(self, component_names: List[str], limit: int = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Search for multiple components by name in batch.

        This is more efficient than calling search_component multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            component_names: List of component names to search for
            limit: Maximum number of results per component
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of component search results, one list per component name
        """
        if not component_names:
            return []

        # If only one component, use single component method
        if len(component_names) == 1:
            return [self.search_component(component_names[0], limit)]

        limit = limit or config.DEFAULT_LIMIT

        # Determine whether to use parallel processing
        use_parallel = parallel and len(component_names) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        try:
            # Generate query embeddings in batch
            query_vectors = self._get_embeddings_batch(component_names)

            # Initialize results list with empty lists
            all_results = [[] for _ in range(len(component_names))]

            if use_parallel:
                logger.info(f"Processing {len(component_names)} component searches in parallel with {min(config.MAX_WORKERS, len(component_names))} workers")

                # Define a worker function for parallel processing
                def worker(idx, component_name, query_vector):
                    logger.debug(f"Worker processing component search {idx+1}/{len(component_names)}: '{component_name}'")
                    return idx, self._process_single_component(component_name, query_vector, limit)

                # Process components in parallel using a thread pool
                with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(component_names))) as executor:
                    # Submit all tasks
                    future_to_idx = {
                        executor.submit(worker, i, component_name, query_vector): i
                        for i, (component_name, query_vector) in enumerate(zip(component_names, query_vectors))
                    }

                    # Process results as they complete
                    for future in as_completed(future_to_idx, timeout=config.THREAD_TIMEOUT):
                        try:
                            idx, result = future.result()
                            all_results[idx] = result
                        except Exception as e:
                            idx = future_to_idx[future]
                            logger.error(f"Worker for component '{component_names[idx]}' failed: {str(e)}")
                            all_results[idx] = []  # Empty results for this component
            else:
                # Process components sequentially
                logger.info(f"Processing {len(component_names)} component searches sequentially")
                for i, (component_name, query_vector) in enumerate(zip(component_names, query_vectors)):
                    logger.debug(f"Processing batch component search {i+1}/{len(component_names)}: '{component_name}'")
                    all_results[i] = self._process_single_component(component_name, query_vector, limit)

            return all_results

        except Exception as e:
            logger.error(f"Error in batch component search: {str(e)}")
            return [[] for _ in component_names]  # Return empty results for all components

    def search_import_path(self, symbol_name: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for import paths for a specific symbol.

        Args:
            symbol_name: Name of the symbol to find import paths for
            limit: Maximum number of results

        Returns:
            List of import path search results
        """
        limit = limit or config.DEFAULT_LIMIT

        try:
            # Generate query embedding
            query_vector = self._get_embedding(symbol_name)

            # Search for import paths
            try:
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        should=[
                            models.FieldCondition(
                                key="symbol_name",
                                match=models.MatchText(text=symbol_name)
                            )
                        ]
                    ),
                    limit=limit
                )
            except Exception as e:
                logger.warning(f"Import path search with filters failed: {str(e)}")
                # Fall back to general search
                return self.suggest_imports(symbol_name, limit=limit)

            # Format results focusing on import statements
            import_paths = []
            for result in results:
                if 'import_statement' in result.payload:
                    import_path = result.payload
                    import_path['score'] = result.score
                    import_paths.append(import_path)

            return import_paths
        except Exception as e:
            logger.error(f"Error searching for import path of {symbol_name}: {str(e)}")
            return []

    def _process_single_import_path(self, symbol_name: str, query_vector: List[float], limit: int) -> List[Dict[str, Any]]:
        """Process a single import path search with its vector.

        This is a helper method for parallel processing.

        Args:
            symbol_name: Name of the symbol to find import paths for
            query_vector: Query embedding vector
            limit: Maximum number of results

        Returns:
            List of import path search results
        """
        try:
            # Search for import paths
            results = qdrant_compatibility.search(
                client=self.client,
                collection_name=self.collection_name,
                query_vector=query_vector,
                query_filter=models.Filter(
                    should=[
                        models.FieldCondition(
                            key="symbol_name",
                            match=models.MatchText(text=symbol_name)
                        )
                    ]
                ),
                limit=limit
            )

            # Format results focusing on import statements
            import_paths = []
            for result in results:
                if 'import_statement' in result.payload:
                    import_path = result.payload
                    import_path['score'] = result.score
                    import_paths.append(import_path)

            return import_paths

        except Exception as e:
            logger.warning(f"Import path search with filters failed for '{symbol_name}': {str(e)}")
            # Fall back to general search
            try:
                general_results = self.suggest_imports(symbol_name, limit=limit)
                import_paths = [r for r in general_results if 'import_statement' in r]
                return import_paths
            except Exception as fallback_error:
                logger.error(f"Fallback also failed for '{symbol_name}': {str(fallback_error)}")
                return []  # Empty results for this symbol

    def search_import_paths_batch(self, symbol_names: List[str], limit: int = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Search for import paths for multiple symbols in batch.

        This is more efficient than calling search_import_path multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            symbol_names: List of symbol names to find import paths for
            limit: Maximum number of results per symbol
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of import path search results, one list per symbol name
        """
        if not symbol_names:
            return []

        # If only one symbol, use single symbol method
        if len(symbol_names) == 1:
            return [self.search_import_path(symbol_names[0], limit)]

        limit = limit or config.DEFAULT_LIMIT

        # Determine whether to use parallel processing
        use_parallel = parallel and len(symbol_names) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        try:
            # Generate query embeddings in batch
            query_vectors = self._get_embeddings_batch(symbol_names)

            # Initialize results list with empty lists
            all_results = [[] for _ in range(len(symbol_names))]

            if use_parallel:
                logger.info(f"Processing {len(symbol_names)} import path searches in parallel with {min(config.MAX_WORKERS, len(symbol_names))} workers")

                # Define a worker function for parallel processing
                def worker(idx, symbol_name, query_vector):
                    logger.debug(f"Worker processing import path search {idx+1}/{len(symbol_names)}: '{symbol_name}'")
                    return idx, self._process_single_import_path(symbol_name, query_vector, limit)

                # Process symbols in parallel using a thread pool
                with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(symbol_names))) as executor:
                    # Submit all tasks
                    future_to_idx = {
                        executor.submit(worker, i, symbol_name, query_vector): i
                        for i, (symbol_name, query_vector) in enumerate(zip(symbol_names, query_vectors))
                    }

                    # Process results as they complete
                    for future in as_completed(future_to_idx, timeout=config.THREAD_TIMEOUT):
                        try:
                            idx, result = future.result()
                            all_results[idx] = result
                        except Exception as e:
                            idx = future_to_idx[future]
                            logger.error(f"Worker for symbol '{symbol_names[idx]}' failed: {str(e)}")
                            all_results[idx] = []  # Empty results for this symbol
            else:
                # Process symbols sequentially
                logger.info(f"Processing {len(symbol_names)} import path searches sequentially")
                for i, (symbol_name, query_vector) in enumerate(zip(symbol_names, query_vectors)):
                    logger.debug(f"Processing batch import path search {i+1}/{len(symbol_names)}: '{symbol_name}'")
                    all_results[i] = self._process_single_import_path(symbol_name, query_vector, limit)

            return all_results

        except Exception as e:
            logger.error(f"Error in batch import path search: {str(e)}")
            return [[] for _ in symbol_names]  # Return empty results for all symbols

    def search_by_symbol_type(self, query: str, symbol_type: str, limit: int = None, use_hybrid: bool = None) -> List[Dict[str, Any]]:
        """Search for symbols of a specific type with enhanced filtering.

        Args:
            query: Query string
            symbol_type: Symbol type to search for (Class, Interface, Enum, Function, etc.)
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search

        Returns:
            List of symbols matching the type and query
        """
        limit = limit or config.DEFAULT_LIMIT
        use_hybrid = use_hybrid if use_hybrid is not None else config.DEFAULT_USE_HYBRID

        try:
            # Generate query embedding
            query_vector = self._get_embedding(query)

            # Define filter based on symbol type
            type_filter = models.FieldCondition(
                key="symbol_type",
                match=models.MatchValue(value=symbol_type)
            )

            try:
                if use_hybrid:
                    # Hybrid search with type filter
                    results = qdrant_compatibility.search(
                        client=self.client,
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        query_filter=models.Filter(
                            must=[type_filter],
                            should=[
                                models.FieldCondition(
                                    key="symbol_name",
                                    match=models.MatchText(text=query)
                                ),
                                models.FieldCondition(
                                    key="description",
                                    match=models.MatchText(text=query)
                                )
                            ]
                        ),
                        limit=limit
                    )
                else:
                    # Vector search with type filter
                    results = qdrant_compatibility.search(
                        client=self.client,
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        query_filter=models.Filter(
                            must=[type_filter]
                        ),
                        limit=limit
                    )
            except Exception as e:
                logger.warning(f"Symbol type search with filters failed: {str(e)}")
                # Fall back to general search and filter by type
                general_results = self.suggest_imports(query, limit=limit*2, use_hybrid=use_hybrid)
                filtered_results = [r for r in general_results if r.get('symbol_type') == symbol_type]
                return filtered_results[:limit]

            # Format results
            symbols = []
            for result in results:
                symbol = result.payload
                symbol['score'] = result.score
                symbols.append(symbol)

            return symbols
        except Exception as e:
            logger.error(f"Error searching for {symbol_type} symbols with query '{query}': {str(e)}")
            return []

    def _process_single_symbol_type(self, query: str, query_vector: List[float], symbol_type: str, limit: int, use_hybrid: bool) -> List[Dict[str, Any]]:
        """Process a single symbol type search with its vector.

        This is a helper method for parallel processing.

        Args:
            query: Query string
            query_vector: Query embedding vector
            symbol_type: Symbol type to search for
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search

        Returns:
            List of symbols matching the type and query
        """
        try:
            # Define filter based on symbol type
            type_filter = models.FieldCondition(
                key="symbol_type",
                match=models.MatchValue(value=symbol_type)
            )

            if use_hybrid:
                # Hybrid search with type filter
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        must=[type_filter],
                        should=[
                            models.FieldCondition(
                                key="symbol_name",
                                match=models.MatchText(text=query)
                            ),
                            models.FieldCondition(
                                key="description",
                                match=models.MatchText(text=query)
                            )
                        ]
                    ),
                    limit=limit
                )
            else:
                # Vector search with type filter
                results = qdrant_compatibility.search(
                    client=self.client,
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        must=[type_filter]
                    ),
                    limit=limit
                )

            # Format results
            symbols = []
            for result in results:
                symbol = result.payload
                symbol['score'] = result.score
                symbols.append(symbol)

            return symbols

        except Exception as e:
            logger.warning(f"Symbol type search with filters failed for '{query}': {str(e)}")
            # Fall back to general search and filter by type
            try:
                general_results = self.suggest_imports(query, limit=limit*2, use_hybrid=use_hybrid)
                filtered_results = [r for r in general_results if r.get('symbol_type') == symbol_type]
                return filtered_results[:limit]
            except Exception as fallback_error:
                logger.error(f"Fallback also failed for '{query}': {str(fallback_error)}")
                return []  # Empty results for this query

    def search_by_symbol_type_batch(self, queries: List[str], symbol_type: str, limit: int = None, use_hybrid: bool = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Search for symbols of a specific type for multiple queries in batch.

        This is more efficient than calling search_by_symbol_type multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            queries: List of query strings
            symbol_type: Symbol type to search for (Class, Interface, Enum, Function, etc.)
            limit: Maximum number of results per query
            use_hybrid: Whether to use hybrid search
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of symbols matching the type and query, one list per query
        """
        if not queries:
            return []

        # If only one query, use single query method
        if len(queries) == 1:
            return [self.search_by_symbol_type(queries[0], symbol_type, limit, use_hybrid)]

        limit = limit or config.DEFAULT_LIMIT
        use_hybrid = use_hybrid if use_hybrid is not None else config.DEFAULT_USE_HYBRID

        # Determine whether to use parallel processing
        use_parallel = parallel and len(queries) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        try:
            # Generate query embeddings in batch
            query_vectors = self._get_embeddings_batch(queries)

            # Initialize results list with empty lists
            all_results = [[] for _ in range(len(queries))]

            if use_parallel:
                logger.info(f"Processing {len(queries)} symbol type searches in parallel with {min(config.MAX_WORKERS, len(queries))} workers")

                # Define a worker function for parallel processing
                def worker(idx, query, query_vector):
                    logger.debug(f"Worker processing symbol type search {idx+1}/{len(queries)}: '{query}' for type '{symbol_type}'")
                    return idx, self._process_single_symbol_type(query, query_vector, symbol_type, limit, use_hybrid)

                # Process queries in parallel using a thread pool
                with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(queries))) as executor:
                    # Submit all tasks
                    future_to_idx = {
                        executor.submit(worker, i, query, query_vector): i
                        for i, (query, query_vector) in enumerate(zip(queries, query_vectors))
                    }

                    # Process results as they complete
                    for future in as_completed(future_to_idx, timeout=config.THREAD_TIMEOUT):
                        try:
                            idx, result = future.result()
                            all_results[idx] = result
                        except Exception as e:
                            idx = future_to_idx[future]
                            logger.error(f"Worker for query '{queries[idx]}' failed: {str(e)}")
                            all_results[idx] = []  # Empty results for this query
            else:
                # Process queries sequentially
                logger.info(f"Processing {len(queries)} symbol type searches sequentially")
                for i, (query, query_vector) in enumerate(zip(queries, query_vectors)):
                    logger.debug(f"Processing batch symbol type search {i+1}/{len(queries)}: '{query}' for type '{symbol_type}'")
                    all_results[i] = self._process_single_symbol_type(query, query_vector, symbol_type, limit, use_hybrid)

            return all_results

        except Exception as e:
            logger.error(f"Error in batch symbol type search for type '{symbol_type}': {str(e)}")
            return [[] for _ in queries]  # Return empty results for all queries

    def handle_agent_query(self, agent_query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Handle queries in the format used by Agno agents.

        Args:
            agent_query: Query string in the format "ArkTS search: 'query'"
            limit: Maximum number of results

        Returns:
            List of search results formatted for the agent
        """
        limit = limit or config.DEFAULT_LIMIT

        # Parse query
        if not agent_query.startswith("ArkTS search:"):
            logger.error(f"Invalid agent query format: {agent_query}")
            return []

        # Extract actual query
        search_query = agent_query.split("'")[1] if "'" in agent_query else agent_query.replace("ArkTS search:", "").strip()
        logger.info(f"Parsed agent query: '{search_query}'")

        # Determine query type
        if "component" in search_query.lower():
            # Extract component name
            component_name = search_query.split("component")[0].strip()
            logger.info(f"Detected component search for: '{component_name}'")
            return self.search_component(component_name, limit=limit)

        elif "import path" in search_query.lower():
            # Extract symbol name
            symbol_name = search_query.split("import path")[0].strip()
            logger.info(f"Detected import path search for: '{symbol_name}'")
            return self.search_import_path(symbol_name, limit=limit)

        elif any(type_keyword in search_query.lower() for type_keyword in ["class", "interface", "enum", "function", "type"]):
            # Extract symbol type and query
            for type_keyword in ["class", "interface", "enum", "function", "type"]:
                if type_keyword in search_query.lower():
                    symbol_type = type_keyword.capitalize()
                    query = search_query.split(type_keyword)[0].strip()
                    logger.info(f"Detected {symbol_type} search for: '{query}'")
                    return self.search_by_symbol_type(query, symbol_type, limit=limit)

        # If no specific type detected, perform general search
        logger.info(f"Performing general search for: '{search_query}'")
        return self.suggest_imports(search_query, limit=limit, use_hybrid=True)

    def handle_agent_queries_batch(self, agent_queries: List[str], limit: int = None, parallel: bool = True) -> List[List[Dict[str, Any]]]:
        """Handle multiple agent queries in batch.

        This is more efficient than calling handle_agent_query multiple times.
        Uses parallel processing for better performance when batch size is large enough.

        Args:
            agent_queries: List of query strings in the format "ArkTS search: 'query'"
            limit: Maximum number of results per query
            parallel: Whether to use parallel processing (default: True)

        Returns:
            List of lists of search results, one list per query
        """
        if not agent_queries:
            return []

        # If only one query, use single query method
        if len(agent_queries) == 1:
            return [self.handle_agent_query(agent_queries[0], limit)]

        limit = limit or config.DEFAULT_LIMIT

        # Parse queries and group by type
        general_queries = []
        general_indices = []
        component_queries = []
        component_indices = []
        import_path_queries = []
        import_path_indices = []
        type_queries = {}
        type_indices = {}

        for i, agent_query in enumerate(agent_queries):
            # Skip invalid queries
            if not agent_query.startswith("ArkTS search:"):
                logger.error(f"Invalid agent query format: {agent_query}")
                continue

            # Extract actual query
            search_query = agent_query.split("'")[1] if "'" in agent_query else agent_query.replace("ArkTS search:", "").strip()

            # Categorize query
            if "component" in search_query.lower():
                component_name = search_query.split("component")[0].strip()
                component_queries.append(component_name)
                component_indices.append(i)

            elif "import path" in search_query.lower():
                symbol_name = search_query.split("import path")[0].strip()
                import_path_queries.append(symbol_name)
                import_path_indices.append(i)

            elif any(type_keyword in search_query.lower() for type_keyword in ["class", "interface", "enum", "function", "type"]):
                for type_keyword in ["class", "interface", "enum", "function", "type"]:
                    if type_keyword in search_query.lower():
                        symbol_type = type_keyword.capitalize()
                        query = search_query.split(type_keyword)[0].strip()

                        if symbol_type not in type_queries:
                            type_queries[symbol_type] = []
                            type_indices[symbol_type] = []

                        type_queries[symbol_type].append(query)
                        type_indices[symbol_type].append(i)
                        break
            else:
                # General search
                general_queries.append(search_query)
                general_indices.append(i)

        # Initialize results
        all_results = [[] for _ in range(len(agent_queries))]

        # Determine whether to use parallel processing for batch operations
        use_parallel = parallel and len(agent_queries) >= config.MIN_BATCH_SIZE_FOR_PARALLEL

        # Process all query types in parallel if there are enough queries
        if use_parallel and len(agent_queries) >= config.MIN_BATCH_SIZE_FOR_PARALLEL * 2:
            logger.info(f"Processing all query types in parallel with {min(config.MAX_WORKERS, 4)} workers")

            # Define worker functions for each query type
            def process_general_queries():
                if general_queries:
                    logger.info(f"Worker processing {len(general_queries)} general queries in batch")
                    batch_results = self.suggest_imports_batch(general_queries, limit=limit, use_hybrid=True, parallel=True)
                    return "general", general_indices, batch_results
                return "general", [], []

            def process_component_queries():
                if component_queries:
                    logger.info(f"Worker processing {len(component_queries)} component queries in batch")
                    batch_results = self.search_components_batch(component_queries, limit=limit, parallel=True)
                    return "component", component_indices, batch_results
                return "component", [], []

            def process_import_path_queries():
                if import_path_queries:
                    logger.info(f"Worker processing {len(import_path_queries)} import path queries in batch")
                    batch_results = self.search_import_paths_batch(import_path_queries, limit=limit, parallel=True)
                    return "import_path", import_path_indices, batch_results
                return "import_path", [], []

            def process_type_queries(symbol_type):
                queries = type_queries.get(symbol_type, [])
                indices = type_indices.get(symbol_type, [])
                if queries:
                    logger.info(f"Worker processing {len(queries)} {symbol_type} queries in batch")
                    batch_results = self.search_by_symbol_type_batch(queries, symbol_type, limit=limit, parallel=True)
                    return symbol_type, indices, batch_results
                return symbol_type, [], []

            # Create a list of tasks to execute
            tasks = [
                process_general_queries,
                process_component_queries,
                process_import_path_queries
            ]

            # Add tasks for each symbol type
            for symbol_type in type_queries.keys():
                tasks.append(lambda st=symbol_type: process_type_queries(st))

            # Execute tasks in parallel
            with ThreadPoolExecutor(max_workers=min(config.MAX_WORKERS, len(tasks))) as executor:
                # Submit all tasks
                futures = [executor.submit(task) for task in tasks]

                # Process results as they complete
                for future in as_completed(futures, timeout=config.THREAD_TIMEOUT):
                    try:
                        query_type, indices, results = future.result()
                        logger.info(f"Completed processing {query_type} queries")

                        # Update results
                        for idx, result in zip(indices, results):
                            all_results[idx] = result
                    except Exception as e:
                        logger.error(f"Worker failed: {str(e)}")
        else:
            # Process each query type sequentially but use parallel processing within each batch

            # Process general queries in batch
            if general_queries:
                logger.info(f"Processing {len(general_queries)} general queries in batch")
                batch_results = self.suggest_imports_batch(general_queries, limit=limit, use_hybrid=True, parallel=use_parallel)
                for idx, results in zip(general_indices, batch_results):
                    all_results[idx] = results

            # Process component queries in batch
            if component_queries:
                logger.info(f"Processing {len(component_queries)} component queries in batch")
                batch_results = self.search_components_batch(component_queries, limit=limit, parallel=use_parallel)
                for idx, results in zip(component_indices, batch_results):
                    all_results[idx] = results

            # Process import path queries in batch
            if import_path_queries:
                logger.info(f"Processing {len(import_path_queries)} import path queries in batch")
                batch_results = self.search_import_paths_batch(import_path_queries, limit=limit, parallel=use_parallel)
                for idx, results in zip(import_path_indices, batch_results):
                    all_results[idx] = results

            # Process type queries by type in batch
            for symbol_type, queries in type_queries.items():
                logger.info(f"Processing {len(queries)} {symbol_type} queries in batch")
                batch_results = self.search_by_symbol_type_batch(queries, symbol_type, limit=limit, parallel=use_parallel)
                for idx, results in zip(type_indices[symbol_type], batch_results):
                    all_results[idx] = results

        return all_results


def main():
    """Main function for querying."""
    parser = argparse.ArgumentParser(description='ArkTS Query')
    parser.add_argument('--qdrant-url', type=str, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, help='Ollama embedding model to use')
    parser.add_argument('--query', type=str, help='Query for import suggestions')
    parser.add_argument('--queries', type=str, nargs='+', help='Multiple queries for batch processing')
    parser.add_argument('--limit', type=int, help='Maximum number of results')
    parser.add_argument('--hybrid', action='store_true', help='Use hybrid search')

    # Add arguments for new search types
    parser.add_argument('--type', type=str, help='Filter by symbol type (Class, Interface, Enum, Function, etc.)')
    parser.add_argument('--types', type=str, nargs='+', help='Multiple queries for batch processing by symbol type')
    parser.add_argument('--nested', type=str, help='Search for nested symbols within a parent')
    parser.add_argument('--nesteds', type=str, nargs='+', help='Multiple parent symbols for batch nested symbols search')
    parser.add_argument('--nested-type', type=str, help='Type of nested symbols to search for')
    parser.add_argument('--component', type=str, help='Search for a specific component')
    parser.add_argument('--components', type=str, nargs='+', help='Multiple components for batch component search')
    parser.add_argument('--import-path', type=str, help='Search for import paths for a symbol')
    parser.add_argument('--import-paths', type=str, nargs='+', help='Multiple symbols for batch import path search')
    parser.add_argument('--agent-query', type=str, help='Process an Agno agent query format')
    parser.add_argument('--agent-queries', type=str, nargs='+', help='Multiple agent queries for batch processing')
    parser.add_argument('--batch', action='store_true', help='Use batch processing for better performance')
    parser.add_argument('--parallel', action='store_true', help='Use parallel processing for better performance')
    parser.add_argument('--no-parallel', action='store_false', dest='parallel', help='Disable parallel processing')
    parser.set_defaults(parallel=True)

    args = parser.parse_args()

    # Initialize query
    query = ArkTSQuery(
        qdrant_url=args.qdrant_url,
        collection_name=args.collection,
        ollama_url=args.ollama_url,
        embedding_model=args.embedding_model
    )

    # Process query based on arguments
    if args.agent_queries:
        # Handle multiple agent queries in batch
        batch_results = query.handle_agent_queries_batch(args.agent_queries, limit=args.limit, parallel=args.parallel)
        print(f"Processed {len(args.agent_queries)} agent queries in batch")

        # Print results for each query
        for i, (agent_query, results) in enumerate(zip(args.agent_queries, batch_results)):
            print(f"\nResults for agent query {i+1}: '{agent_query}'")
            print(f"Found {len(results)} results")

            # Print results for this query
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.agent_query:
        # Handle agent query format
        results = query.handle_agent_query(args.agent_query, limit=args.limit)
        print(f"Found {len(results)} results for agent query: '{args.agent_query}'")

    elif args.queries and args.batch:
        # Process multiple queries in batch
        batch_results = query.suggest_imports_batch(args.queries, limit=args.limit, use_hybrid=args.hybrid, parallel=args.parallel)
        print(f"Processed {len(args.queries)} queries in batch")

        # Print results for each query
        for i, (query_str, results) in enumerate(zip(args.queries, batch_results)):
            print(f"\nResults for query {i+1}: '{query_str}'")
            print(f"Found {len(results)} results")

            # Print results for this query
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.components and args.batch:
        # Process multiple components in batch
        batch_results = query.search_components_batch(args.components, limit=args.limit, parallel=args.parallel)
        print(f"Processed {len(args.components)} components in batch")

        # Print results for each component
        for i, (component_name, results) in enumerate(zip(args.components, batch_results)):
            print(f"\nResults for component {i+1}: '{component_name}'")
            print(f"Found {len(results)} results")

            # Print results for this component
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.component:
        # Search for component
        results = query.search_component(args.component, limit=args.limit)
        print(f"Found {len(results)} components matching '{args.component}':")

    elif args.import_paths and args.batch:
        # Process multiple import paths in batch
        batch_results = query.search_import_paths_batch(args.import_paths, limit=args.limit, parallel=args.parallel)
        print(f"Processed {len(args.import_paths)} import paths in batch")

        # Print results for each import path
        for i, (symbol_name, results) in enumerate(zip(args.import_paths, batch_results)):
            print(f"\nResults for import path {i+1}: '{symbol_name}'")
            print(f"Found {len(results)} results")

            # Print results for this import path
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.import_path:
        # Search for import paths
        results = query.search_import_path(args.import_path, limit=args.limit)
        print(f"Found {len(results)} import paths for '{args.import_path}':")

    elif args.nesteds and args.nested_type and args.batch:
        # Process multiple nested symbols by type in batch
        batch_results = query.search_nested_by_type_batch(args.nesteds, args.nested_type, limit=args.limit, parallel=args.parallel)
        print(f"Processed {len(args.nesteds)} nested symbols by type in batch")

        # Print results for each nested symbol
        for i, (parent_symbol, results) in enumerate(zip(args.nesteds, batch_results)):
            print(f"\nResults for nested {args.nested_type} symbols in '{parent_symbol}'")
            print(f"Found {len(results)} results")

            # Print results for this nested symbol
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.nested and args.nested_type:
        # Search for nested symbols of specific type
        results = query.search_nested_by_type(args.nested, args.nested_type, limit=args.limit)
        print(f"Found {len(results)} nested {args.nested_type} symbols in '{args.nested}':")

    elif args.nesteds and args.batch:
        # Process multiple nested symbols in batch
        batch_results = query.search_nested_symbols_batch(args.nesteds, limit=args.limit, parallel=args.parallel)
        print(f"Processed {len(args.nesteds)} nested symbols in batch")

        # Print results for each nested symbol
        for i, (parent_symbol, results) in enumerate(zip(args.nesteds, batch_results)):
            print(f"\nResults for nested symbols in '{parent_symbol}'")
            print(f"Found {len(results)} results")

            # Print results for this nested symbol
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.nested:
        # Search for all nested symbols
        results = query.search_nested_symbols(args.nested, limit=args.limit)
        print(f"Found {len(results)} nested symbols in '{args.nested}':")

    elif args.types and args.type and args.batch:
        # Process multiple queries by symbol type in batch
        batch_results = query.search_by_symbol_type_batch(args.types, args.type, limit=args.limit, use_hybrid=args.hybrid, parallel=args.parallel)
        print(f"Processed {len(args.types)} queries for {args.type} symbols in batch")

        # Print results for each query
        for i, (query_str, results) in enumerate(zip(args.types, batch_results)):
            print(f"\nResults for {args.type} symbols matching '{query_str}'")
            print(f"Found {len(results)} results")

            # Print results for this query
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    elif args.type and args.query:
        # Search by symbol type
        results = query.search_by_symbol_type(args.query, args.type, limit=args.limit, use_hybrid=args.hybrid)
        print(f"Found {len(results)} {args.type} symbols matching '{args.query}':")

    elif args.query:
        # General import suggestions
        results = query.suggest_imports(args.query, limit=args.limit, use_hybrid=args.hybrid)
        print(f"Found {len(results)} suggestions for '{args.query}':")

    elif args.queries:
        # Process multiple queries sequentially
        print(f"Processing {len(args.queries)} queries sequentially")
        for i, query_str in enumerate(args.queries, 1):
            print(f"\nQuery {i}: '{query_str}'")
            results = query.suggest_imports(query_str, limit=args.limit, use_hybrid=args.hybrid)
            print(f"Found {len(results)} suggestions")

            # Print results for this query
            for j, result in enumerate(results, 1):
                print(f"\n  {j}. {result['symbol_name']} ({result['symbol_type']})")
                print(f"     Import: {result.get('import_statement', 'N/A')}")
                print(f"     Score: {result['score']:.4f}")

                if result.get('parent_symbol'):
                    print(f"     Nested in: {result['parent_symbol']}")

                if result.get('module_name'):
                    print(f"     Module: {result['module_name']}")

                if result.get('description'):
                    # Truncate long descriptions
                    desc = result['description']
                    if len(desc) > 100:
                        desc = desc[:97] + "..."
                    print(f"     Description: {desc}")

        return

    else:
        print("Please provide a query parameter. Use --help for available options.")
        return

    # Print results for single query
    for i, result in enumerate(results, 1):
        print(f"\n{i}. {result['symbol_name']} ({result['symbol_type']})")
        print(f"   Import: {result.get('import_statement', 'N/A')}")
        print(f"   Score: {result['score']:.4f}")

        if result.get('parent_symbol'):
            print(f"   Nested in: {result['parent_symbol']}")

        if result.get('module_name'):
            print(f"   Module: {result['module_name']}")

        if result.get('description'):
            # Truncate long descriptions
            desc = result['description']
            if len(desc) > 100:
                desc = desc[:97] + "..."
            print(f"   Description: {desc}")


if __name__ == "__main__":
    main()
