[{"symbol_name": "ButtonType", "symbol_type": "enum", "module_name": "button", "is_default": false, "is_ets": false, "description": "Provides a button component. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonType } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.6679334}, {"symbol_name": "ButtonType", "symbol_type": "enum", "module_name": "button_component", "is_default": false, "is_ets": true, "description": "Enum for button type. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @since 7", "import_statement": "import { ButtonType } from 'button_component';", "parent_symbol": null, "is_nested": false, "score": 0.643481}, {"symbol_name": "ButtonRole", "symbol_type": "enum", "module_name": "button_component", "is_default": false, "is_ets": true, "description": "Enum for button role. @enum { number } @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @since 7", "import_statement": "import { ButtonRole } from 'button_component';", "parent_symbol": null, "is_nested": false, "score": 0.63695717}]