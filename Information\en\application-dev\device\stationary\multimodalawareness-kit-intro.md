# Introduction to Multimodal Awareness Kit

Multimodal Awareness Kit allows an application to identify user activities (walking, running, driving, etc.) or postures based on the data collected by sensors including gyroscopes and acceleration sensors built in a device.

## How MultimodalAwareness Kit Works

MultimodalAwareness Kit is offered by the system as a basic service for applications. Depending on the service scenario, an application needs to initiate a subscription request to the system and cancel the subscription when the service scenario ends. In this process, the system reports the device status information to the application on a real-time basis.

## Constraints

To use MultimodalAwareness Kit, you need to apply for required permissions. The device must support the sensors required by the corresponding capabilities.
