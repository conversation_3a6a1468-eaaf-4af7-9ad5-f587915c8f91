# ArkData (ArkData Management)

- ArkTS APIs
  - [@ohos.data.commonType (Common Data Types)](js-apis-data-commonType.md)
  - [@ohos.data.dataAbility (DataAbility Predicates)](js-apis-data-ability.md)
  - [@ohos.data.dataSharePredicates (DataShare Predicates)](js-apis-data-dataSharePredicates.md)
  - [@ohos.data.distributedDataObject (Distributed Data Object)](js-apis-data-distributedobject.md)
  - [@ohos.data.distributedKVStore (Distributed KV Store)](js-apis-distributedKVStore.md)
  - [@ohos.data.preferences (User Preferences)](js-apis-data-preferences.md)
  - [@ohos.data.sendablePreferences (Shared User Preferences)](js-apis-data-sendablePreferences.md)
  - [@ohos.data.relationalStore (RDB Store)](js-apis-data-relationalStore.md)
  - [@ohos.data.sendableRelationalStore (Shared RDB Store)](js-apis-data-sendableRelationalStore.md)
  - [@ohos.data.unifiedDataChannel (Unified Data Channel)](js-apis-data-unifiedDataChannel.md)
  - [@ohos.data.unifiedDataChannel (Uniform Data Channel) (System API)](js-apis-data-unifiedDataChannel-sys.md)
  - [@ohos.data.uniformDataStruct (Uniform Data Structs)](js-apis-data-uniformDataStruct.md)
  - [@ohos.data.uniformTypeDescriptor (Uniform Data Definition and Description)](js-apis-data-uniformTypeDescriptor.md)
  - [@ohos.data.ValuesBucket (Value Bucket)](js-apis-data-valuesBucket.md)
  <!--Del-->
  - [@ohos.application.DataShareExtensionAbility (DataShare ExtensionAbility) (System API)](js-apis-application-dataShareExtensionAbility-sys.md)
  <!--DelEnd-->
  - [@ohos.data.cloudData (Device-Cloud Service)](js-apis-data-cloudData.md)
  <!--Del-->
  - [@ohos.data.cloudData (Device-Cloud Service) (System API)](js-apis-data-cloudData-sys.md)
  - [@ohos.data.cloudExtension (Device-Cloud Sharing Extension) (System API)](js-apis-data-cloudExtension-sys.md)
  - [@ohos.data.dataShare (DataShare) (System API)](js-apis-data-dataShare-sys.md)
  - [@ohos.data.dataSharePredicates (DataShare Predicates) (System API)](js-apis-data-dataSharePredicates-sys.md)
  - [@ohos.data.dataShareResultSet (DataShare Result Set) (System API)](js-apis-data-DataShareResultSet-sys.md)
  - [@ohos.data.distributedKVStore (Distributed KV Store) (System API)](js-apis-distributedKVStore-sys.md)
  - [@ohos.data.relationalStore (RDB Store) (System API)](js-apis-data-relationalStore-sys.md)
  <!--DelEnd-->
  - APIs No Longer Maintained
    - [@ohos.data.distributedData (Distributed Data Management)](js-apis-distributed-data.md)
    - [@ohos.data.rdb (RDB)](js-apis-data-rdb.md)
    - [@ohos.data.storage (Lightweight Data Storage)](js-apis-data-storage.md)
    - [@system.storage (Data Storage)](js-apis-system-storage.md)
    - data/rdb
      - [resultSet (Result Set)](js-apis-data-resultset.md) 
- C APIs
  - Modules
    - [Data](_data.md)
    - [RDB](_r_d_b.md)
    - [UDMF](_u_d_m_f.md)
    - [Preferences](_preferences.md)
  - Header Files
    - [data_asset.h](data__asset_8h.md)
    - [oh_cursor.h](oh__cursor_8h.md)
    - [oh_predicates.h](oh__predicates_8h.md)
    - [oh_value_object.h](oh__value__object_8h.md)
    - [oh_values_bucket.h](oh__values__bucket_8h.md)
    - [relational_store_error_code.h](relational__store__error__code_8h.md)
    - [relational_store.h](relational__store_8h.md)
    - [udmf.h](udmf_8h.md)
    - [udmf_err_code.h](udmf__err__code_8h.md)
    - [udmf_meta.h](udmf__meta_8h.md)
    - [uds.h](uds_8h.md)
    - [utd.h](utd_8h.md)
    - [oh_preferences.h](oh__preferences_8h.md)
    - [oh_preferences_option.h](oh__preferences__option_8h.md)
    - [oh_preferences_value.h](oh__preferences__value_8h.md)
  - Structs
    - [OH_Cursor](_o_h___cursor.md)
    - [OH_Predicates](_o_h___predicates.md)
    - [OH_Rdb_Config](_o_h___rdb___config.md)
    - [OH_Rdb_Store](_o_h___rdb___store.md)
    - [OH_VBucket](_o_h___v_bucket.md)
    - [OH_VObject](_o_h___v_object.md)
    - [Rdb_ChangeInfo](_rdb___change_info.md)
    - [Rdb_DataObserver](_rdb___data_observer.md)
    - [Rdb_DistributedConfig](_rdb___distributed_config.md)
    - [Rdb_KeyInfo](_rdb___key_info.md)
    - [Rdb_KeyInfo::Rdb_KeyData](union_rdb___key_info_1_1_rdb___key_data.md)
    - [Rdb_ProgressDetails](_rdb___progress_details.md)
    - [Rdb_ProgressObserver](_rdb___progress_observer.md)
    - [Rdb_Statistic](_rdb___statistic.md)
    - [Rdb_SubscribeCallback](union_rdb___subscribe_callback.md)
    - [Rdb_TableDetails](_rdb___table_details.md)
- Error Codes
  - [RDB Error Codes](errorcode-data-rdb.md)
  - [DataShare Error Codes](errorcode-datashare.md)
  - [Distributed Data Object Error Codes](errorcode-distributed-dataObject.md)
  - [Distributed KV Store Error Codes](errorcode-distributedKVStore.md)
  - [User Preferences Error Codes](errorcode-preferences.md)
  - [UDMF Error Codes](errorcode-udmf.md)
