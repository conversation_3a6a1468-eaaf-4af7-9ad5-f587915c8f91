"""
Configuration file for ArkTS Import Estimator.

This file contains all the configuration parameters for the ArkTS Import Estimator.
"""

# Qdrant configuration
QDRANT_URL = "http://gmktec.ai-institute.uk:6333"
COLLECTION_NAME = "arkts_imports"

# Ollama configuration
OLLAMA_URL = "http://lgpu2.ai-institute.uk:11434"
EMBEDDING_MODEL = "mxbai-embed-large"  # Changed from mxbai-embed-large for faster processing

# Agent configuration
AGENT_MODEL = "qwen3:30b"  # Qwen 3 30B model for agent
AGENT_MAX_TOKENS = 2048
AGENT_TEMPERATURE = 0.1
AGENT_NAME = "ArkTS Import Assistant"
AGENT_DESCRIPTION = "An AI assistant specialized in ArkTS import suggestions and component search"

# Vector size configuration
# mxbai-embed-large: 1024
# nomic-embed-text: 768
VECTOR_SIZES = {
    "mxbai-embed-large": 1024,
    "nomic-embed-text": 768,
    "all-minilm": 384
}

# Default vector size (used if model not in VECTOR_SIZES)
DEFAULT_VECTOR_SIZE = 1024

# Retry configuration for Ollama API
MAX_RETRIES = 3
RETRY_DELAY = 1  # seconds
REQUEST_TIMEOUT = 30  # seconds

# Performance optimization settings - MAXIMIZED FOR SPEED
MAX_EMBEDDING_BATCH_SIZE = 50  # Increased for faster batch processing
MAX_QDRANT_BATCH_SIZE = 500  # Increased for faster batch uploads
CONNECTION_POOL_SIZE = 20  # Increased for better connection reuse

# Thread pool configuration - MAXIMIZED FOR SPEED
MAX_WORKERS = 16  # Increased for maximum parallelism
MIN_BATCH_SIZE_FOR_PARALLEL = 2  # Reduced to enable parallel processing for smaller batches
THREAD_TIMEOUT = 120  # Increased to allow more time for larger batches

# Indexing optimization settings
INDEXING_MAX_WORKERS = 32  # Maximum workers for indexing operations
PARSING_MAX_WORKERS = 24  # Maximum workers for parsing operations
EMBEDDING_MAX_WORKERS = 16  # Maximum workers for embedding operations
UPLOAD_BATCH_SIZE = 500  # Batch size for uploading to Qdrant

# Search configuration
DEFAULT_LIMIT = 10
DEFAULT_USE_HYBRID = True

# Logging configuration
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
