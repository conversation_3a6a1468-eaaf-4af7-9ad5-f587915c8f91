{"main": {"id": "7e10d4fe8b1e2c2d", "type": "split", "children": [{"id": "b73772416a16215b", "type": "tabs", "children": [{"id": "641bc9080985ef80", "type": "leaf", "state": {"type": "markdown", "state": {"file": "reference/apis-ability-kit/js-apis-app-ability-uiExtensionContentSession-sys.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "js-apis-app-ability-uiExtensionContentSession-sys"}}]}], "direction": "vertical"}, "left": {"id": "dffed3b5e20008fb", "type": "split", "children": [{"id": "992ca9c7b84ad151", "type": "tabs", "children": [{"id": "428cce2c8e684b34", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "42e88b62b173293d", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "8df3c98fa2bca8ff", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "3970fbe923a06c98", "type": "split", "children": [{"id": "d584261c5a423354", "type": "tabs", "children": [{"id": "9edd432951f0fabc", "type": "leaf", "state": {"type": "backlink", "state": {"file": "reference/apis-ability-kit/js-apis-app-ability-uiExtensionContentSession-sys.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for js-apis-app-ability-uiExtensionContentSession-sys"}}, {"id": "e1ab3467642a6bd8", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "reference/apis-ability-kit/js-apis-app-ability-uiExtensionContentSession-sys.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from js-apis-app-ability-uiExtensionContentSession-sys"}}, {"id": "89ce37ad3b024597", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "6cebd1aaa6ce7f2e", "type": "leaf", "state": {"type": "outline", "state": {"file": "reference/apis-ability-kit/js-apis-app-ability-uiExtensionContentSession-sys.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of js-apis-app-ability-uiExtensionContentSession-sys"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "641bc9080985ef80", "lastOpenFiles": ["reference/apis-ability-kit/js-apis-app-ability-want.md", "reference/apis-ability-kit/js-apis-app-appstartup-startupConfigEntry.md", "reference/apis-ability-kit/context__constant_8h.md", "reference/apis-ability-kit/_ability_runtime.md", "reference/apis-ability-kit/_ability_access_control.md", "reference/apis-accessibility-kit/errorcode-accessibility.md", "reference/apis-ability-kit/application__context_8h.md", "reference/apis-ability-kit/ability__access__control_8h.md", "reference/apis-ability-kit/_o_h___native_bundle_application_info.md", "reference/apis-ability-kit/_bundle.md", "file-management/app-file-access.md", "file-management/file-access-across-devices.md", "file-management/dev-user-file-manager.md", "file-management/core-file-kit-intro.md", "file-management/app-sandbox-directory.md", "file-management/app-fs-space-statistics.md", "file-management/app-file-overview.md", "application-models/application-context-fa.md", "application-models/app-startup-overview.md", "application-models/app-startup.md", "accessibility/Readme-EN.md", "accessibility/accessibilityKit.md", "accessibility/accessibilityextensionability.md"]}