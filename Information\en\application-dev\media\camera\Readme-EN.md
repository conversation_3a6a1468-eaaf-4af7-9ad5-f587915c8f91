# Camera Kit

- [Introduction to Camera Kit](camera-overview.md)
- [Camera Development Preparations](camera-preparation.md)
- Camera Development (ArkTS)
  - [Camera Device Management (ArkTS)](camera-device-management.md)
  - [Device Input Management (ArkTS)](camera-device-input.md)
  - [Camera Session Management (ArkTS)](camera-session-management.md)
  - [Preview (ArkTS)](camera-preview.md)
  - [Photo Capture (ArkTS)](camera-shooting.md)
  - [Video Recording (ArkTS)](camera-recording.md)
  - [Camera Metadata (ArkTS)](camera-metadata.md)
  - [Using the Flashlight (ArkTS)](camera-torch-use.md)
  - [Adapting to Camera Changes in Different Folding States (ArkTS)](camera-foldable-display.md)
  <!--Del-->
  - [High-Performance Photo Capture (for System Applications Only) (ArkTS)](camera-deferred-photo.md)
  <!--DelEnd-->
  - [Deferred Photo Delivery (ArkTS)](camera-deferred-capture.md)
  - [Moving Photos (ArkTS)](camera-moving-photo.md)
  - [Basic Camera Animation (ArkTS)](camera-animation.md)
  <!--Del-->
  - [Depth Data (for System Applications Only) (ArkTS)](camera-depth-data.md)
  <!--DelEnd-->
- Camera Best Practices (ArkTS)
  - [Using the Camera Picker (ArkTS)](camera-picker.md)
  - [Photo Capture Sample (ArkTS)](camera-shooting-case.md)
  - [Video Recording Sample (ArkTS)](camera-recording-case.md)
  - [Dual-Channel Preview (ArkTS)](camera-dual-channel-preview.md)
  - [Deferred Photo Delivery Sample (ArkTS)](camera-deferred-capture-case.md)
  <!--Del-->
  - [Using Performance Improvement Features (for System Applications Only) (ArkTS)](camera-performance-improvement.md)
  - [High-Performance Photo Capture Sample (for System Applications Only) (ArkTS)](camera-deferred-photo-case.md)
  <!--DelEnd-->
- Camera Development (C/C++)
  - [Camera Device Management (C/C++)](native-camera-device-management.md)
  - [Device Input Management (C/C++)](native-camera-device-input.md)
  - [Camera Session Management (C/C++)](native-camera-session-management.md)
  - [Preview (C/C++)](native-camera-preview.md)
  - [Secondary Processing of Preview Streams (C/C++)](native-camera-preview-imageReceiver.md)
  - [Photo Capture (C/C++)](native-camera-shooting.md)
  - [Deferred Photo Delivery (C/C++)](native-camera-deferred-capture.md)
  - [Video Recording (C/C++)](native-camera-recording.md)
  - [Secondary Processing of Video Streams (C/C++)](native-camera-recording-imageReceiver.md)
  - [Camera Metadata (C/C++)](native-camera-metadata.md)
  - [Using the Flashlight (C/C++)](native-camera-torch-use.md)
- Camera Best Practices (C/C++)
  - [Photo Capture Sample (C/C++)](native-camera-shooting-case.md)
  - [Video Recording Sample (C/C++)](native-camera-recording-case.md)
  - [Sample of Secondary Processing of Video Streams (C/C++)](native-camera-recording-case-imageReceiver.md)
