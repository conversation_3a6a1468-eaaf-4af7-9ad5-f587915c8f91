# FA Model Development Overview


During application development based on the Feature Ability (FA) model, the following tasks are involved in the application model.


  **Table 1** FA model development process

| Task| Introduction| Guide|
| -------- | -------- | -------- |
| Application component development| Use the PageAbility, ServiceAbility, DataAbility, and widgets of the FA model to develop applications.| - [Application- or Component-Level Configuration](application-component-configuration-fa.md)<br>- [PageAbility Component Development](pageability-overview.md)<br>- [ServiceAbility Component Development](serviceability-overview.md)<br>- [DataAbility Component Development](dataability-overview.md)<br>- [Service Widget Development in FA Model](../form/widget-development-fa.md)<br>- [Context](application-context-fa.md)<br>- [Want](want-fa.md)|
| Process model| Learn the process model and common IPC modes of the FA model.| [Process Model Overview](process-model-fa.md)| |
| Thread model| Learn the thread model and common inter-thread communication modes of the FA model.| [Thread Model Overview](thread-model-fa.md)|
| Application configuration file| Learn the requirements for developing application configuration files in the FA model.| [Application Configuration File](config-file-fa.md)|
