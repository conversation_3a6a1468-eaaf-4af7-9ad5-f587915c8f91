# @ohos.app.ability.ConfigurationConstant (ConfigurationConstant)

The **ConfigurationConstant** module provides the enumerated values of the environment configuration information.

> **NOTE**
> 
> The initial APIs of this module are supported since API version 9. Newly added APIs will be marked with a superscript to indicate their earliest API version.

## Modules to Import

```ts
import { ConfigurationConstant } from '@kit.AbilityKit';
```

## ColorMode

Enumerates the color modes.

**Atomic service API**: This API can be used in atomic services since API version 11.

**System capability**: SystemCapability.Ability.AbilityBase

| Name| Value| Description| 
| -------- | -------- | -------- |
| COLOR_MODE_NOT_SET | -1 | Unspecified color mode.| 
| COLOR_MODE_DARK | 0 | Dark mode.| 
| COLOR_MODE_LIGHT | 1 | Light mode.| 


## Direction

Enumerates the display orientations.

**Atomic service API**: This API can be used in atomic services since API version 11.

**System capability**: SystemCapability.Ability.AbilityBase

| Name| Value| Description| 
| -------- | -------- | -------- |
| DIRECTION_NOT_SET | -1 | Unspecified direction.| 
| DIRECTION_VERTICAL | 0 | Vertical direction.| 
| DIRECTION_HORIZONTAL | 1 | Horizontal direction.| 


## ScreenDensity

Enumerates the screen density modes.

**Atomic service API**: This API can be used in atomic services since API version 11.

**System capability**: SystemCapability.Ability.AbilityBase

| Name| Value| Description| 
| -------- | -------- | -------- |
| SCREEN_DENSITY_NOT_SET | 0 | The screen pixel density is not set.| 
| SCREEN_DENSITY_SDPI | 120 | The pixel density of the screen is 'SDPI'.| 
| SCREEN_DENSITY_MDPI | 160 | The pixel density of the screen is 'MDPI'.| 
| SCREEN_DENSITY_LDPI | 240 | The pixel density of the screen is 'LDPI'.| 
| SCREEN_DENSITY_XLDPI | 320 | The pixel density of the screen is 'XLDPI'.| 
| SCREEN_DENSITY_XXLDPI | 480 | The pixel density of the screen is 'XXLDPI'.| 
| SCREEN_DENSITY_XXXLDPI | 640 | The pixel density of the screen is 'XXXLDPI'.| 
