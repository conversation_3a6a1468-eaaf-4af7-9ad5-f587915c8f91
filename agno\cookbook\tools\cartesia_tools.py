"""
pip install cartesia
Get an API key from https://play.cartesia.ai/keys
"""

from agno.agent import Agent
from agno.tools.cartesia import CartesiaTools
from agno.utils.media import save_audio

# Initialize Agent with Cartesia tools
agent = Agent(
    name="Cartesia TTS Agent",
    description="An agent that uses Cartesia for text-to-speech.",
    tools=[CartesiaTools()],
    show_tool_calls=True,
)

response = agent.run(
    f"""Generate a simple greeting using Text-to-Speech:

    Say "Welcome to Cartesia, the advanced speech synthesis platform. This speech is generated by an agent. """
)
# Save the generated audio
if response.audio:
    save_audio(
        base64_data=response.audio[0].base64_audio, output_path="tmp/greeting.mp3"
    )
