# LifecycleData Switching


  | API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding API in the Stage Model| 
| -------- | -------- | -------- |
| update?(uri: string, valueBucket: rdb.ValuesBucket, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;number&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [update?(uri: string, predicates: dataSharePredicates.DataSharePredicates, valueBucket: ValuesBucket, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#update) |
| query?(uri: string, columns: Array&lt;string&gt;, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;ResultSet&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [query?(uri: string, predicates: dataSharePredicates.DataSharePredicates, columns: Array&lt;string&gt;, callback: AsyncCallback&lt;Object&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#query) |
| delete?(uri: string, predicates: dataAbility.DataAbilityPredicates, callback: AsyncCallback&lt;number&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [delete?(uri: string, predicates: dataSharePredicates.DataSharePredicates, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#delete) |
| normalizeUri?(uri: string, callback: AsyncCallback&lt;string&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [normalizeUri?(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#normalizeuri) |
| batchInsert?(uri: string, valueBuckets: Array&lt;rdb.ValuesBucket&gt;, callback: AsyncCallback&lt;number&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [batchInsert?(uri: string, valueBuckets: Array&lt;ValuesBucket&gt;, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#batchinsert) |
| denormalizeUri?(uri: string, callback: AsyncCallback&lt;string&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [denormalizeUri?(uri: string, callback: AsyncCallback&lt;string&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#denormalizeuri) |
| insert?(uri: string, valueBucket: rdb.ValuesBucket, callback: AsyncCallback&lt;number&gt;): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [insert?(uri: string, valueBucket: ValuesBucket, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#insert) |
| openFile?(uri: string, mode: string, callback: AsyncCallback&lt;number&gt;): void; | There is no corresponding API in the stage model.| The stage model does not support cross-process URI access. You are advised to use [the **want** parameter to carry the file descriptor and file information](file-processing-apps-startup.md) for cross-process file access.|
| getFileTypes?(uri: string, mimeTypeFilter: string, callback: AsyncCallback&lt;Array&lt;string&gt;&gt;): void; | There is no corresponding API in the stage model.| The stage model does not support cross-process URI access. You are advised to use [the **want** parameter to carry the file descriptor and file information](file-processing-apps-startup.md) for cross-process file access.|
| onInitialized?(info: AbilityInfo): void; | \@ohos.application.DataShareExtensionAbility.d.ts | [onCreate?(want: Want, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-arkdata/js-apis-application-dataShareExtensionAbility-sys.md#oncreate) |
| getType?(uri: string, callback: AsyncCallback&lt;string&gt;): void; | There is no corresponding API in the stage model.| The stage model does not support cross-process URI access. You are advised to use [the **want** parameter to carry the file descriptor and file information](file-processing-apps-startup.md) for cross-process file access.|
| executeBatch?(ops: Array&lt;DataAbilityOperation&gt;, callback: AsyncCallback&lt;Array&lt;DataAbilityResult&gt;&gt;): void; | There is no corresponding API in the stage model.| No corresponding API is provided.|
| call?(method: string, arg: string, extras: PacMap, callback: AsyncCallback&lt;PacMap&gt;): void; | There is no corresponding API in the stage model.| No corresponding API is provided.|
