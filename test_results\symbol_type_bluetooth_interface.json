[{"symbol_name": "DeviceClass", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the class of a bluetooth device. @typedef DeviceClass @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.DeviceClass", "import_statement": "import { bluetooth.DeviceClass } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.DeviceClass", "score": 0.6738663}, {"symbol_name": "SppOption", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the spp parameters. @typedef SppOption @syscap SystemCapability.Communication.Bluetooth.Core @since 8 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.SppOption", "import_statement": "import { bluetooth.SppOption } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.SppOption", "score": 0.64210594}, {"symbol_name": "AdvertiseSetting", "symbol_type": "interface", "module_name": "@ohos.bluetooth", "is_default": false, "is_ets": false, "description": "Describes the settings for BLE advertising. @typedef AdvertiseSetting @syscap SystemCapability.Communication.Bluetooth.Core @since 7 @deprecated since 9 @useinstead ohos.bluetoothManager/bluetoothManager.AdvertiseSetting", "import_statement": "import { bluetooth.AdvertiseSetting } from '@ohos.bluetooth';", "parent_symbol": "bluetooth", "is_nested": true, "full_name": "bluetooth.AdvertiseSetting", "score": 0.6382396}]