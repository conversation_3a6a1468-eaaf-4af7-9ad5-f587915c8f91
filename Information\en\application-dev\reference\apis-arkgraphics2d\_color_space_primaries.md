# ColorSpacePrimaries


## Overview

The ColorSpacePrimaries struct describes the color space primaries.

**Since**: 13

**Related module**: [NativeColorSpaceManager](_native_color_space_manager.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| float [rX](#rx) | X-coordinate of the red color. | 
| float [rY](#ry) | Y-coordinate of the red color. | 
| float [gX](#gx) | X-coordinate of the green color. | 
| float [gY](#gy) | Y-coordinate of the green color. | 
| float [bX](#bx) | X-coordinate of the blue color. | 
| float [bY](#by) | Y-coordinate of the blue color. | 
| float [wX](#wx) | X-coordinate of the white point. | 
| float [wY](#wy) | Y-coordinate of the white point. | 


## Member Variable Description


### bX

```
float ColorSpacePrimaries::bX
```

**Description**

X-coordinate of the blue color.


### bY

```
float ColorSpacePrimaries::bY
```

**Description**

Y-coordinate of the blue color.


### gX

```
float ColorSpacePrimaries::gX
```

**Description**

X-coordinate of the green color.


### gY

```
float ColorSpacePrimaries::gY
```

**Description**

Y-coordinate of the green color.


### rX

```
float ColorSpacePrimaries::rX
```

**Description**

X-coordinate of the red color.


### rY

```
float ColorSpacePrimaries::rY
```

**Description**

Y-coordinate of the red color.


### wX

```
float ColorSpacePrimaries::wX
```

**Description**

X-coordinate of the white point.


### wY

```
float ColorSpacePrimaries::wY
```

**Description**

Y-coordinate of the white point.
