"""
Create a test dataset for ArkTS import suggestion system.
"""

import os
import shutil
import json

def create_directory(directory):
    """Create a directory if it doesn't exist."""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def copy_file(source, destination):
    """Copy a file from source to destination."""
    try:
        shutil.copy2(source, destination)
        print(f"Copied: {source} -> {destination}")
        return True
    except Exception as e:
        print(f"Error copying {source}: {e}")
        return False

def main():
    """Main function."""
    # Create test dataset directory
    test_dataset_dir = "test_dataset"
    create_directory(test_dataset_dir)

    # Load search results
    try:
        with open('search_results.json', 'r') as f:
            search_results = json.load(f)
    except Exception as e:
        print(f"Error loading search results: {e}")
        search_results = []

    # Filter files
    button_files = [file for file in search_results if "button" in file.lower()]
    dialog_files = [file for file in search_results if "dialog" in file.lower()]

    # Select specific files
    selected_files = []

    # Add button files
    selected_files.append("Information\\default\\openharmony\\ets\\component\\button.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\arkui\\ButtonModifier.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.arkui.advanced.ProgressButton.d.ets")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.arkui.advanced.SegmentButton.d.ets")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.arkui.advanced.DownloadFileButton.d.ets")

    # Add dialog files
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.arkui.advanced.Dialog.d.ets")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\alert_dialog.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\custom_dialog_controller.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.atomicservice.InterstitialDialogAction.d.ets")

    # Add component files
    selected_files.append("Information\\default\\openharmony\\ets\\component\\button_component.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\dialog_component.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\text.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\image.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\grid.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\component\\list.d.ts")

    # Add other important files
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.multimedia.audio.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.net.http.d.ts")
    selected_files.append("Information\\default\\openharmony\\ets\\api\\@ohos.bluetooth.d.ts")

    # Copy files
    copied_files = []
    for file in selected_files:
        if os.path.exists(file):
            destination = os.path.join(test_dataset_dir, os.path.basename(file))
            if copy_file(file, destination):
                copied_files.append(destination)
        else:
            print(f"File not found: {file}")

    # Create a list of copied files
    with open(os.path.join(test_dataset_dir, "files.txt"), "w") as f:
        for file in copied_files:
            f.write(f"{file}\n")

    print(f"\nCreated test dataset with {len(copied_files)} files.")
    print(f"Files list saved to {os.path.join(test_dataset_dir, 'files.txt')}")

if __name__ == "__main__":
    main()
