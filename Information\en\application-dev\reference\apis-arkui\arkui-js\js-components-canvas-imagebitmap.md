# ImageBitmap

>  **NOTE**
>
>  The APIs of this module are supported since API version 7. Updates will be marked with a superscript to indicate their earliest API version.


The **ImageBitmap** object is an object generated using the [transferToImageBitmap()](js-components-canvas-offscreencanvas.md#transfertoimagebitmap) method of [OffscreenCanvas](js-components-canvas-offscreencanvas.md) and stores the pixel data rendered on the offscreen canvas.


## Attributes

| Name| Type| Description|
| -------- | -------- | -------- |
| width | number | Pixel width of the **ImageBitmap** object.|
| height | number | Pixel height of the **ImageBitmap** object.|
