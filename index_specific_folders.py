#!/usr/bin/env python3
"""
Script to clear the Qdrant database and index only specific folders with maximum speed.
"""

import os
import sys
import time
import argparse
import logging
from qdrant_client import QdrantClient

# Import local modules
import config
from arkts_indexer import ArkTSIndexer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format=config.LOG_FORMAT,
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("index_specific_folders.log")
    ]
)
logger = logging.getLogger("IndexSpecificFolders")

def clear_database(qdrant_url: str, collection_name: str) -> bool:
    """
    Clear the Qdrant database by deleting the collection.
    
    Args:
        qdrant_url: URL of the Qdrant server
        collection_name: Name of the collection to clear
        
    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Connecting to Qdrant at {qdrant_url}")
        print(f"Connecting to Qdrant at {qdrant_url}")
        
        client = QdrantClient(
            url=qdrant_url,
            prefer_grpc=False,
            timeout=60.0,
            check_compatibility=False
        )
        
        # Check if collection exists
        collections = client.get_collections().collections
        collection_exists = any(collection.name == collection_name for collection in collections)
        
        if collection_exists:
            logger.info(f"Deleting existing collection: {collection_name}")
            print(f"Deleting existing collection: {collection_name}")
            client.delete_collection(collection_name=collection_name)
            logger.info(f"Collection {collection_name} deleted successfully")
            print(f"Collection {collection_name} deleted successfully")
        else:
            logger.info(f"Collection {collection_name} does not exist, nothing to delete")
            print(f"Collection {collection_name} does not exist, nothing to delete")
        
        return True
        
    except Exception as e:
        logger.error(f"Error clearing database: {str(e)}")
        print(f"Error clearing database: {str(e)}")
        return False

def index_directories(directories: list, qdrant_url: str, collection_name: str, ollama_url: str, embedding_model: str) -> int:
    """
    Index all files in the specified directories with maximum speed.
    
    Args:
        directories: List of directories to index
        qdrant_url: URL of the Qdrant server
        collection_name: Name of the collection to index into
        ollama_url: URL of the Ollama server
        embedding_model: Name of the embedding model to use
        
    Returns:
        Total number of symbols indexed
    """
    try:
        # Initialize indexer with force_recreate=True to ensure a clean collection
        indexer = ArkTSIndexer(
            qdrant_url=qdrant_url,
            collection_name=collection_name,
            ollama_url=ollama_url,
            embedding_model=embedding_model,
            force_recreate=True
        )
        
        total_symbols = 0
        total_start_time = time.time()
        
        # Verify directories exist
        valid_directories = []
        for directory in directories:
            if os.path.exists(directory):
                valid_directories.append(directory)
                logger.info(f"Directory {directory} exists and will be indexed")
                print(f"Directory {directory} exists and will be indexed")
            else:
                logger.warning(f"Directory {directory} does not exist and will be skipped")
                print(f"WARNING: Directory {directory} does not exist and will be skipped")
        
        if not valid_directories:
            logger.error("No valid directories to index")
            print("ERROR: No valid directories to index")
            return 0
        
        # Index each directory
        for directory in valid_directories:
            logger.info(f"Indexing directory: {directory}")
            print(f"\n\n{'='*80}")
            print(f"Indexing directory: {directory}")
            print(f"{'='*80}")
            
            dir_start_time = time.time()
            symbols_indexed = indexer.index_directory(directory)
            dir_time = time.time() - dir_start_time
            
            total_symbols += symbols_indexed
            
            print(f"\nDirectory {directory} completed:")
            print(f"  - Symbols indexed: {symbols_indexed}")
            print(f"  - Time taken: {dir_time:.2f} seconds")
            if symbols_indexed > 0:
                print(f"  - Speed: {symbols_indexed / dir_time:.2f} symbols/second")
            
        total_time = time.time() - total_start_time
        
        print(f"\n\n{'='*80}")
        print(f"Indexing complete!")
        print(f"{'='*80}")
        print(f"Total symbols indexed: {total_symbols}")
        print(f"Total time taken: {total_time:.2f} seconds")
        if total_symbols > 0:
            print(f"Overall speed: {total_symbols / total_time:.2f} symbols/second")
        
        return total_symbols
        
    except Exception as e:
        logger.error(f"Error indexing directories: {str(e)}")
        print(f"Error indexing directories: {str(e)}")
        return 0

def main():
    """Main function."""
    # Define the specific folders to index
    specific_folders = [
        "Information\\en\\application-dev",
        "Information\\default\\hms",
        "Information\\default\\openharmony",
    ]
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Clear Qdrant database and index specific folders with maximum speed')
    parser.add_argument('--qdrant-url', type=str, default=config.QDRANT_URL, help='Qdrant server URL')
    parser.add_argument('--collection', type=str, default=config.COLLECTION_NAME, help='Qdrant collection name')
    parser.add_argument('--ollama-url', type=str, default=config.OLLAMA_URL, help='Ollama server URL')
    parser.add_argument('--embedding-model', type=str, default=config.EMBEDDING_MODEL, help='Ollama embedding model to use')
    parser.add_argument('--skip-clear', action='store_true', help='Skip clearing the database')
    parser.add_argument('--directories', type=str, nargs='+', help='Override default directories to index')
    
    args = parser.parse_args()
    
    # Use provided directories if specified, otherwise use the default specific folders
    directories_to_index = args.directories if args.directories else specific_folders
    
    print(f"\n{'='*80}")
    print(f"ArkTS Import Estimator - Index Specific Folders with MAXIMUM SPEED")
    print(f"{'='*80}")
    print(f"Qdrant URL: {args.qdrant_url}")
    print(f"Collection: {args.collection}")
    print(f"Ollama URL: {args.ollama_url}")
    print(f"Embedding model: {args.embedding_model}")
    print(f"Directories to index: {directories_to_index}")
    print(f"Skip clearing database: {args.skip_clear}")
    print(f"{'='*80}\n")
    
    # Clear database if not skipped
    if not args.skip_clear:
        print("Clearing database...")
        if not clear_database(args.qdrant_url, args.collection):
            print("Failed to clear database. Exiting.")
            return 1
        print("Database cleared successfully.")
    else:
        print("Skipping database clearing as requested.")
    
    # Index directories
    print("\nStarting indexing process with MAXIMUM SPEED...")
    total_symbols = index_directories(
        directories_to_index,
        args.qdrant_url,
        args.collection,
        args.ollama_url,
        args.embedding_model
    )
    
    if total_symbols > 0:
        print("\nIndexing completed successfully!")
        return 0
    else:
        print("\nIndexing failed or no symbols were indexed.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
