# drawing_shadow_layer.h


## Overview

The **, which is an enumerated value of** file declares the functions related to the shadow in the drawing module.

**File to include**: &lt;native_drawing/drawing_shadow_layer.h&gt;

**Library**: libnative_drawing.so

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Functions

| Name| Description|
| -------- | -------- |
| [OH_Drawing_ShadowLayer](_drawing.md#oh_drawing_shadowlayer) \* [OH_Drawing_ShadowLayerCreate](_drawing.md#oh_drawing_shadowlayercreate) (float blurRadius, float x, float y, uint32_t color) | Creates an **OH_Drawing_ShadowLayer** object.|
| void [OH_Drawing_ShadowLayerDestroy](_drawing.md#oh_drawing_shadowlayerdestroy) ([OH_Drawing_ShadowLayer](_drawing.md#oh_drawing_shadowlayer) \*) | Destroys an **OH_Drawing_ShadowLayer** object and reclaims the memory occupied by the object.|

 <!--no_check--> 