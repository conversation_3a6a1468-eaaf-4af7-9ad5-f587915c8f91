# Ability Kit

- [Introduction to Ability Kit](abilitykit-overview.md)
- [Application Models](application-models.md)
- Stage Model Development
  - [Stage Model Development Overview](stage-model-development-overview.md)
  - Stage Model Application Components
    - [Application- or Component-Level Configuration](application-component-configuration-stage.md)
    - UIAbility Component
      - [UIAbility Overview](uiability-overview.md)
      - [UIAbility Lifecycle](uiability-lifecycle.md)
      - [UIAbility Launch Type](uiability-launch-type.md)
      - [UIAbility Usage](uiability-usage.md)
      - [Data Synchronization Between UIAbility and UI Page](uiability-data-sync-with-ui.md)
      - [Starting UIAbility in the Same Application](uiability-intra-device-interaction.md)
    - [ExtensionAbility Component](extensionability-overview.md)
      <!--Del-->
      - [ServiceExtensionAbility (for System Applications Only)](serviceextensionability.md)
      - [UIExtensionAbility (for System Applications Only)](uiextensionability.md)
      - [AutoFillExtensionAbility (for System Applications Only)](autofillextensionablility-guide.md)
      <!--DelEnd-->
      - [EmbeddedUIExtensionAbility](embeddeduiextensionability.md)
    - [AbilityStage Component Container](abilitystage.md)
    - [Context](application-context-stage.md)
    - Want
      - [Want Overview](want-overview.md)
      - [Matching Rules of Explicit Want and Implicit Want](explicit-implicit-want-mappings.md)
      - [Using Explicit Want to Start an Application Component](ability-startup-with-explicit-want.md)
      - [Common action and entities Values (Not Recommended)](actions-entities.md)
    - [Component Startup Rules (Stage Model)](component-startup-rules.md)
    - [AppStartup](app-startup.md)
    <!--Del-->
    - Inter-Device Application Component Interaction (Hopping)
      - [Hopping Overview](inter-device-interaction-hop-overview.md)
      - [Cross-Device Migration](hop-cross-device-migration.md)
      - [Multi-device Collaboration](hop-multi-device-collaboration.md)
    <!--DelEnd-->
    - [Subscribing to System Environment Variable Changes](subscribe-system-environment-variable-changes.md)
  - Inter-Application Redirection
    - [Overview of Application Redirection](link-between-apps-overview.md)
    - Starting a Specified Application<!--directional-redirection-->
      - [Overview of Starting a Specified Application](app-startup-overview.md)
      - [(Optional) Using canOpenLink to Check Application Accessibility](canopenlink.md)
      - [Using Deep Linking for Application Redirection](deep-linking-startup.md)
      <!--Del-->
      - [Using App Linking for Application Redirection](app-linking-startup.md)
      <!--DelEnd-->
      - [Switching from Explicit Want Redirection to Linking Redirection](uiability-startup-adjust.md)
      - [Application Link Description](app-uri-config.md)
    - Starting an Application of the Specified Type<!--specified-type-app-redirection-->
      - [Overview of Starting an Application of the Specified Type](start-intent-panel.md)
      - [Using startAbilityByType to Start a Navigation Application](start-navigation-apps.md)
      - [Using startAbilityByType to Start an Email Application](start-email-apps.md)
      - [Using mailto to Start an Email Application](start-email-apps-by-mailto.md)
      - [Using startAbilityByType to Start a Financial Application](start-finance-apps.md)
      - [Using startAbilityByType to Start an Image Editing Application](photoEditorExtensionAbility.md)
      - [Using startAbility to Start a File Application](file-processing-apps-startup.md)
    - [Starting a System Application](system-app-startup.md)
  - [Process Model (Stage Model)](process-model-stage.md)
  - [Thread Model (Stage Model)](thread-model-stage.md)
  <!--Del-->
  - Mission Management (for System Applications Only)
    - [Mission Management Scenarios](mission-management-overview.md)
    - [Mission and Launch Type](mission-management-launch-type.md)
    - [Page Stack and Mission List](page-mission-stack.md)
    - [Setting the Icon and Name of a Mission Snapshot](mission-set-icon-name-for-task-snapshot.md)
  <!--DelEnd-->
  - [Application Configuration File](config-file-stage.md)
- FA Model Development
  - [FA Model Development Overview](fa-model-development-overview.md)
  - FA Model Application Components
    - [Application- or Component-Level Configuration](application-component-configuration-fa.md)
    - PageAbility Component Development
      - [PageAbility Overview](pageability-overview.md)
      - [PageAbility Configuration](pageability-configuration.md)
      - [PageAbility Lifecycle](pageability-lifecycle.md)
      - [PageAbility Launch Type](pageability-launch-type.md)
      - [Creating a PageAbility](create-pageability.md)
      - [Starting a Local PageAbility](start-local-pageability.md)
      - [Stopping a PageAbility](stop-pageability.md)
      - [Starting a Remote PageAbility (for System Applications Only)](start-remote-pageability.md)
      - [Starting a Specified Page](start-page.md)
      - [Window Properties](window-properties.md)
      - [Requesting Permissions](request-permissions.md)
      - [Redirection Rules](redirection-rules.md)
    - ServiceAbility Component Development
      - [ServiceAbility Overview](serviceability-overview.md)
      - [ServiceAbility Configuration](serviceability-configuration.md)
      - [ServiceAbility Lifecycle](serviceability-lifecycle.md)
      - [Creating a ServiceAbility](create-serviceability.md)
      - [Starting a ServiceAbility](start-serviceability.md)
      - [Connecting to a ServiceAbility](connect-serviceability.md)
    - DataAbility Component Development
      - [DataAbility Overview](dataability-overview.md)
      - [DataAbility Configuration](dataability-configuration.md)
      - [DataAbility Lifecycle](dataability-lifecycle.md)
      - [Creating a DataAbility](create-dataability.md)
      - [Starting a DataAbility](start-dataability.md)
      - [Accessing a DataAbility](access-dataability.md)
      - [DataAbility Permission Control](dataability-permission-control.md)
    - [Context](application-context-fa.md)
    - [Want](want-fa.md)
    - [Component Startup Rules (FA Model)](component-startup-rules-fa.md)
  - [Process Model (Stage Model)](process-model-fa.md)
  - [Thread Model (Stage Model)](thread-model-fa.md)
  <!--Del-->
  - [Mission Management (for System Applications Only)](mission-management-fa.md)
  <!--DelEnd-->
  - [Application Configuration File](config-file-fa.md)
<!--Del-->
- Development of Component Interaction Between the FA Model and Stage Model
  - [Component Interaction Between the FA Model and Stage Model](fa-stage-interaction-overview.md)
  - [Starting a UIAbility from the FA Model](start-uiability-from-fa.md)
  - [Connecting to a ServiceExtensionAbility from the FA Model](bind-serviceextensionability-from-fa.md)
  - [Accessing a DataShareExtensionAbility from the FA Model](access-datashareextensionability-from-fa.md)
  - [Starting a PageAbility from the Stage Model](start-pageability-from-stage.md)
  - [Connecting to a ServiceAbility from the Stage Model](bind-serviceability-from-stage.md)
- Switching from the FA Model to the Stage Model
  - [Model Switching Overview](model-switch-overview.md)
  - Configuration File Switching
    - [Differences in Configuration Files](configuration-file-diff.md)
    - [Switching of app and deviceConfig](app-deviceconfig-switch.md)
    - [Switching of module](module-switch.md)
  - Component Switching
    - [PageAbility Switching](pageability-switch.md)
    - [ServiceAbility Switching](serviceability-switch.md)
    - [DataAbility Switching](dataability-switch.md)
  - [Widget Switching](widget-switch.md)
  - API Switching
    - [API Switching Overview](api-switch-overview.md)
    - [Context Switching](context-switch.md)
    - [featureAbility Switching](featureability-switch.md)
    - [particleAbility Switching](particleability-switch.md)
    - [LifecycleForm Switching](lifecycleform-switch.md)
    - [LifecycleApp Switching](lifecycleapp-switch.md)
    - [LifecycleService Switching](lifecycleservice-switch.md)
    - [LifecycleData Switching](lifecycledata-switch.md)
    - [DataAbilityHelper Switching](dataabilityhelper-switch.md)
    - [request Switching](request-switch.md)
    - [resourceManager Switching](resourcemanager-switch.md)
    - [window Switching](window-switch.md)
    - [Storage Switching](storage-switch.md)
<!--DelEnd-->
- [Native Child Process Development (C/C++)](capi_nativechildprocess_development_guideline.md)
