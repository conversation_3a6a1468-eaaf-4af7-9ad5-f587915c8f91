{"total_queries": 20, "successful_queries": 0, "failed_queries": 20, "query_results": [{"query": "<PERSON><PERSON>", "query_type": "component", "success": false, "query_time": 60.10667848587036, "results_length": 45, "error": "No meaningful results or error in response"}, {"query": "Dialog", "query_type": "component", "success": false, "query_time": 60.057395935058594, "results_length": 45, "error": "No meaningful results or error in response"}, {"query": "Text", "query_type": "component", "success": false, "query_time": 60.060176610946655, "results_length": 43, "error": "No meaningful results or error in response"}, {"query": "Image", "query_type": "component", "success": false, "query_time": 60.041367292404175, "results_length": 44, "error": "No meaningful results or error in response"}, {"query": "List", "query_type": "component", "success": false, "query_time": 60.059547901153564, "results_length": 43, "error": "No meaningful results or error in response"}, {"query": "UIAbility", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "router", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "preferences", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "http", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "bluetooth", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.app.ability.UIAbility", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.router", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.data.preferences", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.net.http", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "@ohos.bluetooth", "query_type": "import_path", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "file system", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "data storage", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "network request", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "device sensor", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}, {"query": "multimedia audio", "query_type": "api", "success": false, "query_time": 0, "error": "'Function' object is not callable"}], "errors": ["Error testing query 'UIAbility': 'Function' object is not callable", "Error testing query 'router': 'Function' object is not callable", "Error testing query 'preferences': 'Function' object is not callable", "Error testing query 'http': 'Function' object is not callable", "Error testing query 'bluetooth': 'Function' object is not callable", "Error testing query '@ohos.app.ability.UIAbility': 'Function' object is not callable", "Error testing query '@ohos.router': 'Function' object is not callable", "Error testing query '@ohos.data.preferences': 'Function' object is not callable", "Error testing query '@ohos.net.http': 'Function' object is not callable", "Error testing query '@ohos.bluetooth': 'Function' object is not callable", "Error testing query 'file system': 'Function' object is not callable", "Error testing query 'data storage': 'Function' object is not callable", "Error testing query 'network request': 'Function' object is not callable", "Error testing query 'device sensor': 'Function' object is not callable", "Error testing query 'multimedia audio': 'Function' object is not callable"]}