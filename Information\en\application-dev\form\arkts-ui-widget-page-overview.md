# Widget Page Capability Overview

You can leverage the ArkUI declarative paradigm to develop ArkTS widget pages. The following widget pages are automatically generated by a DevEco Studio template. You can adjust the pages based on the real-world service scenarios.

![WidgetPreviewPage](figures/WidgetPreviewPage.png)


ArkTS widgets have full capabilities of JS widgets, with added animation and custom drawing capabilities plus partial support for components, events, animations, data management, and state management capabilities of the [declarative paradigm](../ui/arkts-ui-development-overview.md).


## Page Capabilities Supported by ArkTS Widgets

For details about the capabilities supported by ArkTS widgets, see <!--RP1-->[Learning ArkTS](../quick-start/arkts-get-started.md)<!--RP1End--> and [ArkTS-based Declarative Development Paradigm](../ui/arkts-ui-development-overview.md).

Only the components and APIs marked with "supported in ArkTS widgets" can be used for ArkTS widgets. Pay special attention to the differences from applications.

For example, the following description indicates that the @Component decorator can be used in ArkTS widgets.

![WidgetSupportApi](figures/WidgetSupportApi.png)
