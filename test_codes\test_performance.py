"""
Test Performance Optimization

This module tests the performance optimization features of the ArkTS import suggestion system.
It includes tests for caching, batch processing, and parallel processing.
"""

import unittest
import time
import logging
from typing import List, Dict, Any
import concurrent.futures

# Import the performance optimizer
from performance_optimizer import PerformanceOptimizer

# Import the query classes
from arkts_query import Ark<PERSON><PERSON>uery
from arkts_query_cached import ArkTSQueryCached
from arkts_query_enhanced import ArkTSQueryEnhanced
from arkts_query_async import ArkTSQueryAsync

# Import configuration
import config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("TestPerformance")


class TestPerformance(unittest.TestCase):
    """Test the performance optimization features."""

    def setUp(self):
        """Set up the test environment."""
        # Initialize query classes
        self.query = ArkTSQuery()
        self.cached_query = ArkTSQueryCached()
        self.enhanced_query = ArkTSQueryEnhanced()
        self.async_query = ArkTSQueryAsync()
        
        # Initialize performance optimizer
        self.perf_opt = PerformanceOptimizer(
            cache_size=1000,
            cache_ttl=3600,
            batch_size=10,
            num_threads=4
        )
        
        # Test queries
        self.test_queries = [
            "button",
            "audio",
            "http",
            "dialog",
            "bluetooth"
        ]

    def test_caching(self):
        """Test caching performance."""
        # Clear cache
        self.cached_query.clear_cache()
        
        # First query (cache miss)
        start_time = time.time()
        results1 = self.cached_query.suggest_imports(self.test_queries[0])
        first_query_time = time.time() - start_time
        
        # Second query (cache hit)
        start_time = time.time()
        results2 = self.cached_query.suggest_imports(self.test_queries[0])
        second_query_time = time.time() - start_time
        
        # Check results
        self.assertEqual(results1, results2)
        
        # Check timing
        self.assertLess(second_query_time, first_query_time)
        speedup = first_query_time / max(second_query_time, 0.0001)  # Avoid division by zero
        logger.info(f"Cache speedup: {speedup:.2f}x")
        
        # Check that speedup is significant
        self.assertGreater(speedup, 10)

    def test_batch_processing(self):
        """Test batch processing performance."""
        # Define batch processing function
        def process_batch(batch):
            results = []
            for query in batch:
                results.append(self.query.suggest_imports(query))
            return results
        
        # Process queries in batches
        start_time = time.time()
        batch_results = self.perf_opt.batch_process(process_batch, self.test_queries)
        batch_time = time.time() - start_time
        
        # Process queries individually
        start_time = time.time()
        individual_results = []
        for query in self.test_queries:
            individual_results.append(self.query.suggest_imports(query))
        individual_time = time.time() - start_time
        
        # Check results
        self.assertEqual(len(batch_results), len(individual_results))
        
        # Check timing
        logger.info(f"Batch time: {batch_time:.4f}s, Individual time: {individual_time:.4f}s")
        
        # Note: Batch processing may not always be faster for small batches
        # but it should be more efficient for larger batches

    def test_parallel_processing(self):
        """Test parallel processing performance."""
        # Define processing function
        def process_query(query):
            return self.query.suggest_imports(query)
        
        # Process queries in parallel
        start_time = time.time()
        parallel_results = self.perf_opt.parallel_process(process_query, self.test_queries)
        parallel_time = time.time() - start_time
        
        # Process queries sequentially
        start_time = time.time()
        sequential_results = []
        for query in self.test_queries:
            sequential_results.append(process_query(query))
        sequential_time = time.time() - start_time
        
        # Check results
        self.assertEqual(len(parallel_results), len(sequential_results))
        
        # Check timing
        logger.info(f"Sequential time: {sequential_time:.4f}s, Parallel time: {parallel_time:.4f}s")
        
        # Parallel processing should be faster
        self.assertLess(parallel_time, sequential_time)
        speedup = sequential_time / parallel_time
        logger.info(f"Parallel speedup: {speedup:.2f}x")
        
        # Check that speedup is reasonable
        # For 5 queries and 4 threads, speedup should be close to 4x
        # but may be less due to overhead
        self.assertGreater(speedup, 1.5)

    def test_cached_decorator(self):
        """Test cached decorator performance."""
        # Define function with cached decorator
        @self.perf_opt.cached
        def expensive_function(query):
            # Simulate expensive computation
            time.sleep(0.1)
            return self.query.suggest_imports(query)
        
        # First call (cache miss)
        start_time = time.time()
        results1 = expensive_function(self.test_queries[0])
        first_call_time = time.time() - start_time
        
        # Second call (cache hit)
        start_time = time.time()
        results2 = expensive_function(self.test_queries[0])
        second_call_time = time.time() - start_time
        
        # Check results
        self.assertEqual(results1, results2)
        
        # Check timing
        self.assertLess(second_call_time, first_call_time)
        speedup = first_call_time / max(second_call_time, 0.0001)  # Avoid division by zero
        logger.info(f"Cached decorator speedup: {speedup:.2f}x")
        
        # Check that speedup is significant
        self.assertGreater(speedup, 10)

    def test_async_performance(self):
        """Test async performance."""
        import asyncio
        from async_manager import AsyncManager
        
        # Define async function
        async def async_query(query):
            return await self.async_query.suggest_imports_async(query)
        
        # Run async queries
        async_mgr = AsyncManager()
        
        # Run queries concurrently
        start_time = time.time()
        async def run_concurrent():
            tasks = [async_query(query) for query in self.test_queries]
            return await asyncio.gather(*tasks)
        
        concurrent_results = async_mgr.run_in_new_loop(run_concurrent())
        concurrent_time = time.time() - start_time
        
        # Run queries sequentially
        start_time = time.time()
        sequential_results = []
        for query in self.test_queries:
            sequential_results.append(self.query.suggest_imports(query))
        sequential_time = time.time() - start_time
        
        # Check results
        self.assertEqual(len(concurrent_results), len(sequential_results))
        
        # Check timing
        logger.info(f"Sequential time: {sequential_time:.4f}s, Concurrent time: {concurrent_time:.4f}s")
        
        # Concurrent processing should be faster
        self.assertLess(concurrent_time, sequential_time)
        speedup = sequential_time / concurrent_time
        logger.info(f"Async speedup: {speedup:.2f}x")
        
        # Check that speedup is reasonable
        self.assertGreater(speedup, 1.5)


if __name__ == "__main__":
    unittest.main()
