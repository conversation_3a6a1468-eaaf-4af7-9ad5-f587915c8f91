# @ohos.util.HashSet (Nonlinear Container HashSet)

**HashSet** is implemented based on [HashMap](js-apis-hashmap.md). In **HashSet**, only the **value** object is processed.

Unlike [TreeSet](js-apis-treeset.md), which stores and accesses data in sorted order, **HashSet** stores data in a random order. This means that **HashSet** may use a different order when storing and accessing elements. Both of them allow only unique elements. However, null values are allowed in **HashSet**, but not in **TreeSet**, because null values may affect the order of elements in the container.

**Recommended use case**: Use **HashSet** when you need a set that has only unique elements or need to deduplicate a set.

This topic uses the following to identify the use of generics:
- T: Type

> **NOTE**
>
> The initial APIs of this module are supported since API version 8. Newly added APIs will be marked with a superscript to indicate their earliest API version.


## Modules to Import

```ts
import { HashSet } from '@kit.ArkTS';
```

## HashSet

### Attributes

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

| Name | Type | Readable | Writable | Description |
| -------- | -------- | -------- | -------- | -------- |
| length | number | Yes | No | Number of elements in a hash set (called container later). |

**Example**

```ts
let hashSet: HashSet<number> = new HashSet();
hashSet.add(1);
hashSet.add(2);
hashSet.add(3);
hashSet.add(4);
hashSet.add(5);
let res = hashSet.length;
```

### constructor

constructor()

A constructor used to create a **HashSet** instance.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Error codes**

For details about the error codes, see [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 10200012 | The HashSet's constructor cannot be directly invoked. |

**Example**

```ts
let hashSet: HashSet<number> = new HashSet();
```


### isEmpty

isEmpty(): boolean

Checks whether this container is empty (contains no element).

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Return value**

| Type | Description |
| -------- | -------- |
| boolean | Returns **true** if the container is empty; returns **false** otherwise. |

**Error codes**

For details about the error codes, see [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 10200011 | The isEmpty method cannot be bound. |

**Example**

```ts
const hashSet: HashSet<number> = new HashSet();
let result = hashSet.isEmpty();
```


### has

has(value: T): boolean

Checks whether this container contains the specified element.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Parameters**

| Name | Type | Mandatory | Description |
| -------- | -------- | -------- | -------- |
| value | T | Yes | Target element. |

**Return value**

| Type | Description |
| -------- | -------- |
| boolean | Returns **true** if the specified element is contained; returns **false** otherwise. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md) and [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed. |
| 10200011 | The has method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("squirrel");
let result = hashSet.has("squirrel");
```


### add

add(value: T): boolean

Adds an element to this container.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Parameters**

| Name | Type | Mandatory | Description |
| -------- | -------- | -------- | -------- |
| value | T | Yes | Target element. |

**Return value**

| Type | Description |
| -------- | -------- |
| boolean | Returns **true** if the element is added successfully; returns **false** otherwise. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md) and [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed. |
| 10200011 | The add method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
let result = hashSet.add("squirrel");
```


### remove

remove(value: T): boolean

Removes an element from this container.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Parameters**

| Name | Type | Mandatory | Description |
| -------- | -------- | -------- | -------- |
| value | T | Yes | Target element. |

**Return value**

| Type | Description |
| -------- | -------- |
| boolean | Returns **true** if the element is removed successfully; returns **false** otherwise. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md) and [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types; 3. Parameter verification failed. |
| 10200011 | The remove method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("squirrel");
hashSet.add("sparrow");
let result = hashSet.remove("sparrow");
```


### clear

clear(): void

Clears this container and sets its length to **0**.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Error codes**

For details about the error codes, see [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 10200011 | The clear method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("squirrel");
hashSet.add("sparrow");
hashSet.clear();
```


### values

values(): IterableIterator&lt;T&gt;

Obtains an iterator that contains all the values in this container.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Return value**

| Type | Description |
| -------- | -------- |
| IterableIterator&lt;T&gt; | Iterator obtained. |

**Error codes**

For details about the error codes, see [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 10200011 | The values method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("squirrel");
hashSet.add("sparrow");
let iter = hashSet.values();
let temp = iter.next();
while(!temp.done) {
  console.log("value:" + temp.value);
  temp = iter.next();
}
```


### forEach

forEach(callbackFn: (value?: T, key?: T, set?: HashSet&lt;T&gt;) => void, thisArg?: Object): void

Uses a callback to traverse the elements in this container and obtain their position indexes.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Parameters**

| Name | Type | Mandatory | Description |
| -------- | -------- | -------- | -------- |
| callbackFn | function | Yes | Callback invoked to traverse the elements in the container. |
| thisArg | Object | No | Value of **this** to use when **callbackFn** is invoked. The default value is this instance. |

callbackFn
| Name | Type | Mandatory | Description |
| -------- | -------- | -------- | -------- |
| value | T | No | Value of the element that is currently traversed. The default value is the value of the first key-value pair. |
| key | T | No | Key of the element that is currently traversed (same as **value**). The default value is the key of the first key-value pair. |
| set | HashSet&lt;T&gt; | No | Instance that calls the **forEach** API. The default value is this instance. |

**Error codes**

For details about the error codes, see [Universal Error Codes](../errorcode-universal.md) and [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 401      | Parameter error. Possible causes: 1. Mandatory parameters are left unspecified; 2. Incorrect parameter types. |
| 10200011 | The forEach method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("sparrow");
hashSet.add("squirrel");
hashSet.forEach((value?: string, key?: string): void => {
  console.log("value:" + value, "key:" + key);
});
```
```ts
// You are not advised to use the set or remove APIs in forEach because they may cause unpredictable risks such as infinite loops. You can use the for loop when inserting or deleting data.
let hashSet : HashSet<string> = new HashSet();
for(let i = 0;i < 10; i++) {
  hashSet.add("sparrow" + i);
}
for(let i = 0;i < 10; i++) {
  hashSet.remove("sparrow" + i);
}
```

### entries
entries(): IterableIterator<[T, T]>

Obtains an iterator that contains all the elements in this container.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

**Return value**

| Type | Description |
| -------- | -------- |
| IterableIterator<[T, T]> | Iterator obtained. |

**Error codes**

For details about the error codes, see [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 10200011 | The entries method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("squirrel");
hashSet.add("sparrow");
let iter = hashSet.entries();
let temp: IteratorResult<[string, string]> = iter.next();
while(!temp.done) {
  console.log("key:" + temp.value[0]);
  console.log("value:" + temp.value[1]);
  temp = iter.next();
}
```
```ts
// You are not advised to use the set or remove APIs in entries because they may cause unpredictable risks such as infinite loops. You can use the for loop when inserting or deleting data.
let hashSet : HashSet<string> = new HashSet();
for(let i = 0;i < 10; i++) {
  hashSet.add("sparrow" + i);
}
for(let i = 0;i < 10; i++) {
  hashSet.remove("sparrow" + i);
}
```

### [Symbol.iterator]

[Symbol.iterator]\(): IterableIterator&lt;T&gt;

Obtains an iterator, each item of which is a JavaScript object.

**Atomic service API**: This API can be used in atomic services since API version 12.

**System capability**: SystemCapability.Utils.Lang

> **NOTE**
>
> This API cannot be used in .ets files.

**Return value**

| Type | Description |
| -------- | -------- |
| IterableIterator&lt;T&gt; | Iterator obtained. |

**Error codes**

For details about the error codes, see [Utils Error Codes](errorcode-utils.md).

| ID | Error Message |
| -------- | -------- |
| 10200011 | The Symbol.iterator method cannot be bound. |

**Example**

```ts
let hashSet: HashSet<string> = new HashSet();
hashSet.add("squirrel");
hashSet.add("sparrow");

// Method 1:
let val: Array<string> = Array.from(hashSet.values())
for (let item of val) {
  console.log("value: " + item);
}

// Method 2:
let iter = hashSet[Symbol.iterator]();
let temp: IteratorResult<string> = iter.next();
while(!temp.done) {
  console.log("value: " + temp.value);
  temp = iter.next();
}
```
```ts
// You are not advised to use the set or remove APIs in Symbol.iterator because they may cause unpredictable risks such as infinite loops. You can use the for loop when inserting or deleting data.
let hashSet : HashSet<string> = new HashSet();
for(let i = 0;i < 10;i++) {
  hashSet.add("sparrow" + i);
}
for(let i = 0;i < 10;i++) {
  hashSet.remove("sparrow" + i);
}
```
