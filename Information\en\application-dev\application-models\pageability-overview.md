# PageAbility Overview


PageAbility is an application component that has the UI and supports user interaction.


When you create a PageAbility in DevEco Studio, DevEco Studio automatically creates template code. The capabilities related to the PageAbility are implemented through the **featureAbility** class, and the lifecycle callbacks are implemented through the callbacks in **app.js** or **app.ets**.
