# OH_NativeBuffer_Smpte2086


## Overview

The OH_NativeBuffer_Smpte2086 struct describes the SMPTE ST 2086 static metadata.

**System capability**: SystemCapability.Graphic.Graphic2D.NativeBuffer

**Since**: 12

**Related module**: [OH_NativeBuffer](_o_h___native_buffer.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| [OH_NativeBuffer_ColorXY](_o_h___native_buffer___color_x_y.md) [displaPrimaryRed](#displaprimaryred) | Red primary color.| 
| [OH_NativeBuffer_ColorXY](_o_h___native_buffer___color_x_y.md) [displaPrimaryGreen](#displaprimarygreen) | Green primary color.| 
| [OH_NativeBuffer_ColorXY](_o_h___native_buffer___color_x_y.md) [displaPrimaryBlue](#displaprimaryblue) | Blue primary color.| 
| [OH_NativeBuffer_ColorXY](_o_h___native_buffer___color_x_y.md) [whitePoint](#whitepoint) | White point.| 
| float [maxLuminance](#maxluminance) | Maximum luminance.| 
| float [minLuminance](#minluminance) | Minimum luminance.| 


## Member Variable Description


### displaPrimaryBlue

```
OH_NativeBuffer_ColorXY OH_NativeBuffer_Smpte2086::displaPrimaryBlue
```

**Description**

Blue primary color.


### displaPrimaryGreen

```
OH_NativeBuffer_ColorXY OH_NativeBuffer_Smpte2086::displaPrimaryGreen
```

**Description**

Green primary color.


### displaPrimaryRed

```
OH_NativeBuffer_ColorXY OH_NativeBuffer_Smpte2086::displaPrimaryRed
```

**Description**

Red primary color.


### maxLuminance

```
float OH_NativeBuffer_Smpte2086::maxLuminance
```

**Description**

Maximum luminance.


### minLuminance

```
float OH_NativeBuffer_Smpte2086::minLuminance
```

**Description**

Minimum luminance.


### whitePoint

```
OH_NativeBuffer_ColorXY OH_NativeBuffer_Smpte2086::whitePoint
```

**Description**

White point.
