[{"symbol_name": "AudioR<PERSON><PERSON>", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides audio playback APIs. @typedef AudioRenderer @syscap SystemCapability.Multimedia.Audio.Renderer @crossplatform @since 12", "import_statement": "import { audio.AudioRenderer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioRenderer", "score": 0.6183092}, {"symbol_name": "AudioStreamInfo", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Describes audio stream information. @typedef AudioStreamInfo @syscap SystemCapability.Multimedia.Audio.Core @crossplatform @since 12", "import_statement": "import { audio.AudioStreamInfo } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioStreamInfo", "score": 0.6171435}, {"symbol_name": "AudioCapturer", "symbol_type": "interface", "module_name": "@ohos.multimedia.audio", "is_default": false, "is_ets": false, "description": "Provides APIs for audio recording. @typedef AudioCapturer @syscap SystemCapability.Multimedia.Audio.Capturer @crossplatform @since 12", "import_statement": "import { audio.AudioCapturer } from '@ohos.multimedia.audio';", "parent_symbol": "audio", "is_nested": true, "full_name": "audio.AudioCapturer", "score": 0.60543317}]