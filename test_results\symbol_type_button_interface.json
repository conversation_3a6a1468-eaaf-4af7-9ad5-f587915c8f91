[{"symbol_name": "ButtonOptions", "symbol_type": "interface", "module_name": "button", "is_default": false, "is_ets": false, "description": "Defines the button options. @interface ButtonOptions @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @form @atomicservice @since 11", "import_statement": "import { ButtonOptions } from 'button';", "parent_symbol": null, "is_nested": false, "score": 0.65621006}, {"symbol_name": "AlertDialogButtonOptions", "symbol_type": "interface", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Base button param used for AlertDialogParamWithOptions. @interface AlertDialogButtonOptions @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { AlertDialogButtonOptions } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.61015946}, {"symbol_name": "AlertDialogParamWithButtons", "symbol_type": "interface", "module_name": "alert_dialog", "is_default": false, "is_ets": false, "description": "Defines the dialog param with buttons. @interface AlertDialogParamWithButtons @syscap SystemCapability.ArkUI.ArkUI.Full @crossplatform @atomicservice @since 11", "import_statement": "import { AlertDialogParamWithButtons } from 'alert_dialog';", "parent_symbol": null, "is_nested": false, "score": 0.60772574}]