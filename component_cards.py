"""
ArkTS Import Estimator

This module provides functionality to extract import statements for ArkTS programming language
by analyzing source code and documentation.

It supports all ArkTS features including:
- Nested imports
- Nested classes
- Nested namespaces
- Reimport
- Reexport
- Type declarations

The system uses Qdrant for vector and hybrid search capabilities.
"""

import os
import re
import logging
import uuid
import time
import requests
import asyncio
import concurrent.futures
from typing import List, Dict, Any

# Import configuration
try:
    import config
except ImportError:
    # Fallback configuration if config module is not available
    class Config:
        QDRANT_URL = "http://gmktec.ai-institute.uk:6333"
        COLLECTION_NAME = "arkts_imports"
        OLLAMA_URL = "http://lgpu2.ai-institute.uk:11434"
        EMBEDDING_MODEL = "mxbai-embed-large"
        VECTOR_SIZES = {
            "mxbai-embed-large": 1024,
            "nomic-embed-text": 768,
            "all-minilm": 384
        }
        DEFAULT_VECTOR_SIZE = 1024
        MAX_RETRIES = 3
        RETRY_DELAY = 1
        REQUEST_TIMEOUT = 30
    config = Config()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("ArkTSImportEstimator")

class ArkTSSymbolParser:
    """Parser for ArkTS symbols from d.ts and d.ets files."""

    def __init__(self):
        """Initialize the parser."""
        # Regular expressions for parsing
        self.class_pattern = re.compile(r'(export\s+(?:default\s+)?class|declare\s+class)\s+(\w+)')
        self.interface_pattern = re.compile(r'(export\s+(?:default\s+)?interface|declare\s+interface)\s+(\w+)')
        self.type_pattern = re.compile(r'(export\s+(?:default\s+)?type|declare\s+type)\s+(\w+)')
        self.function_pattern = re.compile(r'(export\s+(?:default\s+)?function)\s+(\w+)')
        self.namespace_pattern = re.compile(r'(export\s+(?:default\s+)?namespace|declare\s+namespace)\s+(\w+)')
        self.enum_pattern = re.compile(r'(export\s+(?:default\s+)?enum|declare\s+enum)\s+(\w+)')
        # Match component patterns (simplified to avoid catastrophic backtracking)
        self.component_pattern = re.compile(r'@Component\s+(?:export\s+)?struct\s+(\w+)')
        self.component_multiline_pattern = re.compile(r'@Component\s*\n\s*export\s+declare\s+struct\s+(\w+)')
        self.const_pattern = re.compile(r'export\s+const\s+(\w+)')

        # Import patterns
        self.named_import_pattern = re.compile(r'import\s+\{\s*([^}]+)\s*\}\s+from\s+[\'"]([^\'"]+)[\'"]')
        self.default_import_pattern = re.compile(r'import\s+([^\s{},]+)\s+from\s+[\'"]([^\'"]+)[\'"]')
        self.namespace_import_pattern = re.compile(r'import\s+\*\s+as\s+([^\s]+)\s+from\s+[\'"]([^\'"]+)[\'"]')
        self.type_import_pattern = re.compile(r'import\s+type\s+\{\s*([^}]+)\s*\}\s+from\s+[\'"]([^\'"]+)[\'"]')
        self.type_namespace_import_pattern = re.compile(r'import\s+type\s+\*\s+as\s+([^\s]+)\s+from\s+[\'"]([^\'"]+)[\'"]')

        # Export patterns
        self.named_export_pattern = re.compile(r'export\s+\{\s*([^}]+)\s*\}')
        self.reexport_pattern = re.compile(r'export\s+\{\s*([^}]+)\s*\}\s+from\s+[\'"]([^\'"]+)[\'"]')
        self.all_reexport_pattern = re.compile(r'export\s+\*\s+from\s+[\'"]([^\'"]+)[\'"]')
        self.type_export_pattern = re.compile(r'export\s+type\s+\{\s*([^}]+)\s*\}')
        self.type_reexport_pattern = re.compile(r'export\s+type\s+\{\s*([^}]+)\s*\}\s+from\s+[\'"]([^\'"]+)[\'"]')

        # Nested structure patterns
        self.nested_class_pattern = re.compile(r'class\s+(\w+)')
        self.nested_interface_pattern = re.compile(r'interface\s+(\w+)')
        self.nested_namespace_start_pattern = re.compile(r'namespace\s+(\w+)\s*\{')
        self.nested_namespace_end_pattern = re.compile(r'\}')

        # Advanced ArkTS patterns
        self.method_pattern = re.compile(r'(\w+)\s*\(\s*([^)]*)\s*\)\s*:\s*([^;{]+)')
        self.property_pattern = re.compile(r'(readonly|private|public|protected)?\s*(\w+)\??\s*:\s*([^;=]+)')
        self.generic_pattern = re.compile(r'(interface|class|type)\s+(\w+)<([^>]+)>')
        self.decorator_pattern = re.compile(r'@(\w+)(?:\([^)]*\))?')
        self.callback_type_pattern = re.compile(r'type\s+(\w+)\s*=\s*\([^)]*\)\s*=>\s*\w+')
        self.union_type_pattern = re.compile(r'type\s+(\w+)\s*=\s*([^;]+\|[^;]+)')
        self.intersection_type_pattern = re.compile(r'type\s+(\w+)\s*=\s*([^;]+&[^;]+)')
        self.export_assignment_pattern = re.compile(r'export\s*=\s*(\w+)|export\s+default\s+(\w+)')

    def extract_module_name(self, file_path: str) -> str:
        """Extract module name from file path.

        Args:
            file_path: Path to the d.ts or d.ets file

        Returns:
            Module name
        """
        # Get file name
        file_name = os.path.basename(file_path)

        # Remove extension
        module_name = file_name.replace('.d.ts', '').replace('.d.ets', '')

        # If file name doesn't start with @, check directory structure
        if not module_name.startswith('@'):
            dir_path = os.path.dirname(file_path)
            parts = dir_path.split(os.path.sep)

            # Find directory starting with @ and build proper module path
            scope_found = False
            for part in reversed(parts):
                if part.startswith('@'):
                    # Use proper ArkTS module naming: @scope/package format
                    if '/' in part or '\\' in part:
                        # Already has package name
                        module_name = f"{part}.{module_name}"
                    else:
                        # Just scope, need to find package
                        scope_index = parts.index(part)
                        if scope_index + 1 < len(parts):
                            package = parts[scope_index + 1]
                            module_name = f"{part}/{package}.{module_name}"
                        else:
                            module_name = f"{part}.{module_name}"
                    scope_found = True
                    break

            # If no @ scope found, use directory structure
            if not scope_found and len(parts) > 0:
                # Use last directory as module prefix
                last_dir = parts[-1] if parts[-1] else (parts[-2] if len(parts) > 1 else '')
                if last_dir:
                    module_name = f"{last_dir}.{module_name}"

        return module_name

    def extract_jsdoc(self, content: str, start_pos: int) -> str:
        """Extract JSDoc comment before a symbol.

        Args:
            content: File content
            start_pos: Start position of the symbol

        Returns:
            JSDoc comment
        """
        # Find JSDoc comment before the symbol
        comment_end = start_pos
        comment_start = content.rfind('/**', 0, comment_end)

        if comment_start != -1:
            comment = content[comment_start:comment_end]
            # Clean JSDoc
            lines = comment.split('\n')
            clean_lines = []
            for line in lines:
                line = line.strip()
                line = line.replace('/**', '').replace('*/', '').replace('*', '').strip()
                if line:
                    clean_lines.append(line)

            return ' '.join(clean_lines)

        return ""

    def generate_import_statement(self, module_name: str, symbol_name: str, is_default: bool = False, is_type: bool = False) -> str:
        """Generate import statement.

        Args:
            module_name: Module name
            symbol_name: Symbol name
            is_default: Whether the symbol is a default export
            is_type: Whether the symbol is a type import

        Returns:
            Import statement
        """
        if is_type:
            if is_default:
                return f"import type {symbol_name} from '{module_name}';"
            else:
                return f"import type {{ {symbol_name} }} from '{module_name}';"
        else:
            if is_default:
                return f"import {symbol_name} from '{module_name}';"
            else:
                return f"import {{ {symbol_name} }} from '{module_name}';"

    def parse_file(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse a d.ts or d.ets file and extract symbols.

        Args:
            file_path: Path to the d.ts or d.ets file

        Returns:
            List of symbols
        """
        try:
            # Read file content with fallback encoding
            content = None
            encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252']

            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    break
                except UnicodeDecodeError:
                    continue

            if content is None:
                logger.error(f"Could not decode file {file_path} with any supported encoding")
                return []

            # Determine file type
            is_ets = file_path.endswith('.d.ets')

            # Extract module name
            module_name = self.extract_module_name(file_path)

            # Extract symbols
            symbols = []

            # Extract classes
            for match in self.class_pattern.finditer(content):
                is_default = 'default' in match.group(1)
                class_name = match.group(2)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, class_name, is_default)

                symbols.append({
                    'symbol_name': class_name,
                    'symbol_type': 'class',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

                # Extract nested classes
                self._extract_nested_symbols(content, match.start(), class_name, module_name, is_ets, symbols)

            # Extract interfaces
            for match in self.interface_pattern.finditer(content):
                is_default = 'default' in match.group(1)
                interface_name = match.group(2)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, interface_name, is_default)

                symbols.append({
                    'symbol_name': interface_name,
                    'symbol_type': 'interface',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

                # Extract nested interfaces
                self._extract_nested_symbols(content, match.start(), interface_name, module_name, is_ets, symbols)

            # Extract types
            for match in self.type_pattern.finditer(content):
                is_default = 'default' in match.group(1)
                type_name = match.group(2)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, type_name, is_default, is_type=True)

                symbols.append({
                    'symbol_name': type_name,
                    'symbol_type': 'type',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

            # Extract functions
            for match in self.function_pattern.finditer(content):
                is_default = 'default' in match.group(1)
                function_name = match.group(2)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, function_name, is_default)

                symbols.append({
                    'symbol_name': function_name,
                    'symbol_type': 'function',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

            # Extract namespaces
            for match in self.namespace_pattern.finditer(content):
                is_default = 'default' in match.group(1)
                namespace_name = match.group(2)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, namespace_name, is_default)

                symbols.append({
                    'symbol_name': namespace_name,
                    'symbol_type': 'namespace',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

                # Extract nested namespaces
                self._extract_nested_symbols(content, match.start(), namespace_name, module_name, is_ets, symbols)

            # Extract enums
            for match in self.enum_pattern.finditer(content):
                is_default = 'default' in match.group(1)
                enum_name = match.group(2)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, enum_name, is_default)

                symbols.append({
                    'symbol_name': enum_name,
                    'symbol_type': 'enum',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

            # Extract components (for d.ets files and d.ts files with @Component)
            # Components can be in both .d.ets and .d.ts files in ArkTS
            if is_ets or '@Component' in content:
                # Extract inline components
                for match in self.component_pattern.finditer(content):
                    component_name = match.group(1)

                    if component_name:
                        # Extract JSDoc
                        description = self.extract_jsdoc(content, match.start())

                        # Generate import statement
                        import_statement = self.generate_import_statement(module_name, component_name, False)

                        symbols.append({
                            'symbol_name': component_name,
                            'symbol_type': 'component',
                            'module_name': module_name,
                            'is_default': False,
                            'is_ets': is_ets,
                            'description': description,
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })

                # Extract multiline components
                for match in self.component_multiline_pattern.finditer(content):
                    component_name = match.group(1)

                    if component_name:
                        # Extract JSDoc
                        description = self.extract_jsdoc(content, match.start())

                        # Generate import statement
                        import_statement = self.generate_import_statement(module_name, component_name, False)

                        symbols.append({
                            'symbol_name': component_name,
                            'symbol_type': 'component',
                            'module_name': module_name,
                            'is_default': False,
                            'is_ets': is_ets,
                            'description': description,
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })

            # Extract constants
            for match in self.const_pattern.finditer(content):
                const_name = match.group(1)

                # Extract JSDoc
                description = self.extract_jsdoc(content, match.start())

                # Generate import statement
                import_statement = self.generate_import_statement(module_name, const_name, False)

                symbols.append({
                    'symbol_name': const_name,
                    'symbol_type': 'const',
                    'module_name': module_name,
                    'is_default': False,
                    'is_ets': is_ets,
                    'description': description,
                    'import_statement': import_statement,
                    'parent_symbol': None,
                    'is_nested': False
                })

            # Extract reexports
            self._extract_reexports(content, module_name, is_ets, symbols)

            # Extract advanced ArkTS patterns
            self._extract_advanced_patterns(content, module_name, is_ets, symbols)

            return symbols

        except Exception as e:
            logger.error(f"Error parsing file {file_path}: {str(e)}")
            return []

    def _extract_nested_symbols(self, content: str, start_pos: int, parent_name: str, module_name: str, is_ets: bool, symbols: List[Dict[str, Any]]) -> None:
        """Extract nested symbols from a parent symbol.

        Args:
            content: File content
            start_pos: Start position of the parent symbol
            parent_name: Parent symbol name
            module_name: Module name
            is_ets: Whether the file is a d.ets file
            symbols: List to append symbols to
        """
        # Find opening brace
        brace_pos = content.find('{', start_pos)
        if brace_pos == -1:
            return

        # Find matching closing brace
        brace_count = 1
        pos = brace_pos + 1
        end_pos = -1

        while pos < len(content) and brace_count > 0:
            if content[pos] == '{':
                brace_count += 1
            elif content[pos] == '}':
                brace_count -= 1
                if brace_count == 0:
                    end_pos = pos
                    break
            pos += 1

        if end_pos == -1:
            return

        # Extract content within braces
        nested_content = content[brace_pos+1:end_pos]

        # Extract nested classes
        for match in self.nested_class_pattern.finditer(nested_content):
            class_name = match.group(1)

            # Extract JSDoc
            description = self.extract_jsdoc(nested_content, match.start())

            # Generate import statement
            import_statement = self.generate_import_statement(module_name, f"{parent_name}.{class_name}", False)

            symbols.append({
                'symbol_name': class_name,
                'symbol_type': 'class',
                'module_name': module_name,
                'is_default': False,
                'is_ets': is_ets,
                'description': description,
                'import_statement': import_statement,
                'parent_symbol': parent_name,
                'is_nested': True,
                'full_name': f"{parent_name}.{class_name}"
            })

        # Extract nested interfaces
        for match in self.nested_interface_pattern.finditer(nested_content):
            interface_name = match.group(1)

            # Extract JSDoc
            description = self.extract_jsdoc(nested_content, match.start())

            # Generate import statement
            import_statement = self.generate_import_statement(module_name, f"{parent_name}.{interface_name}", False)

            symbols.append({
                'symbol_name': interface_name,
                'symbol_type': 'interface',
                'module_name': module_name,
                'is_default': False,
                'is_ets': is_ets,
                'description': description,
                'import_statement': import_statement,
                'parent_symbol': parent_name,
                'is_nested': True,
                'full_name': f"{parent_name}.{interface_name}"
            })

        # Extract nested namespaces
        namespace_starts = list(self.nested_namespace_start_pattern.finditer(nested_content))
        for i, match in enumerate(namespace_starts):
            namespace_name = match.group(1)
            start = match.end()

            # Find end of namespace
            brace_count = 1
            end = start
            while end < len(nested_content) and brace_count > 0:
                if nested_content[end] == '{':
                    brace_count += 1
                elif nested_content[end] == '}':
                    brace_count -= 1
                end += 1

            # Extract JSDoc
            description = self.extract_jsdoc(nested_content, match.start())

            # Generate import statement
            import_statement = self.generate_import_statement(module_name, f"{parent_name}.{namespace_name}", False)

            symbols.append({
                'symbol_name': namespace_name,
                'symbol_type': 'namespace',
                'module_name': module_name,
                'is_default': False,
                'is_ets': is_ets,
                'description': description,
                'import_statement': import_statement,
                'parent_symbol': parent_name,
                'is_nested': True,
                'full_name': f"{parent_name}.{namespace_name}"
            })

            # Extract nested symbols from namespace (with depth limit to prevent infinite recursion)
            namespace_content = nested_content[start:end-1]
            # Only recurse if we haven't gone too deep (prevent infinite recursion)
            if parent_name.count('.') < 5:  # Max depth of 5 levels
                self._extract_nested_symbols(namespace_content, 0, f"{parent_name}.{namespace_name}", module_name, is_ets, symbols)
            else:
                logger.warning(f"Maximum nesting depth reached for {parent_name}.{namespace_name}, skipping further nesting")

    def _extract_reexports(self, content: str, module_name: str, is_ets: bool, symbols: List[Dict[str, Any]]) -> None:
        """Extract reexports from file content.

        Args:
            content: File content
            module_name: Module name
            is_ets: Whether the file is a d.ets file
            symbols: List to append symbols to
        """
        # Extract named reexports
        for match in self.reexport_pattern.finditer(content):
            exported_items = [item.strip() for item in match.group(1).split(',')]
            source_module = match.group(2)

            for item in exported_items:
                # Check for alias
                if ' as ' in item:
                    original, alias = item.split(' as ')
                    original = original.strip()
                    alias = alias.strip()

                    # Check for default export
                    if original == 'default':
                        # Default export reexported as named export
                        import_statement = self.generate_import_statement(module_name, alias, False)

                        symbols.append({
                            'symbol_name': alias,
                            'symbol_type': 'reexport',
                            'module_name': module_name,
                            'original_module': source_module,
                            'original_name': 'default',
                            'is_default': False,
                            'is_ets': is_ets,
                            'description': f"Reexported from {source_module}",
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })
                    elif alias == 'default':
                        # Named export reexported as default export
                        import_statement = self.generate_import_statement(module_name, original, True)

                        symbols.append({
                            'symbol_name': original,
                            'symbol_type': 'reexport',
                            'module_name': module_name,
                            'original_module': source_module,
                            'original_name': original,
                            'is_default': True,
                            'is_ets': is_ets,
                            'description': f"Reexported from {source_module}",
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })
                    else:
                        # Named export reexported with alias
                        import_statement = self.generate_import_statement(module_name, alias, False)

                        symbols.append({
                            'symbol_name': alias,
                            'symbol_type': 'reexport',
                            'module_name': module_name,
                            'original_module': source_module,
                            'original_name': original,
                            'is_default': False,
                            'is_ets': is_ets,
                            'description': f"Reexported from {source_module}",
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })
                else:
                    # Simple reexport
                    if item == 'default':
                        # Default export reexported as default export
                        import_statement = self.generate_import_statement(module_name, 'default', True)

                        symbols.append({
                            'symbol_name': 'default',
                            'symbol_type': 'reexport',
                            'module_name': module_name,
                            'original_module': source_module,
                            'original_name': 'default',
                            'is_default': True,
                            'is_ets': is_ets,
                            'description': f"Reexported from {source_module}",
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })
                    else:
                        # Named export reexported as named export
                        import_statement = self.generate_import_statement(module_name, item, False)

                        symbols.append({
                            'symbol_name': item,
                            'symbol_type': 'reexport',
                            'module_name': module_name,
                            'original_module': source_module,
                            'original_name': item,
                            'is_default': False,
                            'is_ets': is_ets,
                            'description': f"Reexported from {source_module}",
                            'import_statement': import_statement,
                            'parent_symbol': None,
                            'is_nested': False
                        })

        # Extract all reexports
        for match in self.all_reexport_pattern.finditer(content):
            source_module = match.group(1)

            # Add a special entry for * reexport
            symbols.append({
                'symbol_name': '*',
                'symbol_type': 'reexport_all',
                'module_name': module_name,
                'original_module': source_module,
                'is_default': False,
                'is_ets': is_ets,
                'description': f"All exports reexported from {source_module}",
                'import_statement': f"import * from '{module_name}';",
                'parent_symbol': None,
                'is_nested': False
            })

        # Extract type reexports
        for match in self.type_reexport_pattern.finditer(content):
            exported_items = [item.strip() for item in match.group(1).split(',')]
            source_module = match.group(2)

            for item in exported_items:
                # Check for alias
                if ' as ' in item:
                    original, alias = item.split(' as ')
                    original = original.strip()
                    alias = alias.strip()

                    # Type export reexported with alias
                    import_statement = self.generate_import_statement(module_name, alias, False, is_type=True)

                    symbols.append({
                        'symbol_name': alias,
                        'symbol_type': 'type_reexport',
                        'module_name': module_name,
                        'original_module': source_module,
                        'original_name': original,
                        'is_default': False,
                        'is_ets': is_ets,
                        'description': f"Type reexported from {source_module}",
                        'import_statement': import_statement,
                        'parent_symbol': None,
                        'is_nested': False
                    })
                else:
                    # Simple type reexport
                    import_statement = self.generate_import_statement(module_name, item, False, is_type=True)

                    symbols.append({
                        'symbol_name': item,
                        'symbol_type': 'type_reexport',
                        'module_name': module_name,
                        'original_module': source_module,
                        'original_name': item,
                        'is_default': False,
                        'is_ets': is_ets,
                        'description': f"Type reexported from {source_module}",
                        'import_statement': import_statement,
                        'parent_symbol': None,
                        'is_nested': False
                    })

    def _extract_advanced_patterns(self, content: str, module_name: str, is_ets: bool, symbols: List[Dict[str, Any]]) -> None:
        """Extract advanced ArkTS patterns like methods, properties, generics, etc.

        Args:
            content: File content
            module_name: Module name
            is_ets: Whether the file is a d.ets file
            symbols: List to append symbols to
        """
        # Extract decorators (beyond @Component)
        for match in self.decorator_pattern.finditer(content):
            decorator_name = match.group(1)

            # Skip @Component as it's handled separately
            if decorator_name != 'Component':
                symbols.append({
                    'symbol_name': decorator_name,
                    'symbol_type': 'decorator',
                    'module_name': module_name,
                    'is_default': False,
                    'is_ets': is_ets,
                    'description': f"ArkTS decorator @{decorator_name}",
                    'import_statement': f"// Decorator: @{decorator_name}",
                    'parent_symbol': None,
                    'is_nested': False
                })

        # Extract callback types
        for match in self.callback_type_pattern.finditer(content):
            callback_name = match.group(1)

            symbols.append({
                'symbol_name': callback_name,
                'symbol_type': 'callback_type',
                'module_name': module_name,
                'is_default': False,
                'is_ets': is_ets,
                'description': f"Callback type definition",
                'import_statement': self.generate_import_statement(module_name, callback_name, False, is_type=True),
                'parent_symbol': None,
                'is_nested': False
            })

        # Extract union types (enhanced)
        for match in self.union_type_pattern.finditer(content):
            union_name = match.group(1)
            union_definition = match.group(2)

            symbols.append({
                'symbol_name': union_name,
                'symbol_type': 'union_type',
                'module_name': module_name,
                'is_default': False,
                'is_ets': is_ets,
                'description': f"Union type: {union_definition}",
                'import_statement': self.generate_import_statement(module_name, union_name, False, is_type=True),
                'parent_symbol': None,
                'is_nested': False
            })

        # Extract intersection types
        for match in self.intersection_type_pattern.finditer(content):
            intersection_name = match.group(1)
            intersection_definition = match.group(2)

            symbols.append({
                'symbol_name': intersection_name,
                'symbol_type': 'intersection_type',
                'module_name': module_name,
                'is_default': False,
                'is_ets': is_ets,
                'description': f"Intersection type: {intersection_definition}",
                'import_statement': self.generate_import_statement(module_name, intersection_name, False, is_type=True),
                'parent_symbol': None,
                'is_nested': False
            })

        # Extract export assignments
        for match in self.export_assignment_pattern.finditer(content):
            export_name = match.group(1) or match.group(2)
            is_default = match.group(2) is not None

            if export_name:
                symbols.append({
                    'symbol_name': export_name,
                    'symbol_type': 'export_assignment',
                    'module_name': module_name,
                    'is_default': is_default,
                    'is_ets': is_ets,
                    'description': f"Export assignment",
                    'import_statement': self.generate_import_statement(module_name, export_name, is_default),
                    'parent_symbol': None,
                    'is_nested': False
                })


class ArkTSUtilities:
    """Utility functions for ArkTS Import Estimator."""

    @staticmethod
    def generate_unique_id(symbol_name: str, module_name: str, parent_symbol: str = None) -> str:
        """Generate a unique ID for a symbol using UUID.

        Args:
            symbol_name: Name of the symbol
            module_name: Module name
            parent_symbol: Parent symbol name (optional)

        Returns:
            Unique UUID string (compatible with Qdrant)
        """
        # Create a deterministic UUID based on symbol information
        # This ensures the same symbol always gets the same ID
        id_str = f"{symbol_name}:{module_name}:{parent_symbol or ''}"
        # Use UUID5 with a namespace for deterministic UUIDs
        namespace = uuid.UUID('6ba7b810-9dad-11d1-80b4-00c04fd430c8')  # Standard namespace
        generated_uuid = uuid.uuid5(namespace, id_str)
        # Return as string for Qdrant compatibility
        return str(generated_uuid)

    @staticmethod
    def convert_uuid_to_int(uuid_str: str) -> int:
        """Convert UUID string to integer for Qdrant compatibility.

        Args:
            uuid_str: UUID string

        Returns:
            Integer representation of UUID
        """
        try:
            # Convert UUID to integer for Qdrant compatibility
            uuid_obj = uuid.UUID(uuid_str)
            return uuid_obj.int % (2**63 - 1)  # Ensure it fits in 64-bit signed integer
        except Exception:
            # Fallback to hash if UUID parsing fails
            return abs(hash(uuid_str)) % (2**63 - 1)

    @staticmethod
    def get_vector_size(embedding_model: str) -> int:
        """Get vector size for embedding model from config.

        Args:
            embedding_model: Name of the embedding model

        Returns:
            Vector size for the model
        """
        return config.VECTOR_SIZES.get(embedding_model, config.DEFAULT_VECTOR_SIZE)

    @staticmethod
    def validate_vector_size(vector: List[float], expected_size: int) -> List[float]:
        """Validate and fix vector size if needed.

        Args:
            vector: Input vector
            expected_size: Expected vector size

        Returns:
            Vector with correct size
        """
        if len(vector) == expected_size:
            return vector
        elif len(vector) > expected_size:
            logger.warning(f"Truncating vector from {len(vector)} to {expected_size}")
            return vector[:expected_size]
        else:
            logger.warning(f"Padding vector from {len(vector)} to {expected_size}")
            return vector + [0.0] * (expected_size - len(vector))


class ArkTSEmbeddingManager:
    """Manages embedding generation with retry logic and error handling."""

    def __init__(self, ollama_url: str = None, embedding_model: str = None):
        """Initialize the embedding manager.

        Args:
            ollama_url: URL of the Ollama server
            embedding_model: Name of the embedding model
        """
        self.ollama_url = ollama_url or config.OLLAMA_URL
        self.embedding_model = embedding_model or config.EMBEDDING_MODEL
        self.vector_size = ArkTSUtilities.get_vector_size(self.embedding_model)

    def get_embedding(self, text: str) -> List[float]:
        """Get embedding from Ollama with retry logic.

        Args:
            text: Text to embed

        Returns:
            Embedding vector as a list of floats
        """
        max_retries = config.MAX_RETRIES
        retry_delay = config.RETRY_DELAY

        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{self.ollama_url}/api/embeddings",
                    json={
                        "model": self.embedding_model,
                        "prompt": text
                    },
                    timeout=config.REQUEST_TIMEOUT
                )

                if response.status_code == 200:
                    embedding = response.json().get("embedding", [])
                    return ArkTSUtilities.validate_vector_size(embedding, self.vector_size)
                else:
                    logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {response.status_code} {response.text}")

            except requests.exceptions.ConnectionError:
                logger.error(f"Connection error to Ollama server (attempt {attempt+1}/{max_retries}). Is Ollama running?")
            except requests.exceptions.Timeout:
                logger.error(f"Timeout connecting to Ollama server (attempt {attempt+1}/{max_retries})")
            except Exception as e:
                logger.error(f"Error getting embedding (attempt {attempt+1}/{max_retries}): {str(e)}")

            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                retry_delay *= 2  # Exponential backoff

        logger.error(f"All {max_retries} attempts to get embedding failed. Raising exception.")
        raise Exception(f"Failed to get embedding after {max_retries} attempts")


class ArkTSCollectionManager:
    """Manages Qdrant collection operations safely."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None, qdrant_client=None):
        """Initialize the collection manager.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the collection
            qdrant_client: Optional shared Qdrant client instance
        """
        self.qdrant_url = qdrant_url or config.QDRANT_URL
        self.collection_name = collection_name or config.COLLECTION_NAME
        self._shared_client = qdrant_client

    def ensure_collection_exists(self, vector_size: int, force_recreate: bool = False) -> bool:
        """Ensure collection exists with proper configuration.

        Args:
            vector_size: Size of vectors for the collection
            force_recreate: Whether to force recreation of existing collection

        Returns:
            True if successful, False otherwise
        """
        try:
            from qdrant_client.http import models

            # Use shared client if available, otherwise create new one
            if self._shared_client:
                client = self._shared_client
            else:
                from qdrant_client import QdrantClient
                client = QdrantClient(url=self.qdrant_url, check_compatibility=False)

            # Check if collection exists
            collections = client.get_collections().collections
            collection_names = [collection.name for collection in collections]

            if self.collection_name in collection_names:
                if force_recreate:
                    logger.info(f"Force recreating collection {self.collection_name}")
                    client.delete_collection(collection_name=self.collection_name)
                else:
                    logger.info(f"Collection {self.collection_name} already exists")
                    return True

            # Create collection
            logger.info(f"Creating collection {self.collection_name} with vector size {vector_size}")
            client.create_collection(
                collection_name=self.collection_name,
                vectors_config=models.VectorParams(
                    size=vector_size,
                    distance=models.Distance.COSINE
                )
            )

            # Create payload indices for better search performance
            try:
                index_fields = ["symbol_name", "symbol_type", "module_name", "parent_symbol"]
                for field in index_fields:
                    try:
                        client.create_payload_index(
                            collection_name=self.collection_name,
                            field_name=field
                        )
                    except Exception as e:
                        logger.warning(f"Could not create index for field {field}: {str(e)}")
            except Exception as e:
                logger.warning(f"Could not create payload indices: {str(e)}")

            logger.info(f"Successfully created collection {self.collection_name}")
            return True

        except Exception as e:
            logger.error(f"Error ensuring collection exists: {str(e)}")
            return False


class ArkTSAsyncManager:
    """Manages async operations safely."""

    def __init__(self, default_timeout: float = 60.0):
        """Initialize the async manager.

        Args:
            default_timeout: Default timeout for async operations
        """
        self.default_timeout = default_timeout

    def run_in_new_loop(self, coro, timeout: float = None):
        """Run a coroutine in a new event loop.

        Args:
            coro: Coroutine to run
            timeout: Timeout for the operation

        Returns:
            Result of the coroutine
        """
        timeout = timeout or self.default_timeout

        try:
            # Try to get the current event loop
            try:
                asyncio.get_running_loop()
                # If we're already in an event loop, use a thread pool
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(self._run_in_thread, coro, timeout)
                    return future.result(timeout=timeout)
            except RuntimeError:
                # No event loop running, create a new one
                return asyncio.run(asyncio.wait_for(coro, timeout=timeout))

        except asyncio.TimeoutError:
            logger.error(f"Async operation timed out after {timeout} seconds")
            raise
        except Exception as e:
            logger.error(f"Error in async operation: {str(e)}")
            raise

    def _run_in_thread(self, coro, timeout: float):
        """Run coroutine in a separate thread with its own event loop.

        Args:
            coro: Coroutine to run
            timeout: Timeout for the operation

        Returns:
            Result of the coroutine
        """
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(asyncio.wait_for(coro, timeout=timeout))
        finally:
            loop.close()


class ArkTSEnhancedParser(ArkTSSymbolParser):
    """Enhanced ArkTS Symbol Parser with improved utilities and error handling."""

    def __init__(self, embedding_manager: ArkTSEmbeddingManager = None):
        """Initialize the enhanced parser.

        Args:
            embedding_manager: Optional embedding manager instance
        """
        super().__init__()
        self.embedding_manager = embedding_manager  # Don't create default to avoid circular dependency
        self.utilities = ArkTSUtilities()

    def parse_file_with_embeddings(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse file and generate embeddings for symbols.

        Args:
            file_path: Path to the file to parse

        Returns:
            List of symbols with embeddings
        """
        try:
            # Parse symbols using parent method
            symbols = self.parse_file(file_path)

            # Add embeddings and unique IDs
            valid_symbols = []
            for symbol in symbols:
                try:
                    # Generate unique ID
                    symbol['id'] = self.utilities.generate_unique_id(
                        symbol['symbol_name'],
                        symbol['module_name'],
                        symbol.get('parent_symbol')
                    )

                    # Generate embedding (only if embedding manager is available)
                    if self.embedding_manager:
                        text_to_embed = self._create_embedding_text(symbol)
                        symbol['embedding'] = self.embedding_manager.get_embedding(text_to_embed)
                    else:
                        logger.warning(f"No embedding manager available for symbol {symbol['symbol_name']}")
                        continue

                    # Only add symbols with successful embeddings
                    if 'embedding' in symbol and symbol['embedding'] and len(symbol['embedding']) > 0:
                        valid_symbols.append(symbol)
                    else:
                        logger.warning(f"Skipping symbol {symbol['symbol_name']} - empty or missing embedding")

                except Exception as e:
                    logger.error(f"Error processing symbol {symbol.get('symbol_name', 'unknown')}: {str(e)}")
                    # Skip symbols that fail embedding generation
                    continue

            return valid_symbols

        except Exception as e:
            logger.error(f"Error parsing file with embeddings {file_path}: {str(e)}")
            return []

    def _create_embedding_text(self, symbol: Dict[str, Any]) -> str:
        """Create text for embedding generation.

        Args:
            symbol: Symbol dictionary

        Returns:
            Text to embed
        """
        # Safely get values with defaults
        symbol_name = symbol.get('symbol_name', '')
        symbol_type = symbol.get('symbol_type', '')
        description = symbol.get('description', '')

        if 'full_name' in symbol and symbol['full_name']:
            full_name = symbol['full_name']
            return f"{full_name} {symbol_type} {description}".strip()
        else:
            return f"{symbol_name} {symbol_type} {description}".strip()

    def validate_symbols(self, symbols: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate and clean symbol data.

        Args:
            symbols: List of symbols to validate

        Returns:
            List of validated symbols
        """
        validated_symbols = []

        for symbol in symbols:
            try:
                # Check required fields (including id and embedding for indexing)
                required_fields = ['symbol_name', 'symbol_type', 'module_name', 'import_statement', 'id', 'embedding']
                if all(field in symbol for field in required_fields) and symbol['embedding']:
                    # Ensure all fields have proper types
                    symbol['is_nested'] = bool(symbol.get('is_nested', False))
                    symbol['is_default'] = bool(symbol.get('is_default', False))
                    symbol['is_ets'] = bool(symbol.get('is_ets', False))
                    symbol['description'] = str(symbol.get('description', ''))

                    validated_symbols.append(symbol)
                else:
                    logger.warning(f"Symbol missing required fields: {symbol}")

            except Exception as e:
                logger.error(f"Error validating symbol {symbol}: {str(e)}")

        return validated_symbols


class ArkTSImportEstimatorFixed:
    """Fixed and improved ArkTS Import Estimator with all issues resolved."""

    def __init__(self, qdrant_url: str = None, collection_name: str = None,
                 ollama_url: str = None, embedding_model: str = None, batch_size: int = 100):
        """Initialize the fixed estimator.

        Args:
            qdrant_url: URL of the Qdrant server
            collection_name: Name of the Qdrant collection
            ollama_url: URL of the Ollama server
            embedding_model: Name of the embedding model
            batch_size: Batch size for indexing operations
        """
        self.qdrant_url = qdrant_url or config.QDRANT_URL
        self.collection_name = collection_name or config.COLLECTION_NAME
        self.ollama_url = ollama_url or config.OLLAMA_URL
        self.embedding_model = embedding_model or config.EMBEDDING_MODEL
        self.batch_size = batch_size

        # Initialize connection pool first
        self._qdrant_client = None

        # Initialize components
        self.embedding_manager = ArkTSEmbeddingManager(self.ollama_url, self.embedding_model)
        self.parser = ArkTSEnhancedParser(self.embedding_manager)
        self.async_manager = ArkTSAsyncManager()
        self.utilities = ArkTSUtilities()

        # Get vector size from config
        self.vector_size = self.utilities.get_vector_size(self.embedding_model)

        # Initialize collection manager with shared client (lazy)
        self.collection_manager = None

        logger.info(f"ArkTSImportEstimatorFixed initialized with:")
        logger.info(f"  Qdrant: {self.qdrant_url}")
        logger.info(f"  Ollama: {self.ollama_url}")
        logger.info(f"  Model: {self.embedding_model}")
        logger.info(f"  Vector size: {self.vector_size}")
        logger.info(f"  Batch size: {self.batch_size}")

    def _get_qdrant_client(self):
        """Get or create Qdrant client with connection pooling.

        Returns:
            Qdrant client instance
        """
        if self._qdrant_client is None:
            try:
                from qdrant_client import QdrantClient
                self._qdrant_client = QdrantClient(url=self.qdrant_url, check_compatibility=False)
            except Exception as e:
                logger.error(f"Failed to create Qdrant client: {str(e)}")
                raise
        return self._qdrant_client

    def _get_collection_manager(self):
        """Get or create collection manager with shared client.

        Returns:
            Collection manager instance
        """
        if self.collection_manager is None:
            client = self._get_qdrant_client()
            self.collection_manager = ArkTSCollectionManager(
                self.qdrant_url, self.collection_name, client
            )
        return self.collection_manager

    def ensure_collection_exists(self, force_recreate: bool = False) -> bool:
        """Ensure collection exists with safe management.

        Args:
            force_recreate: Whether to force recreation

        Returns:
            True if successful
        """
        collection_manager = self._get_collection_manager()
        return collection_manager.ensure_collection_exists(
            self.vector_size, force_recreate
        )

    def close(self):
        """Close connections and cleanup resources."""
        if self._qdrant_client:
            try:
                # Qdrant client doesn't have explicit close method, just set to None
                self._qdrant_client = None
                logger.info("Qdrant client connection closed")
            except Exception as e:
                logger.warning(f"Error closing Qdrant client: {str(e)}")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with cleanup.

        Args:
            exc_type: Exception type (used for exception handling)
            exc_val: Exception value (unused but required by protocol)
            exc_tb: Exception traceback (unused but required by protocol)

        Returns:
            False to propagate exceptions
        """
        try:
            self.close()
        except Exception as cleanup_error:
            logger.error(f"Error during cleanup: {str(cleanup_error)}")
            # If there was already an exception, don't mask it with cleanup error
            if exc_type is None:
                raise cleanup_error

        # Return False to propagate any original exception
        # Note: exc_val and exc_tb are unused but required by context manager protocol
        return False

    def index_file(self, file_path: str) -> int:
        """Index a single file with improved error handling.

        Args:
            file_path: Path to the file to index

        Returns:
            Number of symbols indexed
        """
        try:
            # Parse file with embeddings (already includes validation)
            symbols = self.parser.parse_file_with_embeddings(file_path)

            if not symbols:
                logger.warning(f"No symbols found in {file_path}")
                return 0

            # Index symbols (no need for double validation)
            indexed_count = self._index_symbols(symbols)

            logger.info(f"Indexed {indexed_count} symbols from {file_path}")
            return indexed_count

        except Exception as e:
            logger.error(f"Error indexing file {file_path}: {str(e)}")
            return 0

    def _index_symbols(self, symbols: List[Dict[str, Any]]) -> int:
        """Index symbols in Qdrant with improved error handling and batch processing.

        Args:
            symbols: List of symbols to index

        Returns:
            Number of symbols successfully indexed
        """
        if not symbols:
            return 0

        try:
            from qdrant_client.http import models

            client = self._get_qdrant_client()
            total_indexed = 0

            # Process symbols in batches to avoid memory issues
            for i in range(0, len(symbols), self.batch_size):
                batch = symbols[i:i + self.batch_size]
                logger.info(f"Processing batch {i//self.batch_size + 1}/{(len(symbols) + self.batch_size - 1)//self.batch_size}")

                points = []
                for symbol in batch:
                    try:
                        # Skip symbols without embeddings (consistent with validation)
                        if 'embedding' not in symbol or not symbol['embedding'] or len(symbol['embedding']) == 0:
                            logger.warning(f"Skipping symbol {symbol.get('symbol_name', 'unknown')} - no embedding")
                            continue

                        # Create point with UUID-based ID (try string first, fallback to int)
                        try:
                            point_id = symbol['id']  # Try string UUID first
                            point = models.PointStruct(
                                id=point_id,
                                vector=symbol['embedding'],
                                payload={k: v for k, v in symbol.items() if k not in ['id', 'embedding']}
                            )
                        except Exception:
                            # Fallback to integer ID if string fails
                            point_id = self.utilities.convert_uuid_to_int(symbol['id'])
                            point = models.PointStruct(
                                id=point_id,
                                vector=symbol['embedding'],
                                payload={k: v for k, v in symbol.items() if k not in ['id', 'embedding']}
                            )

                        points.append(point)

                    except Exception as e:
                        logger.error(f"Error creating point for symbol {symbol.get('symbol_name', 'unknown')}: {str(e)}")

                # Batch upsert with retry logic
                if points:
                    try:
                        client.upsert(
                            collection_name=self.collection_name,
                            points=points
                        )
                        total_indexed += len(points)
                        logger.info(f"Successfully indexed {len(points)} symbols in batch")

                    except Exception as e:
                        logger.error(f"Batch upsert failed: {str(e)}. Trying individual upserts.")

                        # Try individual upserts as fallback
                        batch_success = 0
                        for point in points:
                            try:
                                client.upsert(
                                    collection_name=self.collection_name,
                                    points=[point]
                                )
                                batch_success += 1
                            except Exception as e2:
                                logger.error(f"Error upserting point {point.id}: {str(e2)}")

                        total_indexed += batch_success
                        logger.info(f"Indexed {batch_success}/{len(points)} symbols individually")

            return total_indexed

        except Exception as e:
            logger.error(f"Error indexing symbols: {str(e)}")
            return 0

    def index_directory(self, directory_path: str, file_extensions: List[str] = None) -> int:
        """Index all files in a directory.

        Args:
            directory_path: Path to the directory
            file_extensions: List of file extensions to process (default: ['.d.ts', '.d.ets'])

        Returns:
            Total number of symbols indexed
        """
        if file_extensions is None:
            file_extensions = ['.d.ts', '.d.ets']

        total_symbols = 0
        processed_files = 0

        try:
            for root, _, files in os.walk(directory_path):
                for file in files:
                    # Check if file has any of the supported extensions
                    if any(file.endswith(ext) for ext in file_extensions):
                        file_path = os.path.join(root, file)
                        symbols_count = self.index_file(file_path)
                        total_symbols += symbols_count
                        processed_files += 1

            logger.info(f"Indexed {total_symbols} symbols from {processed_files} files in {directory_path}")
            return total_symbols

        except Exception as e:
            logger.error(f"Error indexing directory {directory_path}: {str(e)}")
            return total_symbols

    def suggest_imports(self, query: str, limit: int = 10, use_hybrid: bool = True) -> List[Dict[str, Any]]:
        """Suggest import statements for a query with improved error handling.

        Args:
            query: Query string
            limit: Maximum number of results
            use_hybrid: Whether to use hybrid search

        Returns:
            List of import suggestions
        """
        # Input validation
        if not query or not isinstance(query, str) or not query.strip():
            logger.warning("Empty or invalid query provided")
            return []

        if limit <= 0:
            logger.warning(f"Invalid limit {limit}, returning empty list")
            return []

        query = query.strip()

        try:
            from qdrant_client.http import models

            client = self._get_qdrant_client()

            # Generate query embedding
            query_vector = self.embedding_manager.get_embedding(query)

            if use_hybrid:
                try:
                    # Hybrid search (vector + text)
                    results = client.search(
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        query_filter=models.Filter(
                            should=[
                                models.FieldCondition(
                                    key="symbol_name",
                                    match=models.MatchText(text=query)
                                ),
                                models.FieldCondition(
                                    key="description",
                                    match=models.MatchText(text=query)
                                )
                            ]
                        ),
                        limit=limit
                    )
                except Exception as e:
                    logger.warning(f"Hybrid search failed: {str(e)}. Falling back to vector search.")
                    # Fall back to vector search only
                    results = client.search(
                        collection_name=self.collection_name,
                        query_vector=query_vector,
                        limit=limit
                    )
            else:
                # Vector search only
                results = client.search(
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    limit=limit
                )

            # Format results
            suggestions = []
            for result in results:
                suggestion = dict(result.payload)
                suggestion['score'] = result.score
                suggestions.append(suggestion)

            return suggestions

        except Exception as e:
            logger.error(f"Error suggesting imports: {str(e)}")
            return []

    def filter_suggestions_by_type(self, query: str, symbol_type: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Filter import suggestions by symbol type.

        Args:
            query: Query string
            symbol_type: Symbol type to filter by
            limit: Maximum number of results

        Returns:
            List of filtered import suggestions
        """
        # Input validation
        if not query or not isinstance(query, str) or not query.strip():
            logger.warning("Empty or invalid query provided")
            return []

        if not symbol_type or not isinstance(symbol_type, str) or not symbol_type.strip():
            logger.warning("Empty or invalid symbol_type provided")
            return []

        if limit <= 0:
            logger.warning(f"Invalid limit {limit}, returning empty list")
            return []

        query = query.strip()
        symbol_type = symbol_type.strip()

        try:
            from qdrant_client.http import models

            client = self._get_qdrant_client()

            # Generate query embedding
            query_vector = self.embedding_manager.get_embedding(query)

            try:
                # Search with type filter
                results = client.search(
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    query_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="symbol_type",
                                match=models.MatchValue(value=symbol_type)
                            )
                        ]
                    ),
                    limit=limit
                )
            except Exception as e:
                logger.warning(f"Type filter search failed: {str(e)}. Falling back to vector search.")
                # Fall back to vector search only
                results = client.search(
                    collection_name=self.collection_name,
                    query_vector=query_vector,
                    limit=limit
                )
                # Filter results manually
                results = [r for r in results if r.payload.get('symbol_type') == symbol_type][:limit]

            # Format results
            suggestions = []
            for result in results:
                suggestion = dict(result.payload)
                suggestion['score'] = result.score
                suggestions.append(suggestion)

            return suggestions

        except Exception as e:
            logger.error(f"Error filtering suggestions: {str(e)}")
            return []

    def search_nested_symbols(self, parent_symbol: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Search for nested symbols within a parent symbol.

        Args:
            parent_symbol: Parent symbol name
            limit: Maximum number of results

        Returns:
            List of nested symbols
        """
        # Input validation
        if not parent_symbol or not isinstance(parent_symbol, str) or not parent_symbol.strip():
            logger.warning("Empty or invalid parent_symbol provided")
            return []

        if limit <= 0:
            logger.warning(f"Invalid limit {limit}, returning empty list")
            return []

        parent_symbol = parent_symbol.strip()

        try:
            from qdrant_client.http import models

            client = self._get_qdrant_client()

            try:
                # Use scroll API for efficient filtering without dummy vectors
                scroll_result = client.scroll(
                    collection_name=self.collection_name,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="parent_symbol",
                                match=models.MatchValue(value=parent_symbol)
                            ),
                            models.FieldCondition(
                                key="is_nested",
                                match=models.MatchValue(value=True)
                            )
                        ]
                    ),
                    limit=limit
                )
                # Extract points and next page offset from scroll result
                results, _ = scroll_result  # Ignore next_page_offset for simple use case
                # Note: For pagination, you would use the second value to continue scrolling
            except Exception as e:
                logger.warning(f"Scroll search failed: {str(e)}. Using fallback search.")
                # Fallback to search with dummy vector (safe vector size)
                vector_size = self.vector_size if self.vector_size and self.vector_size > 0 else 1024
                dummy_vector = [0.0] * vector_size
                try:
                    results = client.search(
                        collection_name=self.collection_name,
                        query_vector=dummy_vector,
                        query_filter=models.Filter(
                            must=[
                                models.FieldCondition(
                                    key="parent_symbol",
                                    match=models.MatchValue(value=parent_symbol)
                                ),
                                models.FieldCondition(
                                    key="is_nested",
                                    match=models.MatchValue(value=True)
                                )
                            ]
                        ),
                        limit=limit
                    )
                except Exception as e2:
                    logger.warning(f"Fallback search also failed: {str(e2)}. Using manual filtering.")
                    # Last resort: get all and filter manually
                    results = client.search(
                        collection_name=self.collection_name,
                        query_vector=dummy_vector,
                        limit=limit * 10
                    )
                    # Filter manually
                    results = [r for r in results
                              if r.payload.get('parent_symbol') == parent_symbol
                              and r.payload.get('is_nested', False)][:limit]

            # Format results
            nested_symbols = []
            for result in results:
                symbol = dict(result.payload)
                nested_symbols.append(symbol)

            return nested_symbols

        except Exception as e:
            logger.error(f"Error searching nested symbols: {str(e)}")
            return []
