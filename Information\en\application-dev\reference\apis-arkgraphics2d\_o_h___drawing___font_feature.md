# OH_Drawing_FontFeature


## Overview

The OH_Drawing_FontFeature struct describes a font feature.

**Since**: 12

**Related module**: [Drawing](_drawing.md)


## Summary


### Member Variables

| Name| Description| 
| -------- | -------- |
| char \* [tag](#tag) | Tag of the font feature. | 
| int [value](#value) | Value of the font feature. | 


## Member Variable Description


### tag

```
char* OH_Drawing_FontFeature::tag
```
**Description**

Tag of the font feature.


### value

```
int OH_Drawing_FontFeature::value
```
**Description**

Value of the font feature.
