# particleAbility Switching


  | API in the FA Model| Corresponding .d.ts File in the Stage Model| Corresponding API in the Stage Model| 
| -------- | -------- | -------- |
| [startAbility(parameter: StartAbilityParameter, callback: AsyncCallback&lt;number&gt;): void;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitystartability)<br>[startAbility(parameter: StartAbilityParameter): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitystartability-1) | application\ServiceExtensionContext.d.ts | [startAbility(want: Want, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextstartability)<br>[startAbility(want: Want, options: StartOptions, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextstartability-2)<br>[startAbility(want: Want, options?: StartOptions): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextstartability-1)<br>[startServiceExtensionAbility(want: Want, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextstartserviceextensionability)<br>[startServiceExtensionAbility(want: Want): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextstartserviceextensionability-1) |
| [terminateSelf(callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilityterminateself)<br>[terminateSelf(): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilityterminateself-1) | application\ServiceExtensionContext.d.ts | [terminateSelf(callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextterminateself)<br>[terminateSelf(): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextterminateself-1) |
| [connectAbility(request: Want, options:ConnectOptions ): number;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilityconnectability) | application\ServiceExtensionContext.d.ts | [connectServiceExtensionAbility(want: Want, options: ConnectOptions): number;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextconnectserviceextensionability) |
| [disconnectAbility(connection: number, callback:AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitydisconnectability)<br>[disconnectAbility(connection: number): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitydisconnectability-1) | application\ServiceExtensionContext.d.ts | [disconnectServiceExtensionAbility(connection: number, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextdisconnectserviceextensionability)<br>[disconnectServiceExtensionAbility(connection: number): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-inner-application-serviceExtensionContext-sys.md#serviceextensioncontextdisconnectserviceextensionability-1) |
| [acquireDataAbilityHelper(uri: string): DataAbilityHelper;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilityacquiredataabilityhelper) | \@ohos.data.dataShare.d.ts<br>[\@ohos.data.fileAccess.d.ts | [createDataShareHelper(context: Context, uri: string, callback: AsyncCallback&lt;DataShareHelper&gt;): void;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#datasharecreatedatasharehelper)<br>[createDataShareHelper(context: Context, uri: string): Promise&lt;DataShareHelper&gt;;](../reference/apis-arkdata/js-apis-data-dataShare-sys.md#datasharecreatedatasharehelper-1)<br>[createFileAccessHelper(context: Context): FileAccessHelper;](../reference/apis-core-file-kit/js-apis-fileAccess-sys.md#fileaccesscreatefileaccesshelper-1)<br>[createFileAccessHelper(context: Context, wants: Array&lt;Want&gt;): FileAccessHelper;](../reference/apis-core-file-kit/js-apis-fileAccess-sys.md#fileaccesscreatefileaccesshelper) |
| [startBackgroundRunning(id: number, request: NotificationRequest, callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitystartbackgroundrunningdeprecated)<br>[startBackgroundRunning(id: number, request: NotificationRequest): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitystartbackgroundrunningdeprecated-1) | \@ohos.resourceschedule.backgroundTaskManager.d.ts | [startBackgroundRunning(context: Context, bgMode: BackgroundMode, wantAgent: WantAgent, callback: AsyncCallback): void;](../reference/apis-backgroundtasks-kit/js-apis-resourceschedule-backgroundTaskManager.md#backgroundtaskmanagerstartbackgroundrunning)<br>[startBackgroundRunning(context: Context, bgMode: BackgroundMode, wantAgent: WantAgent): Promise&lt;void&gt;;](../reference/apis-backgroundtasks-kit/js-apis-resourceschedule-backgroundTaskManager.md#backgroundtaskmanagerstartbackgroundrunning-1) |
| [cancelBackgroundRunning(callback: AsyncCallback&lt;void&gt;): void;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitycancelbackgroundrunningdeprecated)<br>[cancelBackgroundRunning(): Promise&lt;void&gt;;](../reference/apis-ability-kit/js-apis-ability-particleAbility.md#particleabilitycancelbackgroundrunningdeprecated-1) | \@ohos.resourceschedule.backgroundTaskManager.d.ts | [stopBackgroundRunning(context: Context, callback: AsyncCallback): void;](../reference/apis-backgroundtasks-kit/js-apis-resourceschedule-backgroundTaskManager.md#backgroundtaskmanagerstopbackgroundrunning)<br>[stopBackgroundRunning(context: Context): Promise&lt;void&gt;;](../reference/apis-backgroundtasks-kit/js-apis-resourceschedule-backgroundTaskManager.md#backgroundtaskmanagerstopbackgroundrunning-1) |
