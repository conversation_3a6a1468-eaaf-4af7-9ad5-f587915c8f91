# context_constant.h


## Overview

The **context_constant.h** file declares context-related enums.

**Library**: libability_runtime.so

**System capability**: SystemCapability.Ability.AbilityRuntime.Core

**Since**: 13

**Related module**: [AbilityRuntime](_ability_runtime.md)


## Summary

### Files

| Name                                         | Description                                                        |
| --------------------------------------------- | ------------------------------------------------------------ |
| [context_constant.h](context__constant_8h.md) | Declares context-related enums.<br>**File to include**: <AbilityKit/ability_runtime/context_constant.h><br>**Library**: libability_runtime.so|


### Enums

| Name                                                        | Description              |
| ------------------------------------------------------------ | ------------------ |
| [AbilityRuntime_AreaMode](_ability_runtime.md#abilityruntime_areamode) {<br>    ABILITY_RUNTIME_AREA_MODE_EL1 = 0,<br>    ABILITY_RUNTIME_AREA_MODE_EL2 = 1,<br>    ABILITY_RUNTIME_AREA_MODE_EL3 = 2,<br>    ABILITY_RUNTIME_AREA_MODE_EL4 = 3,<br>    ABILITY_RUNTIME_AREA_MODE_EL5 = 4<br>} | Enumerates the data encryption levels.|
